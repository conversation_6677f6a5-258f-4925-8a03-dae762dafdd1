package com.wtyt.lgfesentryedamanagement;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.qst.wtyt.trace.EnableTraceLgms;
import com.wtyt.kaa.datasource.DbConn;
import com.wtyt.kaa.datasource.DbPermission;
import com.wtyt.kaa.register.EnableKyddAutoConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

@SpringBootApplication
@ServletComponentScan
@EnableTraceLgms
@ComponentScan(basePackages={"com.wtyt"})
@EnableApolloConfig(value = {"application", "yanfa.fe.commons.yd.app"})
@EnableKyddAutoConfig(autoEureka = true,defaultDb = @DbConn(ns = "yanfa.datasource.mysql.lgfem", permission = DbPermission.RW, name = "lgfemDataSource"))
public class LgFesentryEdaManagementApplication {

	public static void main(String[] args) {
		SpringApplication.run(LgFesentryEdaManagementApplication.class, args);
	}

}
