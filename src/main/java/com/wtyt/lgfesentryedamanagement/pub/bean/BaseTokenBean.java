package com.wtyt.lgfesentryedamanagement.pub.bean;

import com.wtyt.lgfesentryedamanagement.pub.toolkits.ValidateFilter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/1/29 20:17
 * @vesion 1.0
 * @desc
 */
public class BaseTokenBean implements Serializable {
    private static final long serialVersionUID = -832482871699307944L;

    @NotEmpty(message = "操作人所属团队不能为空", groups = {ValidateFilter.Create.class, ValidateFilter.Update.class, ValidateFilter.Delete.class})
    private String optTeam;

    @NotEmpty(message = "操作人不能为空", groups = {ValidateFilter.Create.class, ValidateFilter.Update.class, ValidateFilter.Delete.class})
    private String optUserName;

    private String optUserId;

    public String getOptTeam() {
        return optTeam;
    }

    public void setOptTeam(String optTeam) {
        this.optTeam = optTeam;
    }

    public String getOptUserName() {
        return optUserName;
    }

    public void setOptUserName(String optUserName) {
        this.optUserName = optUserName;
    }

    public String getOptUserId() {
        return optUserId;
    }

    public void setOptUserId(String optUserId) {
        this.optUserId = optUserId;
    }
}
