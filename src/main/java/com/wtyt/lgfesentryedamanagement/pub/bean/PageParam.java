package com.wtyt.lgfesentryedamanagement.pub.bean;

import cn.hutool.core.util.ObjectUtil;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import javax.validation.groups.Default;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/1/30 13:38
 * @vesion 1.0
 * @desc
 */
public class PageParam implements Serializable {
    private static final long serialVersionUID = -4281911866640914100L;

    @Pattern(regexp = "^[0-9]*$", message = "当前页数只能是数字", groups = {Default.class})
    @Size(message = "当前页数过长", max = 5, groups = {Default.class})
    private String pageNumber;//页码

    @Pattern(regexp = "^[0-9]*$", message = "每页数量只能是数字", groups = {Default.class})
    @Size(message = "每页数量过长", max = 3, groups = {Default.class})
    private String pageSize;//分页大小


    public Integer pageNumber() {
        return Integer.parseInt(ObjectUtil.defaultIfBlank(this.getPageNumber(), "1"));
    }

    public void setPageNumber(String pageNumber) {
        this.pageNumber = pageNumber;
    }

    public Integer pageSize() {
        return Integer.parseInt(ObjectUtil.defaultIfBlank(this.getPageSize(), "10"));
    }

    public void setPageSize(String pageSize) {
        this.pageSize = pageSize;
    }

    public String getPageNumber() {
        return pageNumber;
    }

    public String getPageSize() {
        return pageSize;
    }
}
