package com.wtyt.lgfesentryedamanagement.pub.bean;

import com.github.pagehelper.PageHelper;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/30 13:37
 * @vesion 1.0
 * @desc
 */
public class PageVo<T> {
    private String total;
    private List<T> list;

    public String getTotal() {
        return total;
    }

    public void setTotal(String total) {
        this.total = total;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }

    public static <T> PageVo<T> buildEmpty() {
        try {
            PageVo<T> result = new PageVo<>();
            result.setTotal("0");
            result.setList(Collections.emptyList());
            return result;
        } finally {
            PageHelper.clearPage();
        }
    }

    public static <T> PageVo<T> builderResult(long total, List<T> vos) {
        try {
            PageVo<T> result = new PageVo<>();
            result.setTotal(String.valueOf(total));
            result.setList(vos);
            return result;
        } finally {
            PageHelper.clearPage();
        }
    }
}
