package com.wtyt.lgfesentryedamanagement.pub.interceptor;

import com.github.pagehelper.PageHelper;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.wtyt.lgfesentryedamanagement.pub.annotation.Attribute;
import com.wtyt.lgfesentryedamanagement.pub.apollo.ApolloConfigCenter;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.GsonIgnore;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.JavaBeanTookit;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

/**
 * 打印日志
 * 
 * <AUTHOR> -- 毕创
 *
 */
@Aspect
@Component
public class LogInterceptor {

    private static final Logger log = LoggerFactory.getLogger(LogInterceptor.class);
    private static final Gson gson = new GsonBuilder().setExclusionStrategies(new GsonIgnore()).create();
    private static final ThreadLocal<Stopwatch> STOP_WATCH_INFO = new ThreadLocal<>();
    @Autowired
    private ApolloConfigCenter apolloConfigCenter;
    @Pointcut("execution(public * com.wtyt..controller..*(..))")
    public void log(){}

    @AfterReturning(returning = "object", pointcut = "log()")
    public void after(JoinPoint joinPoint, Object object) {

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Attribute attribute = signature.getMethod().getAnnotation(Attribute.class);
        if (null != attribute && StringUtils.isNotBlank(attribute.sid())) {
            String ignoreListSids = apolloConfigCenter.application.getProperty("logInterceptor.ignoreList.sids");
            String objStr;
            if (StringUtils.isNotBlank(ignoreListSids)
                    && Lists.newArrayList(ignoreListSids.split(",")).contains(attribute.sid())) {
                objStr = gson.toJson(object);
            } else {
                objStr = JavaBeanTookit.ignoreListBeanToStr(object);
            }
            log.info("{}（{}-{}）返回参数：{} 耗时: {}ms", method.getName(), attribute.sid(),attribute.name(), objStr, STOP_WATCH_INFO.get().elapsed(TimeUnit.MILLISECONDS));
        } else {
            log.info("{}方法返回参数：{}", method.getName(), gson.toJson(object), STOP_WATCH_INFO.get().elapsed(TimeUnit.MILLISECONDS));
        }
    }

    @Before("log()")
    public void before(JoinPoint joinPoint) {
        if (STOP_WATCH_INFO.get() != null) {
            // 异常情况不走after
            STOP_WATCH_INFO.remove();
        }
        STOP_WATCH_INFO.set(Stopwatch.createStarted());
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Object object = null;
        if (null != joinPoint.getArgs() && joinPoint.getArgs().length > 0) {
            object = joinPoint.getArgs()[0];
        }
        Attribute attribute = signature.getMethod().getAnnotation(Attribute.class);
        if (null != attribute && StringUtils.isNotBlank(attribute.sid())) {
            log.info("{}（{}-{}）请求参数：{}", method.getName(), attribute.sid(),attribute.name(), gson.toJson(object));
        } else {
            log.info("{}方法请求参数：{}", method.getName(), gson.toJson(object));
        }
        // 执行前清理线程分页数据
        PageHelper.clearPage();
    }

}
