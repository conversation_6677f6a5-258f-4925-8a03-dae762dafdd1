package com.wtyt.lgfesentryedamanagement.pub.exceptions;

import com.wtyt.lg.commons.exception.UnifiedBusinessException;

import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @Date 2023/2/10 11:53
 */
public class ExceptionSupplier implements Supplier<UnifiedBusinessException> {
    private static final long serialVersionUID = 7398067417531797533L;

    private UnifiedBusinessException e;

    public ExceptionSupplier(String message) {
        e=new UnifiedBusinessException(message);
    }


    @Override
    public UnifiedBusinessException get() {
        return e;
    }
}
