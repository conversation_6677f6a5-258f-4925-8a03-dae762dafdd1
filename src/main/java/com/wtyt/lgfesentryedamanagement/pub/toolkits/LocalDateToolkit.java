package com.wtyt.lgfesentryedamanagement.pub.toolkits;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/7/27 14:56
 */
public class LocalDateToolkit {

    public static final DateTimeFormatter YYYY_MM= DateTimeFormatter.ofPattern("yyyy-MM");

    public static final DateTimeFormatter YYYY_MM_DD= DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public static final DateTimeFormatter YYYY_MM_DD_= DateTimeFormatter.ofPattern("yyyy/MM/dd");


    public static final DateTimeFormatter YYYY_MM_DD_TIME= DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static final DateTimeFormatter YYYY_MM_DD_HH_MM= DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

    public static final DateTimeFormatter YYYYMMDDHHMMSS= DateTimeFormatter.ofPattern("yyyyMMddHHmmss");



    public static String getMonthStart(){
        return LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()).format(YYYY_MM_DD);
    }
    public static String getMonthEnd(){
        return LocalDate.now().with(TemporalAdjusters.lastDayOfMonth()).format(YYYY_MM_DD);
    }

}
