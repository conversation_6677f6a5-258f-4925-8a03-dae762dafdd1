package com.wtyt.lgfesentryedamanagement.pub.toolkits.validation;



import com.wtyt.lgfesentryedamanagement.pub.toolkits.CommonCheckToolkit;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2023/5/8 19:16
 */
public class NumberValidator implements ConstraintValidator<Number,CharSequence> {


    private int maxIntLen;
    private int maxFloatLen;
    private String maxVal;
    private String minVal;

    @Override
    public void initialize(Number constraintAnnotation) {
        maxIntLen = constraintAnnotation.maxIntLen();
        maxFloatLen = constraintAnnotation.maxFloatLen();
        minVal = constraintAnnotation.min();
        maxVal = constraintAnnotation.max();
    }

    @Override
    public boolean isValid(CharSequence value, ConstraintValidatorContext context) {
        if ( value == null || value.length()==0) {
            return true;
        }
        boolean check = CommonCheckToolkit.checkNumber(maxIntLen, maxFloatLen, value);
        if(check && new BigDecimal(value.toString()).compareTo(new BigDecimal(minVal))<0){
            return false;
        }
        if(check && new BigDecimal(value.toString()).compareTo(new BigDecimal(maxVal))>0){
            return false;
        }
        return check;
    }
}
