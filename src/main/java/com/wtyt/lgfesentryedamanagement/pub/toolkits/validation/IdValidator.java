package com.wtyt.lgfesentryedamanagement.pub.toolkits.validation;



import com.wtyt.lgfesentryedamanagement.pub.toolkits.CommonCheckToolkit;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * <AUTHOR>
 * @since 2023/5/9 10:48
 */
public class IdValidator implements ConstraintValidator<Id,CharSequence> {

    private int maxLen;
    private boolean notBlack;

    @Override
    public void initialize(Id constraintAnnotation) {
        this.maxLen = constraintAnnotation.maxLen();
        this.notBlack = constraintAnnotation.notBlack();
    }

    @Override
    public boolean isValid(CharSequence value, ConstraintValidatorContext context) {
        return CommonCheckToolkit.checkId(value,maxLen,notBlack);
    }
}
