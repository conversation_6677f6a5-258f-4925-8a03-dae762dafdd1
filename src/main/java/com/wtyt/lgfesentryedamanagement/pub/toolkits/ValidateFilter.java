package com.wtyt.lgfesentryedamanagement.pub.toolkits;


import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.HashSet;
import java.util.Set;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/8/10 17:56
 */
public class ValidateFilter {

    private static final Logger log = LoggerFactory.getLogger(ValidateFilter.class);

    public static final String VALIDATE_ERROR = "validate error :{}";

    private static Validator sValidator;


    public interface Delete {
    }

    public interface Update {
    }

    public interface Create {
    }

    public interface Check{

    }


    public interface Query {

    }

    static {
        sValidator = Validation.buildDefaultValidatorFactory().getValidator();
    }

    /**
     * 校验数据 获取过滤值
     *
     * @param obj  数据
     * @param type 类型
     * @param <T>  泛型
     * @return 校验结果 抛出异常结果
     */
    public static <T> void getFilterMessage(T obj, Class type) {
        Set<ConstraintViolation<T>> validate = sValidator.validate(obj, type);
        Set<String> messageList = new HashSet<>();
        if (!validate.isEmpty()) {
            for (ConstraintViolation<T> content : validate) {
                log.info(VALIDATE_ERROR, content.getMessage());
                messageList.add(content.getMessage());
            }
            throw new UnifiedBusinessException("参数校验异常："+messageList);
        }
    }

}
