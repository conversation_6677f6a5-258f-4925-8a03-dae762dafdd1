package com.wtyt.lgfesentryedamanagement.pub.toolkits.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Repeatable;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * <AUTHOR>
 * @since 2022/6/29 17:16
 */
@Constraint(validatedBy = { RegularValidator.class})
@Target({ METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE })
@Retention(RUNTIME)
@Repeatable(Regular.List.class)
public @interface Regular {

    String message() default "参数不合法";

    Class<?>[] groups() default { };

    Class<? extends Payload>[] payload() default { };

    String type();

    @Target({ METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE })
    @Retention(RUNTIME)
    public @interface List {
        Regular[] value();
    }
}
