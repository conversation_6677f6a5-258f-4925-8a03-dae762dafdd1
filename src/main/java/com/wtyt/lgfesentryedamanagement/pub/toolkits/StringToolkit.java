package com.wtyt.lgfesentryedamanagement.pub.toolkits;


import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import javax.servlet.ServletException;
import java.text.SimpleDateFormat;
import java.util.Collection;
import java.util.Date;



/**
 * 关于字符串处理的类
 * <AUTHOR>
 *
 */
public class StringToolkit {

	
	private static final Logger log = LoggerFactory.getLogger(StringToolkit.class);
	
	/**
	 * 将整形的字符串转换成整形
	 * @param str
	 */
	@SuppressWarnings("serial")
	public static  Number getEmptyNum(final String str){
		if(StringUtils.isEmpty(str)){
			return 0;
		}
		Number num = 0;
		try{				
			num = new Number() {					
				@Override
                public long longValue() {					
					return Long.parseLong(str);
				}
				@Override
                public int intValue() {				
					return Integer.parseInt(str);
				}
				@Override
                public float floatValue() {					
					return Float.parseFloat(str);
				}
				@Override
                public double doubleValue() {
					return Double.parseDouble(str);
				}
			};			
		}catch(NumberFormatException e){			
			throw new RuntimeException(e);		
		}	
		return num;
	}  

	
	/**
	 * 通过json获取到value的值
	 * @param paramObj
	 * @param keyName
	 * @return
	 * @throws ServletException
	 */
	public static String getJsonObjectValue(JSONObject paramObj,String keyName) {
		String value = "";		
		try {
			value = paramObj.getString(keyName);
		} catch (JSONException e) {
            throw new UnifiedBusinessException("获取不到" + keyName + "的值");
		}	
		if(StringUtils.isEmpty(value)){
            throw new UnifiedBusinessException("获取不到" + keyName + "的值");
		}
		return value;
	}
	
	
	/**
	 * 通过json获取到value的值,可以是空值
	 * @param paramObj
	 * @param keyName
	 * @return
	 * @throws ServletException
	 */
	public static String getJsonObjectValueCanNull(JSONObject paramObj,String keyName) {
		String value = "";		
		try {
			value = paramObj.getString(keyName);
		} catch (Exception e) {
			log.info("获取不到"+keyName+"的值");
			value = "";
		}			
		return value;
	}
	
	
	
	
	/**
	 * 获取过滤字符串，前后空格和null
	 * @param value
	 * @return
	 */
	public static String getFliterNullStr(String value) {
		if (StringUtils.isEmpty(value)) {
			return "";
		}
		return value.trim();
	}
	
	/**
	 * 判断字符串是否是数字
	 * @param value
	 * @return
	 */
	public static boolean  isNumStr(String value){
		if (null==value) {
			return false;
		}
		return value.matches("(\\+|-)?\\d*");
	}
	
	/**
	
	
	/**
	 * 判断容器是否包含
	 * @return
	 */
    public static <T> boolean  isCollectionContains(T t,Collection<T> coll){
    	if(coll==null||coll.size()==0){
    		return false;
    	}
    	if(t==null||isEmptyStr(String.valueOf(t))){
    		return false;
    	}    	
    	return coll.contains(t);
    }
    
 
	  

   
	
	/**
	 * 判断字符串是否为空
	 * 
	 * @return
	 */
	public static boolean isEmptyStr(String str) {
		return null == str || "".equals(str.trim()) || "null".equals(str);
	}
	

    /**
     * 时间转换
     * @param timeStamp
     * @param pattern
     * @return
     */
	public static String conversionTime(String timeStamp,String pattern) {
		try {
			//yyyy-MM-dd HH:mm:ss 转换的时间格式  可以自定义
	        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
	        //转换 
	        String time = sdf.format(new Date(Long.parseLong(timeStamp)));
	        return time;
		}catch(Exception e) {	
			log.error("转换时间戳"+timeStamp+"到格式"+pattern+"发生了异常"+e.getMessage());
		}
 		return timeStamp;       
    }

      
	   
       /**
        * 将null类型的字符串转换成空字符串
        * @param value
        * @return
        */
       public static String getEmptyStr(String value){
    	   return null==value?"":value;
       }
       
         	
      	
      	 /**
       	 * 首字母大写
       	 * @param value
       	 * @return
       	 */
       	public static String firstToUpcase(String value){
       		char[] cs = value.toCharArray();
               cs[0] -= 32; //首字母小写到大写
               return String.valueOf(cs);
       	}
       	


}


