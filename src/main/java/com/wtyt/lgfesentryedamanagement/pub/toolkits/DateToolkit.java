package com.wtyt.lgfesentryedamanagement.pub.toolkits;

import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import com.wtyt.lgfesentryedamanagement.pub.exceptions.ExceptionSupplier;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.util.CollectionUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

/**
 * 日期工具类
 * 
 * <AUTHOR>
 * 
 */
public class DateToolkit {

    private static String g_date = "3000-01-01";

    /**
     * 日期格式：<br/>
     * yyyy-MM-dd HH:mm:ss<br/>
     * 比如： 2017-12-12 12:12:12
     */
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    /**
     * 日期格式：<br/>
     * yyyy-MM-dd HH:mm<br/>
     * 比如： 2017-12-12 12:12
     */
    public static final String YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";

    public static final DateTimeFormatter YYYY_MM_DD_HH_MM_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
    public static final DateTimeFormatter YYYY_MM_DD_TIME= DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    /**
     * 日期格式：<br/>
     * yyyy-MM-dd<br/>
     * 比如： 2017-12-12
     */
    public static final String YYYY_MM_DD = "yyyy-MM-dd";

    /**
     * 日期格式：<br/>
     * yyyy-MM<br/>
     * 比如： 2017-12
     */
    public static final String YYYY_MM = "yyyy-MM";
    
    /**
     * 日期格式：<br/>
     * yyyyMMdd<br/>
     * 比如： 20171212
     */
    public static final String YYYYMMDD = "yyyyMMdd";
    
	/**
	 * 日期格式：<br/>
	 * yyyy-MM-dd HH:mm:ss<br/>
	 * 比如： 20171212121212
	 */
	public static final String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    private DateToolkit() {
    }

    /**
     * 根据指定的pattern获取一个SimpleDateFormate实例<br/>
     * 如果pattern返回一个new SimpleDateFormate()<br/>
     * pattern一般使用DateToolkit中的常量，比如：<br/>
     * YYYY_MM_DD_HH_MM_SS YYYY_MM_DD
     * 
     * @param pattern
     * @return
     */
    public static SimpleDateFormat getSimpleDateFormat(String pattern) {
        if (StringUtils.isBlank(pattern)) {
            return new SimpleDateFormat();
        } else if (YYYY_MM_DD.equals(pattern)) {
            return new SimpleDateFormat(YYYY_MM_DD);
        } else if (YYYY_MM_DD_HH_MM_SS.equals(pattern)) {
            return new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
        } else if(YYYYMMDD.equals(pattern)){
            return new SimpleDateFormat(YYYYMMDD);
        }else{
            return new SimpleDateFormat(pattern);
        }

    }
    
    /**
     * 解析指定字符串为Date对象，如果传入空，返回null
     * @param source 日期字符串
     * @param pattern 日期格式
     * @return
     * @throws ParseException
     */
    public static Date parseDate(String source,String pattern) throws ParseException{
        if(StringUtils.isBlank(source)||StringUtils.isBlank(pattern)){
            return null;
        }
        return getSimpleDateFormat(pattern).parse(source);
    }
    
    public static String formate(Date date,String pattern){
        return getSimpleDateFormat(pattern).format(date);
    }
    
    public static String foramteStr(String source, int num, int fnum) throws ParseException {
        if (StringUtils.isBlank(source)) {
            return "";
        }
        Date date = getFormat(fnum).parse(source);
        DateFormat df = getFormat(num);
        return df.format(date);
    }

    /**
     * 格式指定的日期字符串，传入空返回""
     * @param date
     * @param pattern
     * @return
     * @throws ParseException
     */
    public static String formate(String date,String pattern) throws ParseException{
        if(StringUtils.isBlank(date)||StringUtils.isBlank(pattern)){
            return "";
        }
        return formate(parseDate(date,pattern),pattern);
    }

    /**
     * 获取指定格式的当前时间
     * @param dateformate 一般为DateToolkit的常量字段
     * @return
     */
    public static String getCurrentDatetime(String dateformate){
        return getSimpleDateFormat(dateformate).format(new Date());
    }

    /**
     * 日期字符串从 format 格式转换为 format2 格式的日期字符串
     * @param date
     * @param format
     * @param format2
     * @return
     * @throws Exception
     */
    public static String getFormatDate(String date,String format,String format2) throws Exception{
        return formate(parseDate(date,format),format2);
    }
    
    /**
     * 当前时间在(00:00:00,startTime)和(endTime,23:59:59)之间返回false,否则返回true
     * 当startTime为空时默认为1:30:00，endTime为空时默认为23:30:00
     * 当startTime和endTime都为null时，默认为(00:00:00,1:30:00)和(23:30:00,23:59:59)
     * startTime和endTime格式：HH:mm:ss
     * @param startTime
     * @param endTime 
     * @return
     */
    public static boolean isTimeZone(String startTime,String endTime){
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
            boolean rs = false;
            Calendar start = Calendar.getInstance();
            Calendar end = Calendar.getInstance();
            end.set(Calendar.HOUR_OF_DAY, 1);
            end.set(Calendar.MINUTE, 30);
            end.set(Calendar.SECOND, 0);
            String tmpStartTime = startTime;
            String tmpEndTime = endTime;
            if(StringUtils.isBlank(startTime)){
                tmpStartTime = "01:30:00";
            }
            if(StringUtils.isBlank(endTime)){
                tmpEndTime = "23:30:00";
            }
            start.setTime(sdf.parse(tmpStartTime));
            end.setTime(sdf.parse(tmpEndTime));
            if(start.after(end)){
                start.setTime(sdf.parse(tmpEndTime));
                end.setTime(sdf.parse(tmpStartTime));
            }
            start.set(Calendar.YEAR, Calendar.getInstance().get(Calendar.YEAR));
            start.set(Calendar.MONTH, Calendar.getInstance().get(Calendar.MONTH));
            end.set(Calendar.YEAR, Calendar.getInstance().get(Calendar.YEAR));
            end.set(Calendar.MONTH, Calendar.getInstance().get(Calendar.MONTH));
            Calendar first = Calendar.getInstance();
            first.set(Calendar.HOUR_OF_DAY, 0);
            first.set(Calendar.MINUTE, 0);
            first.set(Calendar.SECOND, 0);
            Calendar last = Calendar.getInstance();
            last.set(Calendar.HOUR_OF_DAY, 23);
            last.set(Calendar.MINUTE, 59);
            last.set(Calendar.SECOND, 59);
            Calendar now = Calendar.getInstance();
            first.set(Calendar.DATE, 5);
            start.set(Calendar.DATE, 5);
            end.set(Calendar.DATE, 5);
            last.set(Calendar.DATE, 5);
            now.set(Calendar.DATE, 5);
            if(now.after(first)&&now.before(start)){
                rs = false;
            }else if(now.before(last)&&now.after(end)){
                rs = false;
            }else{
                rs = true;
            }
            return rs;
        } catch (Exception e) {
            return false;
        }
    }

    private static SimpleDateFormat getFormat(int num) {
        SimpleDateFormat dateformat = null;
        switch (num) {
        case 0:
            dateformat = new SimpleDateFormat("HH:mm");
            break;
        case 1:
            dateformat = new SimpleDateFormat("HH:mm:ss");
            break;
        case 2:
            dateformat = new SimpleDateFormat("yyyy-MM-dd");
            break;
        case 3:
            dateformat = new SimpleDateFormat("yyyy-MM-dd HH");
            break;
        case 4:
            dateformat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            break;
        case 5:
            dateformat = new SimpleDateFormat("MM");
            break;
        case 6:
            dateformat = new SimpleDateFormat("yyyyMMdd");
            break;
        case 7:
            dateformat = new SimpleDateFormat("MM-dd");
            break;
        case 8:
            dateformat = new SimpleDateFormat("MM-dd-yyyy");
            break;
        case 9:
            dateformat = new SimpleDateFormat("dd-MM-yyyy");
            break;
        case 10:
            dateformat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            break;
        case 11:
            dateformat = new SimpleDateFormat("yyyy-MM");
            break;
        case 12:
            dateformat = new SimpleDateFormat("yyyy年MM月dd日");
            break;
        case 13:
            dateformat = new SimpleDateFormat("MM月dd日");
            break;
        default:
            dateformat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        }

        return dateformat;
    }

    public static String getCurrentDate(int num) {
        Date date = new Date();
        DateFormat df = getFormat(num);
        return df.format(date);
    }

    /**
     * 当前日期加上小时 2013-2-27
     * 
     * @param num
     *            日期格式类型
     * @param hour
     *            加上小时数
     * @return
     */
    public static String getCurrentDatePlusHour(int num, int hour) {
        Calendar cal = Calendar.getInstance();
        Date date = null;
        SimpleDateFormat format = getFormat(num);

        try {
            if (hour == -1) {
                date = format.parse(g_date);
                cal.setTime(date);
            } else {
                date = format.parse(getCurrentDate(num));
                cal.setTime(date);
                cal.add(Calendar.HOUR, hour);
            }
        } catch (ParseException e) {
            // 不作处理
        }
        return format.format(cal.getTime());
    }

    /**
     * 验证日期格式是否满足要求
     * 
     * @param str
     *            需要验证的日期格式
     * @param formatString
     *            验证的标准格式，如：（yyyy/MM/dd HH:mm:ss）
     * @return 返回验证结果
     */
    public static boolean isValidDate(String str, String formatString) {
        // 指定日期格式，注意yyyy/MM/dd区分大小写；
        SimpleDateFormat format = new SimpleDateFormat(formatString);
        try {
            // 设置lenient为false.
            // 否则SimpleDateFormat会比较宽松地验证日期，比如2007/02/29会被接受，并转换成2007/03/01
            format.setLenient(false);
            format.parse(str);
        } catch (ParseException e) {
            // e.printStackTrace();
            // 如果throw java.text.ParseException或者NullPointerException，就说明格式不对
            return false;
        }
        return true;
    }

    /**
     * 两个时间进行比对
     *
     * @return
     */
    public static boolean compareDate(String beginTime, String endTime, int num) {
        try {
            SimpleDateFormat dateformat = getFormat(num);
            Date sDate = dateformat.parse(beginTime);
            Date eDate = dateformat.parse(endTime);
            if (sDate.before(eDate)) {
                return true;
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**

    *字符串的日期格式的计算

    */

    public static int daysBetween(String bdate) throws ParseException {
        Date startDate = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String smdate = sdf.format(startDate);
        Calendar cal = Calendar.getInstance();
        cal.setTime(sdf.parse(smdate));
        long time1 = cal.getTimeInMillis();
        cal.setTime(sdf.parse(bdate));
        long time2 = cal.getTimeInMillis();
        long between_days = (time2 - time1) / (1000 * 3600 * 24);
        return Integer.parseInt(String.valueOf(between_days));
    }

    public static String beforeThreeDate(String expireDate, int interval) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar=Calendar.getInstance();   
        calendar.setTime(sdf.parse(expireDate));
        System.out.println(calendar.get(Calendar.DAY_OF_MONTH));// 今天的日期
        calendar.set(Calendar.DAY_OF_MONTH, calendar.get(Calendar.DAY_OF_MONTH) - interval);// 让日期加1
        return sdf.format(calendar.getTime());
    }

    /**
     * 生成随机时间
     *
     * @return
     */
    public static Date randomDate() {
        try {
            long currentTime = System.currentTimeMillis();
            currentTime += 30 * 60 * 1000;
            String beginDate = DateToolkit.formate(new Date(currentTime), DateToolkit.YYYY_MM_DD_HH_MM_SS);
            currentTime += 90 * 60 * 1000;
            String endDate = DateToolkit.formate(new Date(currentTime), DateToolkit.YYYY_MM_DD_HH_MM_SS);
            SimpleDateFormat format = new SimpleDateFormat(DateToolkit.YYYY_MM_DD_HH_MM_SS);
            Date start = format.parse(beginDate);// 构造开始日期
            Date end = format.parse(endDate);// 构造结束日期
            // getTime()表示返回自 1970 年 1 月 1 日 00:00:00 GMT 以来此 Date 对象表示的毫秒数。
            if (start.getTime() >= end.getTime()) {
                return null;
            }
            long date = random(start.getTime(), end.getTime());
            return new Date(date);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static long random(long begin, long end) {
        long rtn = begin + (long) (Math.random() * (end - begin));
        // 如果返回的是开始时间和结束时间，则递归调用本函数查找随机值
        if (rtn == begin || rtn == end) {
            return random(begin, end);
        }
        return rtn;
    }


    public static void checkStartTimeAndEndTime(String startTimeStr,String endTimeStr){
        Date startTime = null, endTime = null;
        if (!org.springframework.util.StringUtils.isEmpty(startTimeStr)) {
            try {
                startTime = DateUtils.parseDateStrictly(startTimeStr, DateToolkit.YYYY_MM_DD);
            } catch (ParseException e) {
                throw new UnifiedBusinessException("开始时间不合法");
            }
        }
        if (!org.springframework.util.StringUtils.isEmpty(endTimeStr)) {
            try {
                endTime = DateUtils.parseDateStrictly(endTimeStr, DateToolkit.YYYY_MM_DD);
            } catch (ParseException e) {
                throw new UnifiedBusinessException("结束时间不合法");
            }
        }
        if (startTime != null && endTime != null&&startTime.compareTo(endTime)>0) {
            throw new UnifiedBusinessException("开始时间不可大于结束时间");
        }

    }

    /**
     * 校验开始结束时间
     * @param endTimeStr
     * @param startTimeStr
     * @param checkStartTime
     */
    public static void checkZHLineDateRangeOneYear(String startTimeStr, String endTimeStr,boolean checkStartTime) {
        DateToolkit.checkStartTimeAndEndTime(startTimeStr, endTimeStr);
        LocalDate endTime = null;
        LocalDate startTime = null;
        if (StringUtils.isNotBlank(endTimeStr)) {
            endTime = LocalDate.parse(endTimeStr, LocalDateToolkit.YYYY_MM_DD);
            Assert.isTrue(endTime.compareTo(LocalDate.now()) >= 0
                    , new ExceptionSupplier("结束时间必须大于等于当天"));
        }
        if (StringUtils.isNotBlank(startTimeStr)) {
            startTime = LocalDate.parse(startTimeStr, LocalDateToolkit.YYYY_MM_DD);
            if (checkStartTime) {
                Assert.isTrue(startTime.compareTo(LocalDate.now()) >= 0
                        , new ExceptionSupplier("开始时间必须大于等于当天"));
            }
        }
        if (startTime != null && endTime != null) {
            Assert.isTrue(startTime.plusYears(1).compareTo(endTime.plusDays(1)) >= 0
                    , new ExceptionSupplier("时间跨度必须小于等于一年"));
        }
    }


    public static void checkStartTimeAndEndTime(String startTimeStr,String endTimeStr,String prefix){
        Date startTime = null, endTime = null;
        if (!org.springframework.util.StringUtils.isEmpty(startTimeStr)) {
            try {
                startTime = DateUtils.parseDateStrictly(startTimeStr, DateToolkit.YYYY_MM_DD);
            } catch (ParseException e) {
                throw new UnifiedBusinessException(prefix+"开始时间不合法");
            }
        }
        if (!org.springframework.util.StringUtils.isEmpty(endTimeStr)) {
            try {
                endTime = DateUtils.parseDateStrictly(endTimeStr, DateToolkit.YYYY_MM_DD);
            } catch (ParseException e) {
                throw new UnifiedBusinessException(prefix+"结束时间不合法");
            }
        }
        if (startTime != null && endTime != null&&startTime.compareTo(endTime)>0) {
            throw new UnifiedBusinessException(prefix+"开始时间不可大于结束时间");
        }
    }

    /**
     * 根据日期格式将字符串日期转换为日期
     *
     * @param format
     * @param dateString
     * @return
     */
    public static Date convertStringToDate(String format, String dateString) {
        if (StringUtils.isBlank(dateString)) {
            throw new UnifiedBusinessException("convertStringToDate出错，传入日期为空");
        }
        //yyyy-mm-dd 格式转换时，传入yyyy-m-d也不报错，提前校验长度
        if (YYYY_MM_DD.equals(format) && dateString.length() != 10) {
            throw new UnifiedBusinessException("convertStringToDate出错，传入日期格式错误");
        }
        //yyyy-mm 格式转换时，传入yyyy-m也不报错，提前校验长度
        if (YYYY_MM.equals(format) && dateString.length() != 7) {
            throw new UnifiedBusinessException("convertStringToDate出错，传入日期格式错误");
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        try {
            return sdf.parse(dateString);
        } catch (Exception e) {
            throw new UnifiedBusinessException("convertStringToDate出错，日期转换异常");
        }
    }

    /**
     * 指定日期格式 比较date1和date2的日期 date1 > date2 : return 1 date1 < date2 : return -1
     * date1 = date2 : return 0
     *
     * @param date1
     * @param date2
     * @param dateFormat
     * @return
     * @throws Exception
     */
    public static int compare(String date1, String date2, String dateFormat){
        SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
        Date ddate1;
        Date ddate2;
        try {
            ddate1 = sdf.parse(date1);
            ddate2 = sdf.parse(date2);
        } catch (Exception e) {
            throw new UnifiedBusinessException("compare出错，日期转换异常");
        }
        if (ddate1.after(ddate2)) {
            return 1;
        } else if (ddate1.before(ddate2)) {
            return -1;
        } else {
            return 0;
        }
    }


    /**
     * 理解22094接口的时间间隔字段
     * 加减默认单位是小时
     * [{"key":"107","value":"17:50"}]注：107代表每日
     * [{"key":"+","value":"4.0"}]
     * [{"key":"96","value":"17:50"}] ：96代表次日
     * [{"key":"97","value":"17:50"}] ：97代表第三天
     * [{"key":"98","value":"17:50"}] ：98代表第四天
     * [{"key":"1000","value":["1","17:00"]}]注：1000代表间隔天数类型。数组代表：间隔天数，几点，
     *
     * @param deadLineTime
     * @param key
     * @param value
     * @return
     */
    public static Date calcBoPushCfgTime(Date deadLineTime, String key, String value) throws ParseException {
        switch (key) {
            case "+":
                Double d = Double.parseDouble(value);
                //转成秒，去尾
                int addMilliSecond = (int) (d * 60 * 60);
                deadLineTime = DateUtils.addSeconds(deadLineTime, addMilliSecond);
                break;
            case "-":
                d = Double.parseDouble(value);
                //转成秒，去尾
                int delMilliSecond = (int) (d * 60 * 60);
                deadLineTime = DateUtils.addSeconds(deadLineTime, -delMilliSecond);
                break;
            case "107":
                //设置为指定的日期格式，当前配置只有时:分,所以统一按照时分处理
                String dateStr = formate(deadLineTime, YYYY_MM_DD);
                //替换掉小时yyyy-mm-dd hh:mm:ss
                String fullDateStr = dateStr + " " + value + ":00";
                deadLineTime = parseDate(fullDateStr, YYYY_MM_DD_HH_MM_SS);
                break;
            case "1000":
                JSONArray jsonArray = JSONUtil.parseArray(value);
                if(CollectionUtils.isEmpty(jsonArray)||jsonArray.size()<2){
                    return null;
                }
                Integer intervalDay = jsonArray.getInt(0);
                String intervalTime = jsonArray.getStr(1);
                if (intervalDay == null || StringUtils.isBlank(intervalTime)) {
                    return null;
                }
                LocalDateTime localDateTime = LocalDateTime.ofInstant(deadLineTime.toInstant(), ZoneId.systemDefault());
                Integer hour=Integer.valueOf(intervalTime.split(":")[0]);
                Integer minute=Integer.valueOf(intervalTime.split(":")[1]);
                LocalDateTime goalTime = localDateTime.plusDays(intervalDay).withHour(hour).withMinute(minute);
                deadLineTime = Date.from(goalTime.atZone(ZoneId.systemDefault()).toInstant());
                break;
            default:
                break;
        }
        return deadLineTime;
    }
}
