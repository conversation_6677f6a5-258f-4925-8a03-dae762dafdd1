package com.wtyt.lgfesentryedamanagement.pub.toolkits;


import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.wtyt.lgfesentryedamanagement.pub.consts.CodeConstants;
import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;


/**
 * 获取各种返回结果的信息
 * 
 * <AUTHOR>
 * @date 2018年3月2日 下午3:38:28
 */
public class ResultToolkit {

	private static final Logger log = LoggerFactory.getLogger(ResultToolkit.class);

	/**
	 * 获取返回结果
	 * 
	 * @return
	 * @throws JSONException
	 */
	public static String getResult(String req_code, String req_msg) {
		JSONObject resultJson = new JSONObject();		
		resultJson.put("reCode", req_code);
		resultJson.put("reInfo", req_msg);		
		String returnStr = resultJson.toString();
		log.debug("接口返回结果为：" + returnStr);
		return returnStr;
	}

	

	/**
	 * 获取返回结果
	 * 
	 * @return
	 * @throws JSONException
	 */
	private static JSONObject getJSONResult(String req_code, String req_msg) {
		JSONObject resultJson = new JSONObject();		
		resultJson.put("reCode", req_code);
		resultJson.put("reInfo", req_msg);		
		return resultJson;
	}
	
	
	/**
	 * 获取返回结果
	 * 
	 * @return
	 * @throws JSONException
	 */
	public static String getResult(String req_code, String req_msg,Object resultObj) {
		JSONObject resultJson = new JSONObject();		
		resultJson.put("reCode", req_code);
		resultJson.put("reInfo", req_msg);
		if(null!=resultObj) {
			resultJson.put("result", new JSONObject(JsonToolkit.objectToJsonNotNull(resultObj)));
		}
		return resultJson.toString();
	}

	/**
	 * 获取返回的成功结果
	 * 
	 * @return
	 */
	public static String getResult() {
		return getResult(CodeConstants.SUCCESS_CODE, CodeConstants.SUCCESS_MSG);
	}

	/**
	 * 获取返回的成功结果
	 * 
	 * @return
	 */
	public static String getResult(Object obj) {
		String dataJson = null;
		if (obj instanceof String) {
			dataJson = String.valueOf(obj);
		} else {
			dataJson = JsonToolkit.objectToJson(obj);
		}
		JSONObject returnJson = getJSONResult(CodeConstants.SUCCESS_CODE, CodeConstants.SUCCESS_MSG);
		returnJson.put("result", new JSONObject(dataJson));
		String returnStr = returnJson.toString();
		log.debug("接口返回结果为：" + returnStr);
		return returnStr;
	}

	/**
	 * 获取返回的自定义成功结果
	 * 
	 * @return
	 */
	public static String getResult(String dataStr, Object obj) {
		try {
			String dataJson = null;
			if (obj instanceof String) {
				dataJson = String.valueOf(obj);
			} else {
				dataJson = JsonToolkit.objectToJson(obj, PropertyNamingStrategy.SNAKE_CASE);
			}
			JSONObject returnJson = getJSONResult(CodeConstants.SUCCESS_CODE, CodeConstants.SUCCESS_MSG);
			if (StringUtils.isEmpty(dataStr)) {
				returnJson.put(dataStr, new JSONObject(dataJson));
			}
			return returnJson.toString();
		} catch (Exception e) {
            throw new UnifiedBusinessException(CodeConstants.SYS_ERROR_CODE, "json组件发生异常！！！", e);
		}

	}

}
