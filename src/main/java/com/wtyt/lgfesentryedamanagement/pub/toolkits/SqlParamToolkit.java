package com.wtyt.lgfesentryedamanagement.pub.toolkits;

import com.google.gson.Gson;
import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @createTime 2021/06/18 10:21:00
 */
public class SqlParamToolkit {

    public static <T> List<List<T>> splitList(List<T> list,int length) throws UnifiedBusinessException {
        if (length < 0){
            throw new UnifiedBusinessException("切分长度不能小于0");
        }
        List<List<T>> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)){
            return  resultList;
        }
        if (length == 0 || list.size() <= length){
            resultList.add(list);
            return resultList;
        }
        int startIndex = 0;
        while (startIndex < list.size()) {
            int endIndex;
            if (list.size() - length < startIndex) {
                endIndex = list.size();
            } else {
                endIndex = startIndex + length;
            }
            resultList.add(list.subList(startIndex, endIndex));
            startIndex = startIndex + length;
        }
        return resultList;
    }

    public static void main(String[] args) throws UnifiedBusinessException {
        List<String> list = new ArrayList<>();
        list.add("a");
        list.add("b");
        list.add("c");
        list.add("d");
        list.add("e");
        list.add("f");
        List<List<String>> lists = splitList(list,3);
        System.out.println(new Gson().toJson(lists));
    }
}
