package com.wtyt.lgfesentryedamanagement.pub.toolkits;


import com.wtyt.lg.commons.bean.BaseBean;
import com.wtyt.lg.commons.exception.UnifiedBusinessException;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/5/31 11:18
 */
public class CommonToolkit {

    public static <T extends Serializable> T checkReq(BaseBean<T> bean){
        if (bean == null) {
            throw new UnifiedBusinessException("请求未传递数据");
        }
        T data = bean.getData();
        if (data == null) {
            throw new UnifiedBusinessException("未传递请求参数");
        }
        return data;
    }
}
