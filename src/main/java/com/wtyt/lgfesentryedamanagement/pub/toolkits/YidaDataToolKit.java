package com.wtyt.lgfesentryedamanagement.pub.toolkits;

import com.aliyun.dingtalkyida_1_0.models.GetFieldDefByUuidResponseBody;
import com.wtyt.lgfesentryedamanagement.eda.bean.yida.InstanceValueDto;
import com.wtyt.lgfesentryedamanagement.pub.enums.YidaComponentEnum;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.translate.*;

import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * 宜搭数据处理工具类
 */
public class YidaDataToolKit {

    /**
     * json 及 sql 转义，只转义符号，不转义中文字符
     */
    public static final CharSequenceTranslator ESCAPE_JSON;
    static {
        final Map<CharSequence, CharSequence> escapeJsonMap = new HashMap<>();
        escapeJsonMap.put("\"", "\\\"");
        escapeJsonMap.put("\\", "\\\\");
        escapeJsonMap.put("/", "\\/");
        escapeJsonMap.put("'", "\\'");
        escapeJsonMap.put("%", "\\%");
        ESCAPE_JSON = new AggregateTranslator(
                new LookupTranslator(Collections.unmodifiableMap(escapeJsonMap)),
                new LookupTranslator(EntityArrays.JAVA_CTRL_CHARS_ESCAPE)
        );
    }

    /**
     * 从原始的title中取中文标题数据
     * @param sourceTitle
     * @return
     * @throws IOException
     */
    public static String fetchRealTitleZhCn(String sourceTitle) throws IOException {
        if (StringUtils.isEmpty(sourceTitle)) {
            return "";
        }
        if (!JsonToolkit.isJsonObject(sourceTitle)) {
            // 不是json字符串，则直接返回
            return sourceTitle;
        }
        return JsonToolkit.jsonToObject(sourceTitle, Map.class).get("zh_CN").toString();
    }

    /**
     * 从 title 中获取表单名称（页面名称），
     *  默认title值：{发起人}发起的{页面名称}
     * @param sourceTitle
     * @return
     * @throws IOException
     */
    public static String fetchFormNameByTitle(String sourceTitle) throws IOException {
        String titleText = fetchRealTitleZhCn(sourceTitle);
        if (titleText.contains("发起的")) {
            // 截取发起的之后的数据
            return titleText.substring(titleText.indexOf("发起的") + 3);
        }
        return "";
    }

    /**
     * 获取实例对象中的数据，转换为数据库中的数据；不同组件类型的数据格式不一样
     * @param instanceValueDto
     * @return
     */
    public static String fetch2DbValue(InstanceValueDto instanceValueDto) {
        if (instanceValueDto.ifDataEmpty()) {
            return "";
        }
        String componentName = instanceValueDto.getComponentName();
        YidaComponentEnum yidaComponentEnum = YidaComponentEnum.ofName(componentName);
        switch (yidaComponentEnum) {
            case EditorField:
                // 富文本组件，反转义html字符
                String htmlData = instanceValueDto.getFieldData().getValue().toString();
                return StringEscapeUtils.unescapeHtml(htmlData);
            default:
                return instanceValueDto.defaultFieldValue();
        }
    }

    /**
     * 根据宜搭组件名称获取在mysql中的字段类型
     * @param componentName
     * @return
     */
    public static String getComponentDbType(String componentName) {
        YidaComponentEnum yidaComponentEnum = YidaComponentEnum.ofName(componentName);
        // 不同类型的组件对应的字段类型不同
        switch (yidaComponentEnum) {
            case CheckboxField: // 复选框
            case MultiSelectField: // 下拉复选
            case EmployeeField: // 成员组件
            case DepartmentSelectField: // 部门组件
            case AssociationFormField: // 关联表单组件
            case AddressField: // 地址组件
            case TableField: // 子表单组件
            case AttachmentField: // 附件组件
            case ImageField: // 图片上传组件
                // 字段类型使用 json
                // associationFormField_lt8efgjn JSON NULL COMMENT '关联表单组件',
                return "JSON";
            case EditorField: //富文本组件
                // 富文本组件暂时使用 varchar 类型，值会有两种样式：钉钉富文本AS格式（json数据），标注html格式(字符串,html标签数据)
                return "VARCHAR(3000)";
            default:
                // TEXTFIELD_LT8EFGIL VARCHAR(512) NULL COMMENT '操作链路名称',
                return "VARCHAR(512)";
        }
    }

    /**
     * 获取组件对应的数据库备注
     * @param field
     * @return
     */
    public static String fetchComponentDbComment(GetFieldDefByUuidResponseBody.GetFieldDefByUuidResponseBodyResult field) {
        String labelName = getLabelName(field.getLabel());
        if ("HIDDEN".equals(field.getBehavior())) {
            labelName += "(隐藏组件)";
        }
        return labelName;
    }

    /**
     * 从 label 对象中获取 label 名称
     * @param label
     * @return
     */
    public static String getLabelName(Object label) {
        if (label instanceof  Map) {
            Map<String, String> map = (Map<String, String>) label;
            return map.getOrDefault("zh_CN", "");
        }
        return "";
    }

    /**
     * 通过formUuid 转换为数据库的表名， "-" 转换为下划线
     * @param formUuid
     * @return
     */
    public static String getTableNameByFormUuid(String formUuid) {
        return StringUtils.replace(formUuid, "-", "_");
    }

    /**
     * 数据库字符转转义， 主要是json类型中的数据转义，只转义符号，不转义中文字符
     * @param source
     * @return
     */
    public static String escapeDbStr(String source) {
        return ESCAPE_JSON.translate(source);
    }

}
