package com.wtyt.lgfesentryedamanagement.pub.toolkits;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonGenerationException;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.*;
import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.util.StringUtils;

import java.io.IOException;

public class JsonToolkit {
	
	private static  String NOT_JSON = "0";//不是jsonObject  也不是jsonArray
	private static  String JSON_ONLY_OBJ = "1";//是jsonObject
	private static  String JSON_ONLY_ARRAY = "2";//是jsonArray


	/**
	 * 将Object对象转换成JSON格式的字符串返回
	 * @throws JsonProcessingException 
	 * @throws IOException 
	 * @throws JsonMappingException 
	 * @throws JsonGenerationException 
	 */
	public static String objectToJson(Object obj)   {
		ObjectMapper objectMapper = new ObjectMapper();		
		try {
			return objectMapper.writeValueAsString(obj);
		} catch (JsonProcessingException e) {
			throw new RuntimeException(e);
		}
	}
	
	/**
	 * 将Object对象转换成JSON格式的字符串返回
	 * @throws IOException 
	 * @throws JsonMappingException 
	 * @throws JsonGenerationException 
	 */
	public static String objectToJson(Object obj,PropertyNamingStrategy strategy) throws Exception {
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.setPropertyNamingStrategy(strategy);
		return objectMapper.writeValueAsString(obj);
	}
	
		
	
	
	

	
	
	/**
	 * 获取json里面某个内容的值（可空）
	 * @param json
	 * @param fieldName
	 * @return
	 */
	public static String getJsonFieldValueCanNull(JSONObject json,String fieldName){
		String returnStr = "";
		if(json.has(fieldName)) {
			returnStr = String.valueOf(json.get(fieldName));
		}		
		return returnStr;
	}
	
	/**
	 * 获取json里面某个内容的值（不可空）
	 * @param json
	 * @param fieldName
	 * @return
	 * @throws JSONException 
	 */
	public static String getJsonFieldValue(JSONObject json,String fieldName) {
		String returnStr = getJsonFieldValueCanNull(json,fieldName);
		if(StringToolkit.isEmptyStr(returnStr)) {
            throw new UnifiedBusinessException("字段" + fieldName + "不允许为空！！！");
		}
		return returnStr;
	}

	/**
	 * 将Object对象转换成JSON格式的字符串返回
	 * @throws IOException 
	 * @throws JsonMappingException 
	 * @throws JsonGenerationException 
	 */
	public static String objectToJsonNotNull(Object obj)  {
		ObjectMapper mapper = new ObjectMapper();
		mapper.setSerializationInclusion(Include.NON_NULL);  
		try {
			return mapper.writeValueAsString(obj);
		} catch (JsonProcessingException e) {
            throw new UnifiedBusinessException("将" + obj + "转换成json字符串异常！！！");
		}
	}
	
	/**
	 * 将json转换成object
	 * @param jsonStr
	 * @param clazz
	 * @return
	 * @throws IOException 
	 * @throws JsonMappingException 
	 * @throws JsonParseException 
	 */
	public static <T> T  jsonToObject(String  jsonStr,Class<T> clazz) throws JsonParseException, JsonMappingException, IOException{
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		return objectMapper.readValue(jsonStr, clazz);
	}

	/**
	 *
	 * @param jsonStr
	 * @param javaType
	 * @return
	 * @param <T>
	 * @throws IOException
	 */
	public static <T> T  jsonToObject(String  jsonStr, JavaType javaType) throws IOException {
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		return objectMapper.readValue(jsonStr, javaType);
	}
	
	
	/**
	 * 判断当前的object的json类型
	 * @param object
	 * @return  0代表纯字符串  1代表json字符串  2代表jsonArray的字符串
	 */
	private static String  getJsonType(Object  object){
		 String objectStr = String.valueOf(object);
		 if(StringUtils.isEmpty(objectStr)){
			 return NOT_JSON;
		 }		 
		 final char[] strChar = objectStr.substring(0, 1).toCharArray();
		 final char firstChar = strChar[0];
		 if(firstChar == '{'){
			 return JSON_ONLY_OBJ;
		 }else if(firstChar == '['){
			 return JSON_ONLY_ARRAY;
		}
		 return NOT_JSON;
	}

	/**
	 * 判断内省是否为json
	 * @return
	 */
	public static boolean isJsonObject(String object) {		
		return JSON_ONLY_OBJ.equals(getJsonType(object));
	}

	/**
	 * 判断内省是否为json
	 * @return
	 */
	public static boolean isJsonArrayObject(String object) {
		return JSON_ONLY_ARRAY.equals(getJsonType(object));
	}

	/**
	 * 获取泛型的Collection Type
	 *
	 * @param collectionClass 泛型的Collection
	 * @param elementClasses  元素类
	 * @return JavaType Java类型
	 * @since 1.0
	 */
	public static JavaType getCollectionType(Class<?> collectionClass, Class<?>... elementClasses) {
		ObjectMapper objectMapper = new ObjectMapper();
		return objectMapper.getTypeFactory().constructParametricType(collectionClass, elementClasses);
	}
}
