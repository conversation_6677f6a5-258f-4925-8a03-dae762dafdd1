package com.wtyt.lgfesentryedamanagement.pub.toolkits;

import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.FatalBeanException;
import org.springframework.util.Assert;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/7/5 14:53
 */
public class JavaBeanTookit {
    private static final Logger log = LoggerFactory.getLogger(JavaBeanTookit.class);


    private static final Gson gson = new Gson();

    /**
     * 检查object是否为java的基本数据类型/包装类/java.util.Date/java.sql.Date
     *
     * @param propertyType
     * @return
     */
    public static boolean checkObjectIsSysType(Class<?> propertyType) {
        String objType = propertyType.toString();
        if ("byte".equals(objType) || "short".equals(objType) || "int".equals(objType) || "long".equals(objType)
                || "double".equals(objType) || "float".equals(objType) || "boolean".equals(objType)) {
            return true;
        } else if ("class java.lang.Byte".equals(objType) || "class java.lang.Short".equals(objType)
                || "class java.lang.Integer".equals(objType) || "class java.lang.Long".equals(objType)
                || "class java.lang.Double".equals(objType) || "class java.lang.Float".equals(objType)
                || "class java.lang.Boolean".equals(objType) || "class java.lang.String".equals(objType)) {
            return true;
        } else {
            return false;
        }
    }


    /**
     * 返回结果转换为字符串 根据配置规避List
     *
     * @param bean
     * @return
     * @throws Exception
     */
    public static String ignoreListBeanToStr(Object bean, int count) {
        try {
            //递归不能太深
            if (count > 3) {
                return gson.toJson(bean);
            }
            Class type = bean.getClass();
            if (checkObjectIsSysType(type) || Map.class.isAssignableFrom(type)
                    || type.isArray() || Collection.class.isAssignableFrom(type)) {
                return gson.toJson(bean);
            }
            Map<String, String> propertyMap = new HashMap<>();
            BeanInfo beanInfo = Introspector.getBeanInfo(type);
            PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
            for (int i = 0; i < propertyDescriptors.length; i++) {
                PropertyDescriptor descriptor = propertyDescriptors[i];
                String propertyName = descriptor.getName();
                if (!propertyName.equals("class")) {
                    //字段处理，list 判断是否需要规避，java自带类型不管，其他自定义类继续递归最多三次
                    Object invoke = descriptor.getReadMethod().invoke(bean);
                    if (invoke == null) {
                        continue;
                    }
                    Class<?> propertyType = invoke.getClass();
                    //java基础类含时间布尔包装类等
                    if (checkObjectIsSysType(propertyType)) {
                        propertyMap.put(propertyName, String.valueOf(invoke));
                        continue;
                    }
                    if (Map.class.isAssignableFrom(propertyType)) {
                        //map特殊处理
                        propertyMap.put(propertyName, gson.toJson(invoke));
                        continue;
                    }
                    //数组
                    if (propertyType.isArray()) {
                        //写一个替换副本
                        Object[] array = (Object[]) invoke;
                        propertyMap.put(propertyName + "Size", String.valueOf(array.length));
                        continue;
                    }
                    //列表
                    if (Collection.class.isAssignableFrom(propertyType)) {
                        //写一个替换副本
                        Collection collection = (Collection) invoke;
                        propertyMap.put(propertyName + "Size", String.valueOf(collection.size()));
                        continue;
                    }
                    //自定义其他类--递归
                    String beanStr = ignoreListBeanToStr(invoke, ++count);
                    propertyMap.put(propertyName, beanStr);
                }
            }
            return gson.toJson(propertyMap);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
        return null;
    }

    public static String ignoreListBeanToStr(Object bean) {
        String beanStr = ignoreListBeanToStr(bean, 0);
        if(StringUtils.isNotBlank(beanStr)){
            //去除转义
            return beanStr.replace("\\","");
        }
       return "";
    }


    /**
     * 功能 : 只复制source对象的非空属性到target对象上
     */
    public static void copySourceNoNullProperties(Object source, Object target) throws BeansException {
        Assert.notNull(source, "Source must not be null");
        Assert.notNull(target, "Target must not be null");
        Class<?> actualEditable = target.getClass();
        PropertyDescriptor[] targetPds = BeanUtils.getPropertyDescriptors(actualEditable);
        for (PropertyDescriptor targetPd : targetPds) {
            if (targetPd.getWriteMethod() != null) {
                PropertyDescriptor sourcePd = BeanUtils.getPropertyDescriptor(source.getClass(), targetPd.getName());
                if (sourcePd != null && sourcePd.getReadMethod() != null) {
                    try {
                        Method readMethod = sourcePd.getReadMethod();
                        if (!Modifier.isPublic(readMethod.getDeclaringClass().getModifiers())) {
                            readMethod.setAccessible(true);
                        }
                        Object value = readMethod.invoke(source);
                        // 这里判断以下value是否为空 当然这里也能进行一些特殊要求的处理 例如绑定时格式转换等等
                        if (value != null) {
                            Method writeMethod = targetPd.getWriteMethod();
                            if (!Modifier.isPublic(writeMethod.getDeclaringClass().getModifiers())) {
                                writeMethod.setAccessible(true);
                            }
                            writeMethod.invoke(target, value);
                        }
                    } catch (Throwable ex) {
                        throw new FatalBeanException("Could not copy properties from source to target", ex);
                    }
                }
            }
        }
    }

    /**
     * 功能 : 从source只复制target对象的为空属性
     */
    public static void copyTargetNullProperties(Object source, Object target) throws BeansException, InvocationTargetException, IllegalAccessException {
        Assert.notNull(source, "Source must not be null");
        Assert.notNull(target, "Target must not be null");
        Class<?> actualEditable = target.getClass();
        PropertyDescriptor[] targetPds = BeanUtils.getPropertyDescriptors(actualEditable);
        for (PropertyDescriptor targetPd : targetPds) {
            if (targetPd.getWriteMethod() != null) {
                //尝试获取target的属性值
                Method targetPdReadMethod = targetPd.getReadMethod();
                if (!Modifier.isPublic(targetPdReadMethod.getDeclaringClass().getModifiers())) {
                    targetPdReadMethod.setAccessible(true);
                }
                Object targetPdValue = targetPdReadMethod.invoke(source);
                //如果target属性值是空的，则尝试用source的数据填充
                if (null == targetPdValue) {
                    PropertyDescriptor sourcePd = BeanUtils.getPropertyDescriptor(source.getClass(), targetPd.getName());
                    if (sourcePd != null && sourcePd.getReadMethod() != null) {
                        try {
                            Method readMethod = sourcePd.getReadMethod();
                            if (!Modifier.isPublic(readMethod.getDeclaringClass().getModifiers())) {
                                readMethod.setAccessible(true);
                            }
                            Object value = readMethod.invoke(source);
                            // 这里判断以下value是否为空 当然这里也能进行一些特殊要求的处理 例如绑定时格式转换等等
                            if (value != null) {
                                Method writeMethod = targetPd.getWriteMethod();
                                if (!Modifier.isPublic(writeMethod.getDeclaringClass().getModifiers())) {
                                    writeMethod.setAccessible(true);
                                }
                                writeMethod.invoke(target, value);
                            }
                        } catch (Throwable ex) {
                            throw new FatalBeanException("Could not copy properties from source to target", ex);
                        }
                    }
                }
            }
        }
    }



}
