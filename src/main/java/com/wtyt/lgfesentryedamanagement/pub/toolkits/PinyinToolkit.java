package com.wtyt.lgfesentryedamanagement.pub.toolkits;


import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 中文拼音
 */
public class PinyinToolkit {

    private static final Logger log = LoggerFactory.getLogger(PinyinToolkit.class);


    /**
     * 转换中文字符串为拼音首字母
     * @param text
     * @return
     */
    public static String converterToFirstSpellUpperCase(String text) {
        if (StringUtils.isBlank(text)) {
            return null;
        }
        StringBuffer sb = new StringBuffer();
        for (int i=0 ; i<text.length() ; i++) {
            for(int y=0;y<=0;y++){
                sb.append(converterToSpell(String.valueOf(text.charAt(i))).toUpperCase().charAt(0));
            }
        }
        return sb.toString();
    }

    /**
     * 获取中文拼音
     * @param chines
     * @return
     */
    public static String converterToSpell(String chines) {
        String pinyinName = "";
        char[] nameChar = chines.toCharArray();
        HanyuPinyinOutputFormat defaultFormat = new HanyuPinyinOutputFormat();
        defaultFormat.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        defaultFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        for (int i = 0; i < nameChar.length; i++) {
            String s = String.valueOf(nameChar[i]);
            if (s.matches("[\\u4e00-\\u9fa5]")) {
                try {
                    String[] mPinyinArray = PinyinHelper.toHanyuPinyinStringArray(nameChar[i], defaultFormat);
                    pinyinName += mPinyinArray[0];
                } catch (BadHanyuPinyinOutputFormatCombination e) {
                    e.printStackTrace();
                }
            } else {
                pinyinName += nameChar[i];
            }
        }
        return pinyinName;
    }
}
