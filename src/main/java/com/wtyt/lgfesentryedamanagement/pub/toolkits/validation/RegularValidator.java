package com.wtyt.lgfesentryedamanagement.pub.toolkits.validation;




import com.wtyt.lgfesentryedamanagement.pub.toolkits.CommonCheckToolkit;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * <AUTHOR>
 * @since 2022/6/29 17:10
 */
public class RegularValidator implements ConstraintValidator<Regular,CharSequence> {


    private String type;


    @Override
    public void initialize(Regular constraintAnnotation) {
        type = constraintAnnotation.type();

    }

    @Override
    public boolean isValid(CharSequence value, ConstraintValidatorContext context) {
        if ( value == null || value.length()==0) {
            return true;
        }
        return CommonCheckToolkit.checkRegular(type,value);
    }

}
