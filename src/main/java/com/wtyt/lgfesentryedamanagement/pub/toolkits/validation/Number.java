package com.wtyt.lgfesentryedamanagement.pub.toolkits.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Repeatable;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 校验字符传是否为合法数字
 * <AUTHOR>
 * @since 2023/5/8 19:17
 */
@Constraint(validatedBy = { NumberValidator.class})
@Target({ METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE })
@Retention(RUNTIME)
@Repeatable(Number.List.class)
public @interface Number {

    String message() default "数字不合法";

    int maxFloatLen() default 0;

    int maxIntLen() default 12;

    String min() default "0";
    String max() default "9223372036854775807";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default { };

    @Target({ METHOD, FIELD, ANNOTATION_TYPE, CONSTRUCTOR, PARAMETER, TYPE_USE })
    @Retention(RUNTIME)
    public @interface List {
        Number[] value();
    }


}
