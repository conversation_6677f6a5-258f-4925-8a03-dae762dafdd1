package com.wtyt.lgfesentryedamanagement.pub.toolkits;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.wtyt.lg.commons.exception.BaseTipException;
import com.wtyt.lg.commons.toolkits.StringToolkit;
import com.wtyt.lgfesentryedamanagement.pub.consts.PatternConsts;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @since 2022/6/30 16:52
 */
public class CommonCheckToolkit {


    private static final String PREFIX_KEY="basic.data.regular";

    private static final String REGULAR_NAMESPACE="yanfa.public.basic.data.regular";

    public static void checkUserId(String userId,boolean notBlack) throws BaseTipException {
        if(notBlack){
            StringToolkit.notBlank("用户ID不能为空",userId);
        }
        StringToolkit.isOnlyNumber("用户ID不合法",userId);
        StringToolkit.lengthCheck("用户ID长度不合法",userId,10);
    }

    public static void checkIdCard(String idCard,boolean notBlack) throws BaseTipException {
        if(notBlack){
            StringToolkit.notBlank("身份证号不能为空",idCard);
        }
        if(!checkRegular(PatternConsts.REGULAR_ID_CARD,idCard)){
            throw new BaseTipException("身份证号不合法");
        }
    }

    public static void checkCartBadgeNo(String badgeNo,boolean notBlack) throws BaseTipException {
        if(notBlack){
            StringToolkit.notBlank("车牌号不能为空",badgeNo);
        }
        if(!checkRegular(PatternConsts.REGULAR_CAR_NO,badgeNo)){
            throw new BaseTipException("车牌号不合法");
        }
    }

    public static void checkMobileNo(String mobileNo,boolean notBlack) throws BaseTipException {
        if(notBlack){
            StringToolkit.notBlank("手机号为空",mobileNo);
        }
        if(!checkRegular(PatternConsts.REGULAR_MOBILE_NO_ALL,mobileNo)){
            throw new BaseTipException("手机号不合法");
        }
    }
    public static void checkMobileNo(String mobileNo,boolean notBlack,String msg) throws BaseTipException {
        if(notBlack){
            StringToolkit.notBlank(msg,mobileNo);
        }
        if(!checkRegular(PatternConsts.REGULAR_MOBILE_NO_ALL,mobileNo)){
            throw new BaseTipException(msg);
        }
    }



    public static boolean checkRegular(String type,CharSequence content){
        if(StringUtils.isBlank(content)){
            return true;
        }
        Config config = ConfigService.getConfig(REGULAR_NAMESPACE);
        String regex = config.getProperty(PREFIX_KEY+"."+type, "");
        if(StringUtils.isBlank(regex)){
            return true;
        }
        Pattern compile = Pattern.compile(regex);
        Matcher matcher = compile.matcher(content);
        return matcher.matches();
    }

    public static boolean checkNumber(int maxIntLen, int maxFloatLen, CharSequence value) {
        if (StringUtils.isBlank(value)) {
            return true;
        }
        if(maxIntLen<1 || maxFloatLen<0){
            return true;
        }
        String pattern = "^\\d{1,"+maxIntLen+"}";
        if(maxFloatLen>0){
            pattern += "(\\.\\d{1,"+maxFloatLen+"})?";
        }
        pattern +="$";
        return Pattern.matches(pattern, value);
    }

    public static boolean checkId(CharSequence id,int maxLen,boolean notBlack){
        return !notBlack || StringUtils.isNotBlank(id) && StringUtils.isNumeric(id) && id.length() <= maxLen;
    }

}
