package com.wtyt.lgfesentryedamanagement.pub.toolkits;


import com.wtyt.generator.toolkit.UidToolkit;
import com.wtyt.lg.bury.point.collector.BuryPointCollector;
import com.wtyt.lgfesentryedamanagement.dao.bean.*;
import com.wtyt.lgfesentryedamanagement.pub.bean.BaseTokenBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * brd埋点
 */
public class BuryPointToolkit {

    private static final Logger log = LoggerFactory.getLogger(BuryPointToolkit.class);

    /**
     * T_BRD_EDA_SYNC_LOG
     * @param syncType
     * @param syncEnv
     * @param edaMasterList
     */
    public static List<Map<String, String>> transEdaMasterSyncLog(BaseTokenBean baseTokenBean, String syncType, String syncEnv, List<TBurypointsEdaMaster> edaMasterList) {
        try {
            //保存工会三方回调日志
            List<Map<String, String>> mainTableRows = new ArrayList<Map<String, String>>();
            for (TBurypointsEdaMaster tBurypointsEdaMaster : edaMasterList) {
                Map<String, String> row = new HashMap<String, String>();
                row.put("BRD_EDA_SYNC_LOG_ID", UidToolkit.generateUidString());
                row.put("SYNC_TYPE", syncType);
                row.put("SYNC_ENV", syncEnv);
                row.put("SYNC_ID", String.valueOf(tBurypointsEdaMaster.getBurypointsEdaMasterId()));
                row.put("SYNC_DATA_JSON", JsonToolkit.objectToJson(tBurypointsEdaMaster));
                row.put("OPT_TEAM", baseTokenBean.getOptTeam());
                row.put("OPT_USER_NAME", baseTokenBean.getOptUserName());
                row.put("IS_DEL", "0");
                row.put("NOTE", "");
                row.put("CREATED_TIME", DateToolkit.getCurrentDate(4));
                row.put("LAST_MODIFIED_TIME", DateToolkit.getCurrentDate(4));
                mainTableRows.add(row);
            }
            return mainTableRows;
        } catch (Throwable e) {
            log.error("保存同步EDA数据日志出现异常", e);
        }
        return null;
    }

    /**
     * T_BRD_EDA_SYNC_LOG
     * @param syncType
     * @param syncEnv
     * @param edaRelList
     */
    public static List<Map<String, String>> transEdaFeatureSyncLog(BaseTokenBean baseTokenBean, String syncType, String syncEnv, List<TBurypointsEdaRel> edaRelList) {
        try {
            //保存工会三方回调日志
            List<Map<String, String>> mainTableRows = new ArrayList<Map<String, String>>();
            for (TBurypointsEdaRel tBurypointsEdaRel : edaRelList) {
                Map<String, String> row = new HashMap<String, String>();
                row.put("BRD_EDA_SYNC_LOG_ID", UidToolkit.generateUidString());
                row.put("SYNC_TYPE", syncType);
                row.put("SYNC_ENV", syncEnv);
                row.put("SYNC_ID", String.valueOf(tBurypointsEdaRel.getBurypointsEdaRelId()));
                row.put("SYNC_DATA_JSON", JsonToolkit.objectToJson(tBurypointsEdaRel));
                row.put("OPT_TEAM", baseTokenBean.getOptTeam());
                row.put("OPT_USER_NAME", baseTokenBean.getOptUserName());
                row.put("IS_DEL", "0");
                row.put("NOTE", "");
                row.put("CREATED_TIME", DateToolkit.getCurrentDate(4));
                row.put("LAST_MODIFIED_TIME", DateToolkit.getCurrentDate(4));
                mainTableRows.add(row);
            }
            return mainTableRows;
        } catch (Throwable e) {
            log.error("保存同步EDA数据日志出现异常", e);
        }
        return null;
    }

    /**
     * T_BRD_EDA_SYNC_LOG
     * @param syncType
     * @param syncEnv
     * @param edaBranchList
     */
    public static List<Map<String, String>> transEdaBranchSyncLog(BaseTokenBean baseTokenBean, String syncType, String syncEnv, List<TBurypointsEda> edaBranchList) {
        try {
            //保存工会三方回调日志
            List<Map<String, String>> mainTableRows = new ArrayList<Map<String, String>>();
            for (TBurypointsEda tBurypointsEda : edaBranchList) {
                Map<String, String> row = new HashMap<String, String>();
                row.put("BRD_EDA_SYNC_LOG_ID", UidToolkit.generateUidString());
                row.put("SYNC_TYPE", syncType);
                row.put("SYNC_ENV", syncEnv);
                row.put("SYNC_ID", String.valueOf(tBurypointsEda.getBurypointsEdaId()));
                row.put("SYNC_DATA_JSON", JsonToolkit.objectToJson(tBurypointsEda));
                row.put("OPT_TEAM", baseTokenBean.getOptTeam());
                row.put("OPT_USER_NAME", baseTokenBean.getOptUserName());
                row.put("IS_DEL", "0");
                row.put("NOTE", "");
                row.put("CREATED_TIME", DateToolkit.getCurrentDate(4));
                row.put("LAST_MODIFIED_TIME", DateToolkit.getCurrentDate(4));
                mainTableRows.add(row);
            }
            return mainTableRows;
        } catch (Throwable e) {
            log.error("组装同步EDA数据日志出现异常", e);
        }
        return null;
    }

    /**
     * T_BRD_EDA_SYNC_LOG
     * @param syncType
     * @param syncEnv
     * @param edaBkConfigList
     */
    public static List<Map<String, String>> transEdaBkConfigSyncLog(BaseTokenBean baseTokenBean, String syncType, String syncEnv, List<TBurypointsEdaBkConfig> edaBkConfigList) {
        try {
            //保存工会三方回调日志
            List<Map<String, String>> mainTableRows = new ArrayList<Map<String, String>>();
            for (TBurypointsEdaBkConfig tBurypointsEdaBkConfig : edaBkConfigList) {
                Map<String, String> row = new HashMap<String, String>();
                row.put("BRD_EDA_SYNC_LOG_ID", UidToolkit.generateUidString());
                row.put("SYNC_TYPE", syncType);
                row.put("SYNC_ENV", syncEnv);
                row.put("SYNC_ID", String.valueOf(tBurypointsEdaBkConfig.getBurypointsEdaBkConfigId()));
                row.put("SYNC_DATA_JSON", JsonToolkit.objectToJson(tBurypointsEdaBkConfig));
                row.put("OPT_TEAM", baseTokenBean.getOptTeam());
                row.put("OPT_USER_NAME", baseTokenBean.getOptUserName());
                row.put("IS_DEL", "0");
                row.put("NOTE", "");
                row.put("CREATED_TIME", DateToolkit.getCurrentDate(4));
                row.put("LAST_MODIFIED_TIME", DateToolkit.getCurrentDate(4));
                mainTableRows.add(row);
            }
            return mainTableRows;
        } catch (Throwable e) {
            log.error("组装同步EDA数据日志出现异常", e);
        }
        return null;
    }

    /**
     * 推送日志记录
     * @param mainTableRows
     */
    public static void saveEdaSyncLog(List<Map<String, String>> mainTableRows) {
        try {
            BuryPointCollector.getInstance().buryPointToDataphin("T_BRD_EDA_SYNC_LOG", mainTableRows);
        } catch (Throwable e) {
            log.error("保存同步EDA数据日志出现异常", e);
        }
    }

}
