package com.wtyt.lgfesentryedamanagement.pub.toolkits;

import com.google.gson.ExclusionStrategy;
import com.google.gson.FieldAttributes;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @Date 2022/7/25 16:21
 */
public class GsonIgnore implements ExclusionStrategy {
    @Override
    public boolean shouldSkipField(FieldAttributes f) {
        if (f.getAnnotation(IgnoreField.class) != null) {
            // 包含這個注解的，直接忽略
            return true;
        }
        return false;
    }

    @Override
    public boolean shouldSkipClass(Class<?> clazz) {
        if (clazz.isAnnotationPresent(IgnoreField.class)) {
            return true;
        }
        return false;
    }

    @Target({ElementType.ANNOTATION_TYPE, ElementType.FIELD, ElementType.TYPE})
    @Retention(RetentionPolicy.RUNTIME)
    @Documented
    public @interface IgnoreField {
    }
}
