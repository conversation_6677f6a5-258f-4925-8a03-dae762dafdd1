package com.wtyt.lgfesentryedamanagement.pub.advice;


import com.wtyt.lg.commons.bean.ResDataBean;
import com.wtyt.lg.commons.exception.BaseConfirmException;
import com.wtyt.lg.commons.exception.BaseCustomException;
import com.wtyt.lg.commons.exception.BaseTipException;
import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import com.wtyt.lg.commons.toolkits.AlarmToolkit;
import com.wtyt.lgfesentryedamanagement.pub.consts.CodeConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.converter.HttpMessageConversionException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
/**
 * 统一异常处理
 * 
 * <AUTHOR>
 *
 */
@ControllerAdvice
public class ControllerExceptionAdvice {

    private static final Logger logger = LoggerFactory.getLogger(ControllerExceptionAdvice.class);

    @ResponseBody
    @ExceptionHandler(value = BaseTipException.class)
    public ResDataBean<String> baseTipException(BaseTipException ex) {
        logger.info(ex.getMessage());
        ResDataBean<String> res = new ResDataBean<>();
        res.tipFail(ex.getMessage(), null);
        return res;
    }

    @ResponseBody
    @ExceptionHandler(value = BaseConfirmException.class)
    public ResDataBean<String> baseConfirmException(BaseConfirmException ex) {
        logger.info(ex.getMessage());
        ResDataBean<String> res = new ResDataBean<>();
        res.confirmFail(ex.getMessage(), null);
        return res;
    }

    @ResponseBody
    @ExceptionHandler(value = BaseCustomException.class)
    public ResDataBean<Object> baseCustomException(BaseCustomException ex) {
        logger.error(ex.getMessage(), ex);
        ResDataBean<Object> res = new ResDataBean<>();
        res.setReCode(ex.getReCode());
        res.setReInfo(ex.getReInfo());
        res.setResult(ex.getData());
        return res;
    }

    @ResponseBody
    @ExceptionHandler(value = Exception.class)
    public ResDataBean<String> baseConfirmException(Exception ex) {
        logger.error(ex.getMessage(), ex);
        AlarmToolkit.logAlarm(ex.getMessage(), ex, "");
        ResDataBean<String> res = new ResDataBean<>();
        res.tipFail(CodeConstants.SYS_ERROR_MSG, null);
        return res;
    }



    /**
     * 参数绑定异常
     * @param ex
     * @return
     */
    @ResponseBody
    @ExceptionHandler(value = HttpMessageConversionException.class)
    public ResDataBean<String> httpMessageConversionExceptionHandler(HttpMessageConversionException ex) {
        logger.error("参数绑定异常！！！"+ex.getMessage(),ex);
        AlarmToolkit.logAlarm(ex.getMessage(), ex, CodeConstants.SYS_ILLEGAL_MSG);
        ResDataBean<String> res = new ResDataBean<>();
        res.tipFail(CodeConstants.SYS_ILLEGAL_MSG, null);
        return res;
    }

    /**
     * 访问方式异常
     * @param ex
     * @return
     */
    @ResponseBody
    @ExceptionHandler(value = HttpRequestMethodNotSupportedException.class)
    public ResDataBean<String> httpRequestMethodNotSupportedExceptionHandler(HttpRequestMethodNotSupportedException ex) {
        logger.error("访问方式异常！！！"+ex.getMessage(),ex);
        AlarmToolkit.logAlarm(ex.getMessage(), ex, CodeConstants.SYS_ERROR_MODE_MSG);
        ResDataBean<String> res = new ResDataBean<>();
        res.tipFail(CodeConstants.SYS_ERROR_MODE_MSG, null);
        return res;
    }

    /**
     * 拦截捕捉自定义异常 UnifiedBusinessException.class
     * @param ex
     * @return
     */
    @ResponseBody
    @ExceptionHandler(value = UnifiedBusinessException.class)
    public ResDataBean<String> unifiedBusinessExceptionHandler(UnifiedBusinessException ex) {
        logger.warn("发生异常了！！！" + ex.getMessage());
        if (ex.isAlarm()) {
            AlarmToolkit.logAlarm(ex.getMessage(), ex, "");
        }
        ResDataBean<String> res = new ResDataBean<>();
        res.tipFail(ex.getMessage(), null);
        return res;
    }


}
