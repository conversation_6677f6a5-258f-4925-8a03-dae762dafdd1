package com.wtyt.lgfesentryedamanagement.pub.rpc.bean.request;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class Luge8000535Request {

    private String operatorId;//操作者id
    private String taskId;//任务Id
    private String customfieldId;//自定义字段ID
    private String customfieldName;//自定义字段名称(如果提供自定义字段ID 则忽略)
    private String customfieldInstanceId;//自定义字段InstanceId(如果提供自定义字段ID 或者 自定义字段名称 则忽略)
    private String disableActivity;//是否忽略触发动态

    /**
     * 字段值集合
     */
    private List<ValueDto> value;

    public Luge8000535Request addValue(String title) {
        if (value == null) {
            value = new ArrayList<>();
        }
        ValueDto valueDto = new ValueDto();
        valueDto.setTitle(title);
        value.add(valueDto);
        return this;
    }

    @Data
    public static class ValueDto {
        private String id; // 字段值id
        private String title; // 字段值渲染值
        private String thumbUrl; // 用户头像
    }

}
