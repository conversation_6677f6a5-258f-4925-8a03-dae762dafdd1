package com.wtyt.lgfesentryedamanagement.pub.rpc;

import com.wtyt.lg.commons.consts.ReCodeConsts;
import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import com.wtyt.qst.lgms.bean.LgmsPostBean;
import com.wtyt.qst.lgms.bean.LgmsPostBuilder;
import com.wtyt.qst.lgms.util.LgmsUtil;
import com.wtyt.qst.util.bean.LgmsNewResult;
import com.wtyt.qst.util.bean.LgmsResult;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

/**
 * 社区网关调用封装服务
 */
@Component
public class RpcSqService {

    /**
     * 请求卡友地带网关接口，格式为req_code、req_msg
     * @param serviceId
     * @param reqData
     * @param <I>
     * @param <O>
     * @return
     * @throws Exception
     */
    public <I,O> LgmsResult<O> postBaseMicLrOld(String serviceId, I reqData, Class<O> outClass) throws Exception {
        LgmsPostBean postBean = LgmsPostBuilder.builder(serviceId, reqData).addUniqueId().build();
        return LgmsUtil.postLgmsResult(outClass, postBean);
    }

    public <I, O> O postMicLrOld(String serviceId, I reqData, Class<O> outClass) throws Exception {
        LgmsResult<O> objectLgmsResult = this.postBaseMicLrOld(serviceId, reqData, outClass);
        if (StringUtils.equals("200", objectLgmsResult.getReq_code())) {
            return objectLgmsResult.getData();
        }
        throw new UnifiedBusinessException(ReCodeConsts.CONFIRM, objectLgmsResult.getResult_msg());

    }

    /**
     * 请求卡友地带网关接口，格式为reCode、reInfo
     * @param serviceId
     * @param reqData
     * @param <I>
     * @param <O>
     * @return
     * @throws Exception
     */
    public <I,O> LgmsNewResult<O> postBaseMicLrNew(String serviceId, I reqData, Class<O> outClass) throws Exception {
        LgmsPostBean postBean = LgmsPostBuilder.builder(serviceId, reqData).addUniqueId().build();
        return LgmsUtil.postLgmsNres(outClass, postBean);
    }

    public <I, O> O postMicLrNew(String serviceId, I reqData, Class<O> outClass) throws Exception {
        LgmsNewResult<O> lgmsNewResult = this.postBaseMicLrNew(serviceId, reqData, outClass);
        if (StringUtils.equals("0", lgmsNewResult.getReCode())) {
            return lgmsNewResult.getResult();
        }
        // 其他返回码统一转换为2
        throw new UnifiedBusinessException(ReCodeConsts.CONFIRM, lgmsNewResult.getReInfo());

    }


}
