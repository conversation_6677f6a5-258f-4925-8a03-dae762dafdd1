package com.wtyt.lgfesentryedamanagement.pub.rpc.bean.request;

import lombok.Data;

@Data
public class Luge8000533Request {
    /**
     * 钉钉企业corpid
     * 线上钉钉：dinga4a007414b2a0b4c
     * 测试钉钉：ding0841f89fd88169354ac5d6980864d335
     */
    private String refId;
    /**
     * tb用户userid
     * teambition平台的tb userId
     *
     * tbId和extraUserId至少一个不为空
     */
    private String tbId;
    /**
     * 钉钉用户userId
     * 钉钉平台的钉钉 userId
     *
     * tbId和extraUserId至少一个不为空
     */
    private String extraUserId;

}
