package com.wtyt.lgfesentryedamanagement.pub.rpc.bean.response;

import lombok.Data;

import java.util.List;

@Data
public class Bbs5251118Response {

    /**
     * 部门ID
     */
    private String deptId;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 删除标记
     * 0-未删除，
     * 1-删除
     */
    private String delFlag;
    /**
     * 子部门
     * 如果存在子部门，一直嵌套下去
     */
    private List<Bbs5251118Response> childDp;

}
