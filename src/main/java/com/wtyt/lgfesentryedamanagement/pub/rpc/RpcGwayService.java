package com.wtyt.lgfesentryedamanagement.pub.rpc;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.wtyt.gateway.sdk.MgwClient;
import com.wtyt.gateway.sdk.MgwClientBuilder;
import com.wtyt.gateway.sdk.bean.MgwBaseIBean;
import com.wtyt.gateway.sdk.bean.MgwBaseOBean;
import com.wtyt.gateway.sdk.enums.EncryptDataEnum;
import com.wtyt.gateway.sdk.enums.MgwTypeEnum;
import com.wtyt.gateway.sdk.enums.SignTypeEnum;
import com.wtyt.lg.commons.exception.BaseTipException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * 金融网关封装调用服务
 */
@Component
public class RpcGwayService {

    private static final Logger logger = LoggerFactory.getLogger(RpcGwayService.class);
    private static final Gson gson = new GsonBuilder().disableHtmlEscaping().create();

    private MgwClient client;

    public RpcGwayService() throws com.wtyt.lg.commons.exception.BaseTipException {
        this.initClient();
    }

    /**
     * 初始化MgwClient
     */
    private void initClient() throws com.wtyt.lg.commons.exception.BaseTipException {
        Config config = ConfigService.getConfig("yanfa.fe.commons.jr.gateway");
        Config domainConfig = ConfigService.getConfig("yanfa.gateway.zuul");
        this.client = MgwClientBuilder.build()
                .setMgwUrl(domainConfig.getProperty("gateway.jr.inner.domain", ""))
                .setAid(config.getProperty("jr.gateway.appId", ""))
                .setAppSecret(config.getProperty("jr.gateway.app.secret", ""))
                .setPrivateKey(config.getProperty("jr.gateway.privateKey", ""))
                .setSignType(SignTypeEnum.SHA256WithRSA)
                .setMgwType(MgwTypeEnum.GATEWAY)// 不指定默认是普通请求
                .setConnectTimeout(Integer.parseInt(config.getProperty("jr.gateway.connectTimeout", "10000")))// 不指定默认是10s
                .setReadTimeout(Integer.parseInt(config.getProperty("jr.gateway.readTimeout", "10000")))// 不指定默认是10s
                .create();
    }

    @ApolloConfigChangeListener("yanfa.fe.commons.jr.gateway")
    public void refresh(ConfigChangeEvent changeEvent) {
        try {
            this.initClient();
        } catch (com.wtyt.lg.commons.exception.BaseTipException e) {
            logger.warn("刷新MgwClient失败");
        }
    }


    /**
     * 调用网关接口，返回原始数据集
     */
    public <I, O> MgwBaseOBean<O> rpcBaseResult(I data, String sid, EncryptDataEnum dataEnum, Class<O> outClazz) throws BaseTipException {
        MgwBaseIBean<I> baseIBean = new MgwBaseIBean<>();
        baseIBean.setData(data);// 设置请求参数对象
        baseIBean.setSid(sid);// 请求接口sid
        baseIBean.setEncryptData(dataEnum);// 加密方式
        try {
            logger.info("调用{}接口请求参数:{}", sid, gson.toJson(data));
            MgwBaseOBean<O> result = client.rpcInvoke(baseIBean, outClazz);
            logger.info("调用{}接口返回结果集:{}", sid, gson.toJson(result));
            return result;
        } catch (com.wtyt.lg.commons.exception.BaseTipException e) {
            throw new BaseTipException("调用" + sid + "接口异常，异常信息为：" + e.getMessage(), e);
        } catch (Exception e) {
            throw new BaseTipException("调用" + sid + "接口异常，异常信息为：" + e.getMessage(), e);
        }
    }

    /**
     * 调用网关接口，返回最终数据集
     */
    public <I, O> O rpcResult(I data, String sid, Class<O> outClazz, boolean encrypt) throws BaseTipException {
        EncryptDataEnum dataEnum = encrypt ? EncryptDataEnum.ALL_ENCRYPT : EncryptDataEnum.NO_ENCRYPT;
        MgwBaseOBean<O> result = this.rpcBaseResult(data, sid, dataEnum, outClazz);
        if ("0".equals(result.getReCode())) {
            return result.getResult();
        }
        throw new BaseTipException("调用" + sid + "接口异常，异常信息为：" + result.getReInfo());
    }

    /**
     * 调用网关接口，返回最终数据集
     */
    public <I> void rpcResult(I data, String sid, boolean encrypt) throws BaseTipException {
        this.rpcResult(data, sid, Void.class, encrypt);
    }

}
