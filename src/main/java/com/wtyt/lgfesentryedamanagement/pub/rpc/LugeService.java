package com.wtyt.lgfesentryedamanagement.pub.rpc;

import com.wtyt.lgfesentryedamanagement.pub.rpc.bean.request.Luge1076Request;
import com.wtyt.lgfesentryedamanagement.pub.rpc.bean.request.Luge8000533Request;
import com.wtyt.lgfesentryedamanagement.pub.rpc.bean.request.Luge8000535Request;
import com.wtyt.lgfesentryedamanagement.pub.rpc.bean.response.Luge1076Response;
import com.wtyt.lgfesentryedamanagement.pub.rpc.bean.response.Luge8000533Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 社区网关调用接口
 */
@Service
public class LugeService {

    @Autowired
    private RpcSqService rpcSqService;


    public Luge1076Response invoke1076(Luge1076Request request) throws Exception {
        return rpcSqService.postMicLrOld("1076", request, Luge1076Response.class);
    }

    public void invoke8000535(Luge8000535Request request) throws Exception {
        rpcSqService.postMicLrNew("8000535", request, Object.class);
    }

    public Luge8000533Response invoke8000533(Luge8000533Request request) throws Exception {
        return rpcSqService.postMicLrNew("8000533", request, Luge8000533Response.class);
    }
}
