package com.wtyt.lgfesentryedamanagement.pub.rpc;

import com.wtyt.lg.commons.exception.BaseTipException;
import com.wtyt.lgfesentryedamanagement.pub.rpc.bean.request.Bbs5251117Request;
import com.wtyt.lgfesentryedamanagement.pub.rpc.bean.request.Bbs5251118Request;
import com.wtyt.lgfesentryedamanagement.pub.rpc.bean.response.Bbs5251117Response;
import com.wtyt.lgfesentryedamanagement.pub.rpc.bean.response.Bbs5251118Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class BbsGwayService {
    @Autowired
    private RpcGwayService rpcGwayService;

    /**
     * 【5251117】查询钉钉用户信息
     * @param bbs5251117Request
     * @return
     * @throws BaseTipException
     */
    public Bbs5251117Response invoke5251117(Bbs5251117Request bbs5251117Request) throws BaseTipException {
        return rpcGwayService.rpcResult(bbs5251117Request, "5251117", Bbs5251117Response.class, false);
    }
    /**
     * 【5251118】查询钉钉组织架构
     * @param bbs5251118Request
     * @return
     * @throws BaseTipException
     */
    public Bbs5251118Response invoke5251118(Bbs5251118Request bbs5251118Request) throws BaseTipException {
        return rpcGwayService.rpcResult(bbs5251118Request, "5251118", Bbs5251118Response.class, false);
    }
}
