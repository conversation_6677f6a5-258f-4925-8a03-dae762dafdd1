package com.wtyt.lgfesentryedamanagement.pub.rpc;

import com.aliyun.dingtalkyida_1_0.Client;
import com.aliyun.dingtalkyida_1_0.models.*;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.ctrip.framework.apollo.ConfigService;
import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import com.wtyt.lgfesentryedamanagement.pub.rpc.bean.request.Luge1076Request;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.JsonToolkit;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 调用宜搭接口封装服务
 */
@Component
public class RpcYdService implements InitializingBean {

    private static Logger logger = LogManager.getLogger(RpcYdService.class);

    @Autowired
    private LugeService lugeService;

    /**
     * 宜搭调用客户端，公用的，固定的
     */
    private Client client;

    /**
     * 宜搭应用编码
     * APP_P4SD284GYJNBE0RZF1BK
     */
    private String appType;
    /**
     * 宜搭应用秘钥
     * APP_P4SD284GYJNBE0RZF1BK
     */
    private String systemToken;
    /**
     * 宜搭应用管理员用户id
     * 4258471638823393
     */
    private String managerUserId;
    /**
     * 钉钉应用类型
     */
    private String ddType;

    @Override
    public void afterPropertiesSet() throws Exception {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        client = new Client(config);

        com.ctrip.framework.apollo.Config apolloConfig = ConfigService.getConfig("yanfa.fe.commons.yd.app");
        appType = apolloConfig.getProperty("yd.app.operLink.appType", "");
        systemToken = apolloConfig.getProperty("yd.app.operLink.systemToken", "");
        // 线上的应用
//        appType = "APP_B4YS2RVCKTP8NI5V14W7";
//        systemToken = "IXC66971C43INCRV7G1VUC1E241M29C0UMYRL71M";
        managerUserId = apolloConfig.getProperty("yd.app.operLink.managerUserId", "");

        ddType = apolloConfig.getProperty("yd.app.operLink.ddType", "");
    }


    private String getAccessToken() throws Exception {
        return lugeService.invoke1076(Luge1076Request.ofType(ddType)).getAccess_token();
    }

    /**
     * 获取表单组件定义列表, 不用这个，用下面那个(getFieldDefByUuid)，这个会返回多余的页面及容器组件
     * @param formUuid
     * @throws Exception
     */
    public GetFormComponentDefinitionListResponse queryFormComponentInfo(String formUuid) throws Exception {
        GetFormComponentDefinitionListHeaders headers = new GetFormComponentDefinitionListHeaders();
        headers.xAcsDingtalkAccessToken = getAccessToken();

        GetFormComponentDefinitionListRequest request = new GetFormComponentDefinitionListRequest()
                .setSystemToken(systemToken)
                .setUserId(managerUserId)
                .setLanguage("zh_CN");
        logger.info("调用宜搭 getFormComponentDefinitionListWithOptions, 请求头：{}, 请求数据：{}", JsonToolkit.objectToJson(headers), JsonToolkit.objectToJson(request));
        GetFormComponentDefinitionListResponse response = client.getFormComponentDefinitionListWithOptions(appType, formUuid, request, headers, new RuntimeOptions());
        logger.info("调用宜搭 getFormComponentDefinitionListWithOptions 返回数据：{}", JsonToolkit.objectToJson(response));
        return response;
    }

    /**
     * 获取表单内的组件信息
     * @param formUuid
     * @return
     * @throws Exception
     */
    public List<GetFieldDefByUuidResponseBody.GetFieldDefByUuidResponseBodyResult> getFieldDefByUuid(String formUuid) throws Exception {
        com.aliyun.dingtalkyida_1_0.models.GetFieldDefByUuidHeaders headers = new com.aliyun.dingtalkyida_1_0.models.GetFieldDefByUuidHeaders();
        headers.xAcsDingtalkAccessToken = getAccessToken();
        com.aliyun.dingtalkyida_1_0.models.GetFieldDefByUuidRequest request = new com.aliyun.dingtalkyida_1_0.models.GetFieldDefByUuidRequest()
                .setAppType(appType)
                .setSystemToken(systemToken)
                .setFormUuid(formUuid)
                .setUserId(managerUserId);
        logger.info("调用宜搭 getFieldDefByUuidWithOptions, 请求头：{}, 请求数据：{}", JsonToolkit.objectToJson(headers), JsonToolkit.objectToJson(request));
        GetFieldDefByUuidResponse response = client.getFieldDefByUuidWithOptions(request, headers, new RuntimeOptions());
        logger.info("调用宜搭 getFieldDefByUuidWithOptions 返回数据：{}", JsonToolkit.objectToJson(response));
        if (response.getStatusCode() != 200 || !response.getBody().success) {
            throw new UnifiedBusinessException("宜搭返回数据异常");
        }
        return response.getBody().getResult();
    }

    /**
     * 通过表单实例id查询表单数据; 这个只会返回 formData, 不会返回 instanceValue； 使用下面的方法（getInstanceById）
     * https://open.dingtalk.com/document/orgapp/query-form-data
     * @param formInstanceId
     * @return
     * @throws Exception
     */
    public GetFormDataByIDResponseBody getFormDataByFormInstId(String formInstanceId) throws Exception {
        GetFormDataByIDHeaders headers = new GetFormDataByIDHeaders();
        headers.xAcsDingtalkAccessToken = getAccessToken();
        GetFormDataByIDRequest request = new GetFormDataByIDRequest()
                .setAppType(appType)
                .setSystemToken(systemToken)
                .setUserId(managerUserId)
                .setLanguage("zh_CN");
        logger.info("调用宜搭 getFormDataByIDWithOptions, 请求头：{}, 请求数据：{}", JsonToolkit.objectToJson(headers), JsonToolkit.objectToJson(request));
        GetFormDataByIDResponse response = client.getFormDataByIDWithOptions(formInstanceId, request, headers, new RuntimeOptions());
        logger.info("调用宜搭 getFormDataByIDWithOptions 返回数据：{}", JsonToolkit.objectToJson(response));
        if (response.getStatusCode() != 200) {
            throw new UnifiedBusinessException("宜搭返回数据异常");
        }
        return response.getBody();
    }

    /**
     * 通过表单实例id获取表单实例数据, instanceValue(以组件形式展示实例数据)
     * https://open.dingtalk.com/document/orgapp/obtain-multiple-form-instance-data
     *
     * @param formUuid
     * @param formInstanceId
     * @return
     */
    public BatchGetFormDataByIdListResponseBody.BatchGetFormDataByIdListResponseBodyResult getInstanceById(String formUuid, String formInstanceId) throws Exception {
        BatchGetFormDataByIdListHeaders headers = new BatchGetFormDataByIdListHeaders();
        headers.xAcsDingtalkAccessToken = getAccessToken();
        BatchGetFormDataByIdListRequest request = new BatchGetFormDataByIdListRequest()
                .setFormUuid(formUuid)
                .setAppType(appType)
                .setSystemToken(systemToken)
                .setFormInstanceIdList(Arrays.asList(formInstanceId))
                .setNeedFormInstanceValue(true)
                .setUserId(managerUserId);
        logger.info("调用宜搭 batchGetFormDataByIdListWithOptions, 请求头：{}, 请求数据：{}", JsonToolkit.objectToJson(headers), JsonToolkit.objectToJson(request));
        BatchGetFormDataByIdListResponse response = client.batchGetFormDataByIdListWithOptions(request, headers, new RuntimeOptions());
        logger.info("调用宜搭 batchGetFormDataByIdListWithOptions 返回数据：{}", JsonToolkit.objectToJson(response));
        if (response.getStatusCode() != 200) {
            throw new UnifiedBusinessException("宜搭返回数据异常");
        }
        if (response.getBody() == null || CollectionUtils.isEmpty(response.getBody().getResult())) {
            throw new UnifiedBusinessException("宜搭返回数据为空");
        }
        return response.getBody().getResult().get(0);
    }

    /**
     * 获取指定应用下的表单列表（普通表单）
     * @param currentPage
     * @return
     * @throws Exception
     */
    public GetFormListInAppResponseBody.GetFormListInAppResponseBodyResult getFormListInAppWithOptions(int currentPage) throws Exception {
        GetFormListInAppHeaders headers = new GetFormListInAppHeaders();
        headers.xAcsDingtalkAccessToken = getAccessToken();
        GetFormListInAppRequest request = new GetFormListInAppRequest()
                .setAppType(appType)
                .setSystemToken(systemToken)
                .setFormTypes("receipt") // 普通表单
                .setPageSize(100)
                .setPageNumber(currentPage)
                .setUserId(managerUserId);
        logger.info("调用宜搭 getFormListInAppWithOptions, 请求头：{}, 请求数据：{}", JsonToolkit.objectToJson(headers), JsonToolkit.objectToJson(request));
        GetFormListInAppResponse response = client.getFormListInAppWithOptions(request, headers, new RuntimeOptions());
        logger.info("调用宜搭 getFormListInAppWithOptions 返回数据：{}", JsonToolkit.objectToJson(response));
        if (response.getStatusCode() != 200 || !response.getBody().success) {
            throw new UnifiedBusinessException("宜搭返回数据异常");
        }
        return response.getBody().getResult();
    }

    /**
     * 查询表单实例数据,分页封装, 每页10条数据，按照创建时间升序排序
     * @param formUuid
     * @param currentPage
     * @return
     * @throws Exception
     */
    public List<SearchFormDatasResponseBody.SearchFormDatasResponseBodyData> pageSearchFormDataList(String formUuid, int currentPage) throws Exception {
        String dynamicOrder = "{\"gmt_create\":\"+\"}";// 按照提交时间升序排序
        return pageSearchFormDataList(formUuid, currentPage, 10, "", dynamicOrder);
    }
    /**
     * 查询表单实例数据,分页封装
     *
     * @param formUuid
     * @param currentPage
     * @param pageSize
     * @param searchFieldJson
     * @param dynamicOrder
     * @return
     * @throws Exception
     */
    public List<SearchFormDatasResponseBody.SearchFormDatasResponseBodyData> pageSearchFormDataList(String formUuid, int currentPage, int pageSize, String searchFieldJson, String dynamicOrder) throws Exception {
        SearchFormDatasHeaders headers = new SearchFormDatasHeaders();
        headers.xAcsDingtalkAccessToken = getAccessToken();
        SearchFormDatasRequest request = new SearchFormDatasRequest()
                .setAppType(appType)
                .setSystemToken(systemToken)
                .setUserId(managerUserId)
//                .setLanguage("zh_CN")
                .setFormUuid(formUuid)
                .setSearchFieldJson(searchFieldJson)
                .setCurrentPage(currentPage)
                .setPageSize(pageSize)
                .setDynamicOrder(dynamicOrder);

        logger.info("调用宜搭 searchFormDatasWithOptions, 请求头：{}, 请求数据：{}", JsonToolkit.objectToJson(headers), JsonToolkit.objectToJson(request));
        SearchFormDatasResponse response = client.searchFormDatasWithOptions(request, headers, new RuntimeOptions());
        logger.info("调用宜搭 searchFormDatasWithOptions 返回数据：{}", JsonToolkit.objectToJson(response));
        if (response.getStatusCode() != 200) {
            throw new UnifiedBusinessException("宜搭返回数据异常");
        }
        if (response.getBody() == null || CollectionUtils.isEmpty(response.getBody().getData())) {
            return new ArrayList<>();
        }
        return response.getBody().getData();

    }

}
