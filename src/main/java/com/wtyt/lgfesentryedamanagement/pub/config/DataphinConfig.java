package com.wtyt.lgfesentryedamanagement.pub.config;

import com.aliyun.odps.Odps;
import com.aliyun.odps.account.Account;
import com.aliyun.odps.account.AliyunAccount;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.wtyt.kaa.util.KyddAutoConfigUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DataphinConfig {

    // MaxCompute项目的Endpoint信息，详情请参见配置Endpoint。
    private static String odpsEndpoint = "http://service.cn-shanghai.maxcompute.aliyun.com/api";

    public Config dataphinApolloConfig =  ConfigService.getConfig("yanfa.cloud.platform.alibaba.eda");

    @Bean
    public Odps odpsClient(){
        String project = "LOG56_ODS_dev";
        if(KyddAutoConfigUtil.isOnlineEnv()){
            project = "LOG56_ODS";
        }
        Account account = new AliyunAccount(dataphinApolloConfig.getProperty("accessKeyId",""), dataphinApolloConfig.getProperty("accessKeySecret",""));
        Odps odps = new Odps(account);
        odps.setEndpoint(odpsEndpoint);
        odps.setDefaultProject(project);

        //监听apollo变动，如果变动了则修改accout的内容
        dataphinApolloConfig.addChangeListener((e)->{
            if(e.isChanged("accessKeyId")){
                odps.setAccount(new AliyunAccount(dataphinApolloConfig.getProperty("accessKeyId",""), dataphinApolloConfig.getProperty("accessKeySecret","")));
            }
            if(e.isChanged("accessKeySecret")){
                odps.setAccount(new AliyunAccount(dataphinApolloConfig.getProperty("accessKeyId",""), dataphinApolloConfig.getProperty("accessKeySecret","")));
            }
        });

        return odps;
    }
}
