package com.wtyt.lgfesentryedamanagement.pub.config;

import cn.hippo4j.common.executor.support.BlockingQueueTypeEnum;
import cn.hippo4j.core.executor.DynamicThreadPool;
import cn.hippo4j.core.executor.support.ThreadPoolBuilder;
import cn.hippo4j.springboot.starter.factory.LgTransimitThreadFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
public class ThreadPoolConfig {

    /**
     *  计算覆盖率的ThreadPool
     * @return
     */
    @Bean(name = "calBkcRateThreadPool")
    @DynamicThreadPool
    public ThreadPoolExecutor calBurypoitBackendCoverThreadPool() {
        String threadPoolId = "lg-fesentry-eda-management@calBkcRateThreadPool";
        ThreadPoolExecutor calBurypoitBackendCoverThreadPool = ThreadPoolBuilder.builder()
                .threadFactory(new LgTransimitThreadFactory("calBkcRateThreadPool"))
                .threadPoolId(threadPoolId)
                .corePoolSize(30)//核心线程数
                .maximumPoolSize(30)//最大线程数
                .workQueue(BlockingQueueTypeEnum.RESIZABLE_LINKED_BLOCKING_QUEUE)//线程池队列类型
                .capacity(10000)//队列容量
                .keepAliveTime(600,TimeUnit.SECONDS)
                .executeTimeOut(600000)//执行任务超时时间，超时后会告警，不会中断任务
                .timeUnit(TimeUnit.MILLISECONDS)//时间单位
                .dynamicPool()
                .build();
        return calBurypoitBackendCoverThreadPool;
    }

    /**
     * 共用的ThreadPool
     * @return
     */
    @Bean(name = "commonThreadPool")
    @DynamicThreadPool
    public ThreadPoolExecutor commonThreadPool() {
        String threadPoolId = "lg-fesentry-eda-management@commonThreadPool";
        ThreadPoolExecutor commonThreadPool = ThreadPoolBuilder.builder()
                .threadFactory(new LgTransimitThreadFactory("commonThreadPool"))
                .threadPoolId(threadPoolId)
                .corePoolSize(10)//核心线程数
                .maximumPoolSize(20)//最大线程数
                .workQueue(BlockingQueueTypeEnum.RESIZABLE_LINKED_BLOCKING_QUEUE)//线程池队列类型
                .capacity(10000)//队列容量
                .keepAliveTime(600,TimeUnit.SECONDS)
                .executeTimeOut(10000)//执行任务超时时间，超时后会告警，不会中断任务
                .timeUnit(TimeUnit.MILLISECONDS)//时间单位
                .dynamicPool()
                .build();
        return commonThreadPool;
    }

}
