package com.wtyt.lgfesentryedamanagement.pub.config;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.wtyt.qst.lgms.util.LgmsInit;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Component
public class StartUpLuge implements CommandLineRunner {

    private static Logger logger = LogManager.getLogger(StartUpLuge.class);

    @Override
    public void run(String... args) throws Exception {
        //初始化社区微服务配置
        this.initLgms();
    }

    public void initLgms() {
        logger.info("进入初始化社区微服务配置！");
        try {
            Config config = ConfigService.getConfig("yanfa.fe.commons.sq.gateway");
            Config domainConfig = ConfigService.getConfig("yanfa.gateway.zuul");
            // 初始化社区微服务调用的app_id和app_secret等参数
            LgmsInit.initLgmsParam(
                    config.getProperty("sq.gateway.appId", ""),
                    config.getProperty("sq.gateway.app.secret", ""),
                    domainConfig.getProperty("gateway.sq.server.inner.url", ""),
                    Integer.parseInt(config.getProperty("sq.gateway.connectTimeout", "15000")),
                    Integer.parseInt(config.getProperty("sq.gateway.readTimeout", "15000")));
        } catch (Exception e) {
            logger.error(e, e);
        }
        logger.info("离开初始化社区微服务配置！");
    }
}
