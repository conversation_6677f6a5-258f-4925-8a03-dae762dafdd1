package com.wtyt.lgfesentryedamanagement.pub.config;

import javax.sql.DataSource;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.mybatis.spring.boot.autoconfigure.SpringBootVFS;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import com.wtyt.security.wrapper.druid.DruidDataSourceWrapperFactory;
import com.wtyt.security.wrapper.druid.enums.DBOperTypeEnum;

@Configuration
@MapperScan(basePackages = {"com.wtyt.**.mapper"}, sqlSessionTemplateRef = "lgfemSqlSessionTemplate")
public class DataSourceConfig {

	@Bean(name = "lgfemDataSource")
	public DataSource getDataSource() {
		return DruidDataSourceWrapperFactory.createDataSource("yanfa.datasource.mysql.lgfem", "spring.datasource.mysql.lgfem", DBOperTypeEnum.READ_WRITE);
	}

	@Primary
	@Bean(name = "lgfemTransactionManager")
	public DataSourceTransactionManager setTransactionManager(@Qualifier("lgfemDataSource") DataSource dataSource) {
		return new DataSourceTransactionManager(dataSource);
	}

	@Primary
	@Bean(name = "lgfemSqlSessionFactory")
	public SqlSessionFactory setSqlSessionFactory(@Qualifier("lgfemDataSource") DataSource dataSource) throws Exception {
		SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
		bean.setDataSource(dataSource);
		bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:com/wtyt/**/mapper/*Mapper.xml"));
		bean.setConfigLocation(new DefaultResourceLoader().getResource("classpath:config/mybatis-config.xml"));
		bean.setVfs(SpringBootVFS.class);
		return bean.getObject();
	}

	@Bean(name = "lgfemSqlSessionTemplate")
	public SqlSessionTemplate setSqlSessionTemplate(@Qualifier("lgfemSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
		return new SqlSessionTemplate(sqlSessionFactory);
	}
}
