package com.wtyt.lgfesentryedamanagement.pub.consts;

import java.util.regex.Pattern;

/**
 * 正则表达式常量类
 * 
 * <AUTHOR>
 *
 */
public interface PatternConsts {

    /**
     * 手机号正则，1开头，后面10个数字
     */
    String MOBILE_NO = "^1\\d{10}$";

    /**
     * Email正则
     */
    Pattern EMAIL = Pattern.compile("^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$");

    /**
     * 金额正则，必须是格式化为两位小数的金额<br>
     * 比如：8.00,0.00,0.10等
     */
    Pattern MONEY_STRICT = Pattern.compile("^\\d+[.]\\d{2}$");

    /**
     * 只包含数字或字母
     */
    Pattern ONLY_NUMBER_LETTER = Pattern.compile("^[a-zA-Z0-9]*$");

    /**
     * 金额正则，是格式化为一位小数的金额或者两位小数的金额或为整数<br>
     * 比如：10.0,8.00,0.00,0.10,10等
     */
    Pattern MONEY_PATTERN = Pattern.compile("^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$");

    /**
     * 数字正则，是格式化为一位小数的金额或者两位小数的金额或为整数<br>
     * 比如：10.0,8.0000,0.000,0.100,10等
     */
    Pattern DOYBLE_FOUR_PATTERN = Pattern.compile("^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,4})?$");

    /**
     * 验证数字
     */
    Pattern NUMBER_PATTERN = Pattern.compile("^[0-9]*$");


    /**
     * 身份证号正则15位+19位
     */
     String REGEXP_ID_CARD="^$|(^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$)|(^[1-9]\\d{5}\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{2}$)";



    Pattern CHINESE_PATTERN = Pattern.compile("^[\\u4E00-\\u9FA5]+$");

    /**
     * 车牌号正则包含新能源--不含军车、飞行保障车辆专用号牌等特殊车辆
     */
    String regexpCartBadgeNo="^|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领]{1}(?![IO])[a-zA-Z](([DABCEFGHJK](?![IO])[a-zA-Z0-9][0-9]{4})|([0-9]{5}[DABCEFGHJK]))|[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领]{1}(?![IO])[A-Za-z]{1}(?![IO])[A-Za-z0-9]{4}(?![IO])[A-Za-z0-9挂学警港澳]{1})$";



    String REGULAR_CAR_NO="carno";


    String REGULAR_ID_CARD = "idcard";


    String REGULAR_MOBILE_NO_ALL = "mobile.all";

    String REGULAR_URL = "url";


}
