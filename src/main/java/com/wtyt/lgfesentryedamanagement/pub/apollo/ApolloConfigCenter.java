package com.wtyt.lgfesentryedamanagement.pub.apollo;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfig;
import org.springframework.context.annotation.Configuration;

/**
 * 配置中心工具类
 * 
 * <AUTHOR>
 *
 */
@Configuration
public class ApolloConfigCenter {

    @ApolloConfig("application")
    private Config applicationConfig;

    /**
     * 从Apollo获取application中的参数
     */
    public final ApolloConfigFunction application = key -> applicationConfig.getProperty(key, "");

}
