package com.wtyt.lgfesentryedamanagement.pub.enums;

import com.wtyt.lg.commons.exception.BaseTipException;

/**
 * <AUTHOR>
 * @Date 2023/5/18 18:05
 */
public enum LogOperTypeEnum {

    ADD(1, "新增"),
    EDIT(2, "修改"),
    DEL(3, "删除"),
    ;


    private Integer value;

    private String name;


    public static LogOperTypeEnum getByValue(Integer value) throws BaseTipException {
        for(LogOperTypeEnum logOperTypeEnum:LogOperTypeEnum.values()){
            if(logOperTypeEnum.value.equals(value)){
                return logOperTypeEnum;
            }
        }
        throw new BaseTipException("未知的操作类型");
    }


    LogOperTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
