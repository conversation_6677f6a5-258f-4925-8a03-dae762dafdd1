package com.wtyt.lgfesentryedamanagement.pub.enums;

/**
 * <AUTHOR>
 * @Date 2023/5/18 18:08
 */
public enum LogBusiTypeEnum {

    T_BURYPOINTS_EDA(1, "EDA分支"),
    T_BURYPOINTS_EDA_MASTER(2, "EDA"),
    T_BURYPOINTS_EDA_BK_CONFIG(3, "覆盖率配置");

    private Integer value;

    private String tableName;

    LogBusiTypeEnum(Integer value, String tableName) {
        this.value = value;
        this.tableName = tableName;
    }

    public static String getLogTableName(Integer value) {
        for (LogBusiTypeEnum logBusiTypeEnum : values()) {
            if (logBusiTypeEnum.getValue().equals(value)) {
                return logBusiTypeEnum.getTableName();
            }
        }
        return null;
    }

    public Integer getValue() {
        return value;
    }

    public String getTableName() {
        return tableName;
    }
}
