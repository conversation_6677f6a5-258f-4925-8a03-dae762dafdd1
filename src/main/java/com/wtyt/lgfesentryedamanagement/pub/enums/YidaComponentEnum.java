package com.wtyt.lgfesentryedamanagement.pub.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 宜搭组件枚举（常见）
 */
public enum YidaComponentEnum {

    /**
     * 默认, 不在定义里面的使用 DEFAULT
     */
    DEFAULT,
    /**
     * 单行文本
     */
    TextField,
    /**
     * 多行文本
     */
    TextareaField,
    /**
     * 数值
     */
    NumberField,
    /**
     * 评分
     */
    RateField,
    /**
     * 单选
     */
    RadioField,
    /**
     * 复选
     */
    CheckboxField,
    /**
     * 下拉单选
     */
    SelectField,
    /**
     * 下拉复选
     */
    MultiSelectField,
    /**
     * 级联选择
     */
    CascadeSelectField,
    /**
     * 日期
     */
    DateField,
    /**
     * 日期区间
     */
    CascadeDateField,
    /**
     * 图片上传
     */
    ImageField,
    /**
     * 附件
     */
    AttachmentField,
    /**
     * 成员
     */
    EmployeeField,
    /**
     * 子表单
     */
    TableField,
    /**
     * 部门
     */
    DepartmentSelectField,
    /**
     * 地址
     */
    AddressField,
    /**
     * 关联表单
     */
    AssociationFormField,
    /**
     * 富文本组件
     */
    EditorField,


    ;

    /**
     * 通过名称获取枚举，不存在的返回 DEFAULT
     * @param name
     * @return
     */
    public static YidaComponentEnum ofName(String name) {
        YidaComponentEnum[] values = values();
        for (YidaComponentEnum value : values) {
            if (StringUtils.equals(name, value.name())) {
                return value;
            }
        }
        return DEFAULT;
    }

}
