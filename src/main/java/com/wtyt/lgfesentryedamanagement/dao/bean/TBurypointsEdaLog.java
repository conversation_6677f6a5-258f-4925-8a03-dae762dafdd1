package com.wtyt.lgfesentryedamanagement.dao.bean;

import java.time.LocalDateTime;

/**
* <AUTHOR>
* @date 2024/1/29 16:29
* @vesion 1.0
* @desc 
*/
/**
    * eda埋点维护操作日志
    */
public class TBurypointsEdaLog {
    /**
    * 主键
    */
    private Long burypointsEdaLogId;

    /**
    * 操作的团队名称
    */
    private String optTeam;

    /**
    * 操作人
    */
    private String optUserName;

    /**
    * 操作的EDA分支编号/EDA编号/EDA配置id
    */
    private String edaBranchNo;

    /**
    * 日志类型 ,3:删除EDA，逻辑删除,2:修改EDA,1:新增EDA
    */
    private Integer logType;

    /**
    * 操作中文说明
    */
    private String optNote;

    /**
    * 是否删除 ,1:已删除,0:未删除
    */
    private Integer isDel;

    /**
    * 创建时间
    */
    private LocalDateTime createdTime;

    /**
    * 修改时间
    */
    private LocalDateTime lastModifiedTime;

    /**
    * 备注
    */
    private String note;

    /**
    * 操作类型:1：EDA分支管理，2:EDA管理，3：EDA后端覆盖率管理 ,3:EDA后端覆盖率配置管理,2:EDA管理,1:EDA分支管理
    */
    private Integer optType;

    public Long getBurypointsEdaLogId() {
        return burypointsEdaLogId;
    }

    public void setBurypointsEdaLogId(Long burypointsEdaLogId) {
        this.burypointsEdaLogId = burypointsEdaLogId;
    }

    public String getOptTeam() {
        return optTeam;
    }

    public void setOptTeam(String optTeam) {
        this.optTeam = optTeam;
    }

    public String getOptUserName() {
        return optUserName;
    }

    public void setOptUserName(String optUserName) {
        this.optUserName = optUserName;
    }

    public String getEdaBranchNo() {
        return edaBranchNo;
    }

    public void setEdaBranchNo(String edaBranchNo) {
        this.edaBranchNo = edaBranchNo;
    }

    public Integer getLogType() {
        return logType;
    }

    public void setLogType(Integer logType) {
        this.logType = logType;
    }

    public String getOptNote() {
        return optNote;
    }

    public void setOptNote(String optNote) {
        this.optNote = optNote;
    }

    public Integer getIsDel() {
        return isDel;
    }

    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getLastModifiedTime() {
        return lastModifiedTime;
    }

    public void setLastModifiedTime(LocalDateTime lastModifiedTime) {
        this.lastModifiedTime = lastModifiedTime;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public Integer getOptType() {
        return optType;
    }

    public void setOptType(Integer optType) {
        this.optType = optType;
    }
}