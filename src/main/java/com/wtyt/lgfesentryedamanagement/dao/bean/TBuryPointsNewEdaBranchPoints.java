package com.wtyt.lgfesentryedamanagement.dao.bean;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class TBuryPointsNewEdaBranchPoints {

    private Long edaBranchPointsId;
    private Long edaBranchId;

    private String edaBranchNo;
    private String edaMxcellId;
    private String eleResourceId;
    private String eleResourceName;
    private String eleExtJson;
    private String eleColumnJson;
    private Long edaLinkLoc;
    private Integer isDel;
    private LocalDateTime createdTime;
    private LocalDateTime lastModifiedTime;
    private String note;
    private Long burypointsEleColumnId;
    private Long branchPointsExtfieldsId;
    private String appTag;  

}
