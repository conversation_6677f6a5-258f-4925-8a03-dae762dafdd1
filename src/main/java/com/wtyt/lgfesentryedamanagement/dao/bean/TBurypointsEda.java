package com.wtyt.lgfesentryedamanagement.dao.bean;

import java.time.LocalDateTime;

/**
* <AUTHOR>
* @date 2024/1/29 16:28
* @vesion 1.0
* @desc 
*/
/**
    * EDA分支实体表
    */
public class TBurypointsEda {
    /**
    * 主键
    */
    private Long burypointsEdaId;

    /**
    * EDA编号
    */
    private String edaNo;

    /**
    * EDA中文名称
    */
    private String edaName;

    /**
    * EDA描述说明
    */
    private String edaDesc;

    /**
    * EDA编号类型 ,1:EDA分支编号,0:EDA模块编号；2：功能点
    */
    private Integer edaType;

    /**
    * 是否删除 ,1:已删除,0:未删除
    */
    private Integer isDel;

    /**
    * 创建时间
    */
    private LocalDateTime createdTime;

    /**
    * 修改时间
    */
    private LocalDateTime lastModifiedTime;

    /**
    * 备注
    */
    private String note;

    /**
    * 业务线
    */
    private String edaBizLine;

    /**
    * 产品服务
    */
    private String edaProduct;

    /**
    * 产品模块
    */
    private String edaModule;

    /**
    * 分支EDA父级EDA编码
    */
    private String edaParentNo;

    /**
    * 串行标识类型
    */
    private Integer bizType;

    /**
    * 业务串行标识ID字段名，当BIZ_TYPE=0,3,4时必填，关联t_bur_domain_rel中的BUR_PAGE_CODE字段
    */
    private String bizBurCode;

    /**
    * 当BIZ_TYPE=5时必填，app客户端唯一设备号
    */
    private String appDeviceUdid;

    /**
     * 同步时间
     */
    private LocalDateTime syncTime;

    private LocalDateTime bizTypeModifiedTime;
    
    private Integer judgeType;

    public Integer getJudgeType() {
        return judgeType;
    }

    public void setJudgeType(Integer judgeType) {
        this.judgeType = judgeType;
    }

    public LocalDateTime getBizTypeModifiedTime() {
        return bizTypeModifiedTime;
    }

    public void setBizTypeModifiedTime(LocalDateTime bizTypeModifiedTime) {
        this.bizTypeModifiedTime = bizTypeModifiedTime;
    }

    public Long getBurypointsEdaId() {
        return burypointsEdaId;
    }

    public void setBurypointsEdaId(Long burypointsEdaId) {
        this.burypointsEdaId = burypointsEdaId;
    }

    public String getEdaNo() {
        return edaNo;
    }

    public void setEdaNo(String edaNo) {
        this.edaNo = edaNo;
    }

    public String getEdaName() {
        return edaName;
    }

    public void setEdaName(String edaName) {
        this.edaName = edaName;
    }

    public String getEdaDesc() {
        return edaDesc;
    }

    public void setEdaDesc(String edaDesc) {
        this.edaDesc = edaDesc;
    }

    public Integer getEdaType() {
        return edaType;
    }

    public void setEdaType(Integer edaType) {
        this.edaType = edaType;
    }

    public Integer getIsDel() {
        return isDel;
    }

    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getLastModifiedTime() {
        return lastModifiedTime;
    }

    public void setLastModifiedTime(LocalDateTime lastModifiedTime) {
        this.lastModifiedTime = lastModifiedTime;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getEdaBizLine() {
        return edaBizLine;
    }

    public void setEdaBizLine(String edaBizLine) {
        this.edaBizLine = edaBizLine;
    }

    public String getEdaProduct() {
        return edaProduct;
    }

    public void setEdaProduct(String edaProduct) {
        this.edaProduct = edaProduct;
    }

    public String getEdaModule() {
        return edaModule;
    }

    public void setEdaModule(String edaModule) {
        this.edaModule = edaModule;
    }

    public String getEdaParentNo() {
        return edaParentNo;
    }

    public void setEdaParentNo(String edaParentNo) {
        this.edaParentNo = edaParentNo;
    }

    public Integer getBizType() {
        return bizType;
    }

    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    public String getBizBurCode() {
        return bizBurCode;
    }

    public void setBizBurCode(String bizBurCode) {
        this.bizBurCode = bizBurCode;
    }

    public String getAppDeviceUdid() {
        return appDeviceUdid;
    }

    public void setAppDeviceUdid(String appDeviceUdid) {
        this.appDeviceUdid = appDeviceUdid;
    }

    public LocalDateTime getSyncTime() {
        return syncTime;
    }

    public void setSyncTime(LocalDateTime syncTime) {
        this.syncTime = syncTime;
    }
}