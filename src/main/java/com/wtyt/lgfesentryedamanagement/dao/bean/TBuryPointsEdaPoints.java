package com.wtyt.lgfesentryedamanagement.dao.bean;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class TBuryPointsEdaPoints {

    private Long burypointsEdaPointsId;

    private String edaMxcellId;
    private String edaMxcellParentId;
    private String edaMxcellSourceId;
    private String edaMxcellTargetId;
    private String eleResourceId;
    private String eleExtJson;
    private String eleColumnJson;
    private String appTag;
    private String eleEdaNo;
    private String optLinkId;
    private Long edaLinkLoc;
    private Integer isDel;
    private LocalDateTime createdTime;
    private LocalDateTime lastModifiedTime;
    private String note;
    private String eleResourceName;
    private String eleExtField;
    private String eleExtFieldVal;
    private String bpParentMxcellIds;
    private String edaMxcellType;
    private String burypointsEleColumnId;

}
