package com.wtyt.lgfesentryedamanagement.dao.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
    * EDA埋点元素关系表
    */

@Data
public class TBurypointsEdaRel {

    private Long burypointsEdaRelId;

    private String eleResourceId;

    private String eleEdaNo;

    private String edaBranch;

    private Integer isStart;

    private Integer isEnd;

    private Integer isMBranch;

    private String eleExtField;

    private String eleExtFieldVal;

    private Integer env;

    private Long burDomainRelId;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    private LocalDateTime lastModifiedTime;

    /**
     * 是否删除 ,1:已删除,0:未删除
     */
    private Integer isDel;

}