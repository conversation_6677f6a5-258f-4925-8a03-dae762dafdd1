package com.wtyt.lgfesentryedamanagement.dao.bean;

import lombok.Data;

/**
 * 后端覆盖率配置
 */
@Data
public class TBurypointsEdaCoverBk {

    private String optLinkId;
    private String edaMxcellId;
    /**
    * 主键
    */
    private String burypointsEdaCoverBkId;

    /**
    * 接口所在的项目名
    */
    private String interfaceProjectName;

    /**
    * 接口标识（主要是接口id）
    */
    private String interfaceTag;

    /**
    * 接口类型 ,structs:3,springmvc:2,卡友地带网关:1,金融网关:0
    */
    private String interfaceType;

    /**
    * 接口类型的过滤条件
    */
    private String interfaceFilters;

    /**
    * 使用接口全埋时的索引名称
    */
    private String interfaceIndexName;

    /**
    * 接口类型的排除过滤条件
    */
    private String interfaceExcludeFilters;


}