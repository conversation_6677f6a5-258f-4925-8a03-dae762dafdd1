package com.wtyt.lgfesentryedamanagement.dao.bean;

import java.time.LocalDateTime;

/**
* <AUTHOR>
* @date 2024/1/29 16:29
* @vesion 1.0
* @desc 
*/
/**
    * eda后端埋点的元数据表
    */
public class TBurypointsEdaBkConfig {
    /**
    * 主键
    */
    private Long burypointsEdaBkConfigId;

    /**
    * eda的编号
    */
    private String edaNo;

    /**
    * es索引前缀名称
    */
    private String esIndexName;

    /**
    * 接口所在的项目名
    */
    private String interfaceProjectName;

    /**
    * 接口标识（主要是接口id）
    */
    private String interfaceTag;

    /**
    * 接口类型 ,structs:3,springmvc:2,卡友地带网关:1,金融网关:0
    */
    private Integer interfaceType;

    /**
    * 统计的数值计算类型 ,减法计算类型:1,加法计算类型:0
    */
    private Integer calType;

    /**
    * 当前的过滤类型 ,从接口全埋中过滤:1,已日志方式过滤:0
    */
    private Integer filterType;

    /**
    * 日志类型的过滤条件-多个以逗号相隔
    */
    private String logFiltersKeywords;

    /**
    * 接口类型的过滤条件
    */
    private String interfaceFilters;

    /**
    * 是否删除 ,1:已删除,0:未删除
    */
    private Integer isDel;

    /**
    * 创建时间
    */
    private LocalDateTime createdTime;

    /**
    * 修改时间
    */
    private LocalDateTime lastModifiedTime;

    /**
    * 备注
    */
    private String note;

    /**
    * 日志类型的排除参数过滤条件-多个以逗号相隔
    */
    private String logFiltersExcludeKeywords;

    /**
    * 使用接口全埋时的索引名称
    */
    private String interfaceIndexName;

    /**
    * 接口类型的排除过滤条件
    */
    private String interfaceExcludeFilters;
    /**
     * 链路位置
     */
    private String linkLocation;
    /**
     * EDA分支编号
     */
    private String edaBranch;

    private String configType;//覆盖率类型

    private String eleResourceId;//埋点元素id

    private String eleExtField;//元素扩展字段里的翻译字段

    private String eleExtFieldVal;//元素扩展字段里的翻译字段值


    public Long getBurypointsEdaBkConfigId() {
        return burypointsEdaBkConfigId;
    }

    public void setBurypointsEdaBkConfigId(Long burypointsEdaBkConfigId) {
        this.burypointsEdaBkConfigId = burypointsEdaBkConfigId;
    }

    public String getEdaNo() {
        return edaNo;
    }

    public void setEdaNo(String edaNo) {
        this.edaNo = edaNo;
    }

    public String getEsIndexName() {
        return esIndexName;
    }

    public void setEsIndexName(String esIndexName) {
        this.esIndexName = esIndexName;
    }

    public String getInterfaceProjectName() {
        return interfaceProjectName;
    }

    public void setInterfaceProjectName(String interfaceProjectName) {
        this.interfaceProjectName = interfaceProjectName;
    }

    public String getInterfaceTag() {
        return interfaceTag;
    }

    public void setInterfaceTag(String interfaceTag) {
        this.interfaceTag = interfaceTag;
    }

    public Integer getInterfaceType() {
        return interfaceType;
    }

    public void setInterfaceType(Integer interfaceType) {
        this.interfaceType = interfaceType;
    }

    public Integer getCalType() {
        return calType;
    }

    public void setCalType(Integer calType) {
        this.calType = calType;
    }

    public Integer getFilterType() {
        return filterType;
    }

    public void setFilterType(Integer filterType) {
        this.filterType = filterType;
    }

    public String getLogFiltersKeywords() {
        return logFiltersKeywords;
    }

    public void setLogFiltersKeywords(String logFiltersKeywords) {
        this.logFiltersKeywords = logFiltersKeywords;
    }

    public String getInterfaceFilters() {
        return interfaceFilters;
    }

    public void setInterfaceFilters(String interfaceFilters) {
        this.interfaceFilters = interfaceFilters;
    }

    public Integer getIsDel() {
        return isDel;
    }

    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getLastModifiedTime() {
        return lastModifiedTime;
    }

    public void setLastModifiedTime(LocalDateTime lastModifiedTime) {
        this.lastModifiedTime = lastModifiedTime;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getLogFiltersExcludeKeywords() {
        return logFiltersExcludeKeywords;
    }

    public void setLogFiltersExcludeKeywords(String logFiltersExcludeKeywords) {
        this.logFiltersExcludeKeywords = logFiltersExcludeKeywords;
    }

    public String getInterfaceIndexName() {
        return interfaceIndexName;
    }

    public void setInterfaceIndexName(String interfaceIndexName) {
        this.interfaceIndexName = interfaceIndexName;
    }

    public String getInterfaceExcludeFilters() {
        return interfaceExcludeFilters;
    }

    public void setInterfaceExcludeFilters(String interfaceExcludeFilters) {
        this.interfaceExcludeFilters = interfaceExcludeFilters;
    }

    public String getLinkLocation() {
        return linkLocation;
    }

    public void setLinkLocation(String linkLocation) {
        this.linkLocation = linkLocation;
    }

    public String getEdaBranch() {
        return edaBranch;
    }

    public void setEdaBranch(String edaBranch) {
        this.edaBranch = edaBranch;
    }

    public String getConfigType() {
        return configType;
    }

    public void setConfigType(String configType) {
        this.configType = configType;
    }

    public String getEleResourceId() {
        return eleResourceId;
    }

    public void setEleResourceId(String eleResourceId) {
        this.eleResourceId = eleResourceId;
    }

    public String getEleExtField() {
        return eleExtField;
    }

    public void setEleExtField(String eleExtField) {
        this.eleExtField = eleExtField;
    }

    public String getEleExtFieldVal() {
        return eleExtFieldVal;
    }

    public void setEleExtFieldVal(String eleExtFieldVal) {
        this.eleExtFieldVal = eleExtFieldVal;
    }
}