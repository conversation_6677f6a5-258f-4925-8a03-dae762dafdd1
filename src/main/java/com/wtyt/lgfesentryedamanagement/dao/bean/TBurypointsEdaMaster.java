package com.wtyt.lgfesentryedamanagement.dao.bean;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* <AUTHOR>
* @date 2024/1/29 16:29
* @vesion 1.0
* @desc 
*/
/**
    * EDA实体
    */
public class TBurypointsEdaMaster {
    /**
    * 主键
    */
    private Long burypointsEdaMasterId;

    /**
    * EDA编号
    */
    private String edaNo;

    /**
    * EDA中文名称
    */
    private String edaName;

    /**
     * EDA缩写名称
     */
    private String edaAbbreveName;

    /**
    * EDA描述说明
    */
    private String edaDesc;

    /**
    * 业务线
    */
    private String edaBizLine;

    /**
    * 产品服务
    */
    private String edaProduct;

    /**
    * 产品模块
    */
    private String edaModule;

    /**
    * 维护团队
    */
    private String maintenanceTeam;

    /**
    * EDA状态：0: 不呈现，1：呈现 ,1:呈现,0:不呈现
    */
    private Integer edaStatus;

    /**
    * 负责UI
    */
    private String maintenanceUi;

    /**
    * 覆盖率基线（百分比）
    */
    private BigDecimal coverageBaseline;

    /**
    * 浮动比例（百分比）
    */
    private BigDecimal floatingRatio;

    /**
    * 非常规覆盖率基线原因
    */
    private String unconventionalBaselineReason;

    /**
    * 是否删除 ,1:已删除,0:未删除
    */
    private Integer isDel;

    /**
    * 创建时间
    */
    private LocalDateTime createdTime;

    /**
    * 修改时间
    */
    private LocalDateTime lastModifiedTime;

    /**
    * 备注
    */
    private String note;

    /**
     * 操作链路ID(宜搭)
     */
    private String optLinkId;

    /**
     * EDA类型 ,1:功能点,0:操作链路
     */
    private Integer edaType;


    public Long getBurypointsEdaMasterId() {
        return burypointsEdaMasterId;
    }

    public void setBurypointsEdaMasterId(Long burypointsEdaMasterId) {
        this.burypointsEdaMasterId = burypointsEdaMasterId;
    }

    public String getEdaNo() {
        return edaNo;
    }

    public void setEdaNo(String edaNo) {
        this.edaNo = edaNo;
    }

    public String getEdaName() {
        return edaName;
    }

    public void setEdaName(String edaName) {
        this.edaName = edaName;
    }

    public String getEdaAbbreveName() {
        return edaAbbreveName;
    }

    public void setEdaAbbreveName(String edaAbbreveName) {
        this.edaAbbreveName = edaAbbreveName;
    }

    public String getEdaDesc() {
        return edaDesc;
    }

    public void setEdaDesc(String edaDesc) {
        this.edaDesc = edaDesc;
    }

    public String getEdaBizLine() {
        return edaBizLine;
    }

    public void setEdaBizLine(String edaBizLine) {
        this.edaBizLine = edaBizLine;
    }

    public String getEdaProduct() {
        return edaProduct;
    }

    public void setEdaProduct(String edaProduct) {
        this.edaProduct = edaProduct;
    }

    public String getEdaModule() {
        return edaModule;
    }

    public void setEdaModule(String edaModule) {
        this.edaModule = edaModule;
    }

    public String getMaintenanceTeam() {
        return maintenanceTeam;
    }

    public void setMaintenanceTeam(String maintenanceTeam) {
        this.maintenanceTeam = maintenanceTeam;
    }

    public Integer getEdaStatus() {
        return edaStatus;
    }

    public void setEdaStatus(Integer edaStatus) {
        this.edaStatus = edaStatus;
    }

    public String getMaintenanceUi() {
        return maintenanceUi;
    }

    public void setMaintenanceUi(String maintenanceUi) {
        this.maintenanceUi = maintenanceUi;
    }

    public BigDecimal getCoverageBaseline() {
        return coverageBaseline;
    }

    public void setCoverageBaseline(BigDecimal coverageBaseline) {
        this.coverageBaseline = coverageBaseline;
    }

    public BigDecimal getFloatingRatio() {
        return floatingRatio;
    }

    public void setFloatingRatio(BigDecimal floatingRatio) {
        this.floatingRatio = floatingRatio;
    }

    public String getUnconventionalBaselineReason() {
        return unconventionalBaselineReason;
    }

    public void setUnconventionalBaselineReason(String unconventionalBaselineReason) {
        this.unconventionalBaselineReason = unconventionalBaselineReason;
    }

    public Integer getIsDel() {
        return isDel;
    }

    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getLastModifiedTime() {
        return lastModifiedTime;
    }

    public void setLastModifiedTime(LocalDateTime lastModifiedTime) {
        this.lastModifiedTime = lastModifiedTime;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getOptLinkId() {
        return optLinkId;
    }

    public void setOptLinkId(String optLinkId) {
        this.optLinkId = optLinkId;
    }

    public Integer getEdaType() {
        return edaType;
    }

    public void setEdaType(Integer edaType) {
        this.edaType = edaType;
    }
}