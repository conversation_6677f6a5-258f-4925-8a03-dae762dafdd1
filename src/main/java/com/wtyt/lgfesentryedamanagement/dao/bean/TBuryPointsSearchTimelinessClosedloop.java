package com.wtyt.lgfesentryedamanagement.dao.bean;
import java.time.LocalDateTime;
import lombok.Data;

@Data
public class TBuryPointsSearchTimelinessClosedloop {
    private Long seTimelinessClosedloopId;

    private String optLinkId;
    private String edaBranchNos;
    private String searchDs;// 查询分区 例：20250601，默认昨日
    private String orgIds; // 查询条件：项目ID，多个以逗号隔开
    private String type; // 查询类型： 0-台账数据查询 1-探测数据查询
    private String env; // 查询环境： PRO-生产环境 FAT-测试环境
    private String realUserIds; // 查询条件：user_id / driver_id，多个以逗号隔开
    private String startDateId;// 开始日期 例：20250601，默认昨日
    private String endDateId;// 结束日期 例：20250601，默认昨日
    private String searchState;// 查询状态 0:查询中 1:查询完成 2:查询失败
    private String searchFailInfo; // 查询失败信息
    private String searchResult; // 查询结果json
    private Integer isDel;
    private LocalDateTime createdTime;
    private LocalDateTime lastModifiedTime;
    private String note;   
    
}
