package com.wtyt.lgfesentryedamanagement.dao.bean;

import lombok.Data;

/**
    * EDA覆盖率查询日志
    */
@Data
public class TBurypointsCoverRate {
    /**
    * 主键
    */
    private String burypointsCoverRateId;

    private String edaNo;

    /**
     * EDA中文名称
     */
    private String edaName;

    /**
     * EDA缩写名称
     */
    private String edaAbbreveName;

    private String startTime;

    private String endTime;

    private String executeTime;

    private String bkValue;

    private String ftValue;

    private String coverRate;

    private String ftExecuteSql;

    private String backList;

    private String frontList;

    private String optUserId;

    private String optUserName;

    private String searchState;

    private String linkLocation;

    private String searchFailInfo;

    private String branchList;//分支探测结果json

}