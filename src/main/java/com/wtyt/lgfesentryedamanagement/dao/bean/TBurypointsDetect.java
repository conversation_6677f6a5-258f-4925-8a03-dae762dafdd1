package com.wtyt.lgfesentryedamanagement.dao.bean;

import lombok.Data;

import java.util.List;

/**
    * EDA探测数据
    */
@Data
public class TBurypointsDetect {
    /**
    * 主键
    */
    private String burypointsDetectionId;

    private String searchId;

    private String edaNo;

    /**
     * EDA中文名称
     */
    private String edaName;

    /**
     * EDA缩写名称
     */
    private String edaAbbreveName;

    private String startTime;

    private String endTime;

    private String executeTime;

    private String ftExecuteSql;

    private String searchState;

    private String searchFailInfo;

    private String optLinkId;

    private String env;

    private List<String> searchItem;

    private String searchStr;

    private String searchItemStr;

    private String searchData;

    private String coverData;



}