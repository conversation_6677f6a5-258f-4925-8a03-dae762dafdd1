package com.wtyt.lgfesentryedamanagement.dao.mapper;

import com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaLog;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @date 2024/1/29 16:29
* @vesion 1.0
* @desc 
*/
public interface TBurypointsEdaLogMapper {
    int deleteByPrimaryKey(Long burypointsEdaLogId);

    int insert(TBurypointsEdaLog record);

    int insertSelective(TBurypointsEdaLog record);

    TBurypointsEdaLog selectByPrimaryKey(Long burypointsEdaLogId);

    int updateByPrimaryKeySelective(TBurypointsEdaLog record);

    int updateByPrimaryKey(TBurypointsEdaLog record);
}