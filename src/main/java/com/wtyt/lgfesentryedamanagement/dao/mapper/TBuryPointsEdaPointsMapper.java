package com.wtyt.lgfesentryedamanagement.dao.mapper;

import com.wtyt.lgfesentryedamanagement.bkc.bean.BkcEdaConfBean;
import com.wtyt.lgfesentryedamanagement.dao.bean.TBuryPointsEdaPoints;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TBuryPointsEdaPointsMapper {


    /**
     * 批量插入
     * @param tBuryPointsEdaPointsList
     */
    int batchInsert(@Param("list") List<TBuryPointsEdaPoints> tBuryPointsEdaPointsList);

    /**
     * 根据操作链路id删除已有数据
     * @param optLinkId
     */
    int delByOptLinkId(@Param("optLinkId") String optLinkId);

    TBuryPointsEdaPoints selectByMxcellId(@Param("edaMxcellId") String edaMxcellId,@Param("optLinkId") String optLinkId);

    List<TBuryPointsEdaPoints> selectBpMxcellIdByOptLinkId(@Param("optLinkId") String optLinkId);

    List<TBuryPointsEdaPoints> queryList(List<String> list);
}
