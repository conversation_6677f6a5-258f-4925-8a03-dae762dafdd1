package com.wtyt.lgfesentryedamanagement.dao.mapper;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface EdaYidaSyncMapper {


    /**
     * 检测表名是否存在
     * @param tableName
     * @return
     */
    Map<String, String> checkTableExistsWithShow(@Param("tableName") String tableName);

    /**
     * 查询表字段信息
     * @param tableName
     * @return
     */
    List<Map<String, String>> allColumnsFromTableWithShow(@Param("tableName") String tableName);

    /**
     * 根据formInstanceId 查询数据
     * @param tableName
     * @param formInstanceId
     * @return
     */
    int checkExistByFormInstanceId(@Param("tableName") String tableName, @Param("formInstanceId") String formInstanceId);

    void executeSql(@Param("sql") String sql);

    /**
     * 新增数据
     * @param tableName 表名
     * @param mapData   新增的字段级数据，key:字段名，value:数据
     * @return
     */
    int insertData(@Param("tableName") String tableName, @Param("mapData") Map<String, String> mapData);

    /**
     * 根据实例id更新表数据
     * @param tableName     表名
     * @param formInstanceId 实例id（更新条件）
     * @param mapData   具体更新的数据， key:字段名，value:数据
     * @return
     */
    int updateByInstanceId(@Param("tableName") String tableName, @Param("formInstanceId") String formInstanceId, @Param("mapData") Map<String, String> mapData);

    /**
     * 根据实例id逻辑删除数据
     * @param tableName         表名
     * @param formInstanceId    表单实例id
     * @return
     */
    int logicDelete(@Param("tableName") String tableName, @Param("formInstanceId") String formInstanceId);
}
