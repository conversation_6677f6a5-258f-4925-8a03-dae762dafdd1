package com.wtyt.lgfesentryedamanagement.dao.mapper;

import com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaMaster;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.EdaMasterListParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @date 2024/1/29 16:29
* @vesion 1.0
* @desc
*/
public interface TBurypointsEdaMasterMapper {
    int deleteByPrimaryKey(Long burypointsEdaMasterId);

    void deleteByEdaNo(String edaNo);

    int insert(TBurypointsEdaMaster record);

    int insertSelective(TBurypointsEdaMaster record);

    TBurypointsEdaMaster selectByPrimaryKey(Long burypointsEdaMasterId);

    int updateByPrimaryKeySelective(TBurypointsEdaMaster record);

    int updateByPrimaryKey(TBurypointsEdaMaster record);

    TBurypointsEdaMaster selectByEdaNo(String edaNo);
    TBurypointsEdaMaster selectByOptLinkId(String optLinkId);

    TBurypointsEdaMaster selectByEdaName(String edaName);

    List<TBurypointsEdaMaster> edaMasterList(EdaMasterListParam param);

    /**
     * 通过edaNo列表查询master表数据
     * @param edaNoList
     * @return
     */
    List<TBurypointsEdaMaster> queryByEdaNoList(@Param("list") List<String> edaNoList, @Param("isDel") String isDel);


    /**
     * 通过EDA编号删除数据
     * @param asList
     */
    void removeByEdaNoList(List<String> asList);
}