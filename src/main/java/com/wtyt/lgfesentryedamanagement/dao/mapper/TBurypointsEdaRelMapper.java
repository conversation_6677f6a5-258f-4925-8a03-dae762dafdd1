package com.wtyt.lgfesentryedamanagement.dao.mapper;

import com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEda;
import com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaRel;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545025IBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.response.Res5545027OBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * T_BURYPOINTS_EDA_REL
 */
public interface TBurypointsEdaRelMapper {

    List<TBurypointsEdaRel> queryByIdList(@Param("list") List<Long> burypointsEdaRelIdList, @Param("isDel") String isDel);

    void updateByPrimaryKey(TBurypointsEdaRel burypointsEdaRel);

    void insert(TBurypointsEdaRel burypointsEdaRel);

    List<TBurypointsEdaRel> queryByEdaAndBranchList(@Param("edaNo") String edaNo, @Param("list") List<TBurypointsEda> list, @Param("isDel") String isDel);

    List<TBurypointsEdaRel> selectByParams(Req5545025IBean param);

    Res5545027OBean getEdaRelInfo(@Param("burypointsEdaRelId") String burypointsEdaRelId);

    List<TBurypointsEdaRel> selectByIds(@Param("burypointsEdaRelIds") List<String> burypointsEdaRelIds);

    void deleteByIds(@Param("burypointsEdaRelIds") List<Long> burypointsEdaRelIds);

    List<TBurypointsEdaRel> selectByEdaBranch(@Param("edaBranches") List<String> edaBranches);
}