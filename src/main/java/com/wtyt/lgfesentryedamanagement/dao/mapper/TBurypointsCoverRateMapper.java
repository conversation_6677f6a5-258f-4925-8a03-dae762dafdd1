package com.wtyt.lgfesentryedamanagement.dao.mapper;

import com.wtyt.lgfesentryedamanagement.bkc.bean.response.Req5545032OBean;
import com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsCoverRate;
import com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsDetect;

import java.util.List;

/**
 *
 */
public interface TBurypointsCoverRateMapper {

    void insertCoverRate(TBurypointsCoverRate coverRateBean);

    void updateCoverRate(TBurypointsCoverRate tBurypointsCoverRate);
    void updateDetected(TBurypointsDetect tdDetect);

    List<TBurypointsCoverRate> queryCoverRateList(String optUserId);

    void insertDetected(TBurypointsDetect tdDetect);

    List<Req5545032OBean> queryDetectedList(String optLinkId);

    Req5545032OBean queryDetectedDetail(String searchId);

}