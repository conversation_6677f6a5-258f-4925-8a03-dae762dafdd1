package com.wtyt.lgfesentryedamanagement.dao.mapper;

import com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaCoverBk;
import com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaCoverFt;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TBurypointsEdaCoverFtMapper {

    /**
     * 批量插入
     * @param tBurypointsEdaCoverFtList
     */
    int batchInsert(@Param("list") List<TBurypointsEdaCoverFt> tBurypointsEdaCoverFtList);
    /**
     * 根据操作链路id删除已有数据
     * @param optLinkId
     */
    int delByOptLinkId(@Param("optLinkId") String optLinkId);

    /**
     * 查询配置列表
     * @param burypointsEdaPointsIdList
     * @return
     */
    List<TBurypointsEdaCoverFt> queryList(List<String> burypointsEdaPointsIdList);
}
