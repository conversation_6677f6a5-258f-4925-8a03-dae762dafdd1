package com.wtyt.lgfesentryedamanagement.dao.mapper;

import com.wtyt.lgfesentryedamanagement.dao.bean.TBuryPointsEleField;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TBuryPointsEleFieldMapper {


    /**
     * 批量插入
     * @param tBuryPointsEleFieldList
     */
    int batchInsert(@Param("list") List<TBuryPointsEleField> tBuryPointsEleFieldList);

    int delByOptLinkId(@Param("optLinkId") String optLinkId, @Param("relType") String relType);
}
