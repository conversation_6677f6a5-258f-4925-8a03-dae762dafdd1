package com.wtyt.lgfesentryedamanagement.dao.mapper;

import com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsBizTypeCfg;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface TBurypointsBizTypeCfgMapper {
    TBurypointsBizTypeCfg getBizTypeBySingle(@Param("env") int env, @Param("hasTable") int hasTable);

    TBurypointsBizTypeCfg getBizType(@Param("startEnv")int startEnv, @Param("startHasTable")int startHasTable,
                                     @Param("endEnv")int endEnv, @Param("endHasTable")int endHasTable);
}
