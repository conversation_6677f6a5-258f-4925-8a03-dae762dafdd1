package com.wtyt.lgfesentryedamanagement.dao.mapper;
import com.wtyt.lgfesentryedamanagement.dao.bean.NewEdaBranchPointsExtFields;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface NewEdaBranchPointsExtFieldsMapper {
    /**
     * 批量插入
     * @param newEdaBranchPointsExtFieldsList
     */
    int batchInsert(@Param("list") List<NewEdaBranchPointsExtFields> newEdaBranchPointsExtFieldsList);

    /**
     * 根据操id删除数据
     * @param optLinkId
     */
    int delByOptLinkId(@Param("optLinkId") String optLinkId);
}
