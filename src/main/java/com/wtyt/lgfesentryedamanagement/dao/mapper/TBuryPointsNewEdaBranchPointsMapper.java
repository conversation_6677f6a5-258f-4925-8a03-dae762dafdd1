package com.wtyt.lgfesentryedamanagement.dao.mapper;

import com.wtyt.lgfesentryedamanagement.dao.bean.TBuryPointsNewEdaBranchPoints;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TBuryPointsNewEdaBranchPointsMapper {


    /**
     * 批量插入
     * @param tBuryPointsEdaPointsList
     */
    int batchInsert(@Param("list") List<TBuryPointsNewEdaBranchPoints> tBuryPointsEdaPointsList);

    /**
     * 根据optLinkId查询
     * @param optLinkId
     * @return
     */
    List<TBuryPointsNewEdaBranchPoints> selectBranchAndPointsInfo(@Param("optLinkId") String optLinkId);

    /**
     * 根据optLinkId删除分支关联表数据
     * @param optLinkId
     */
    int delByOptLinkId(@Param("optLinkId") String optLinkId);

    Long selectOneEleColumnId(@Param("optLinkId") String optLinkId,@Param("branchPoint") TBuryPointsNewEdaBranchPoints branchPoint); // 新增方法，用于根据optLinkId查询记录数，用于后续的判断逻辑
}
