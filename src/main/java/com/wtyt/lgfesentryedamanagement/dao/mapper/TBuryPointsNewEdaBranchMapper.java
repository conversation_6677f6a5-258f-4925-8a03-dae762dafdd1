package com.wtyt.lgfesentryedamanagement.dao.mapper;

import com.wtyt.lgfesentryedamanagement.dao.bean.TBuryPointsNewEdaBranch;
import com.wtyt.lgfesentryedamanagement.eda.bean.response.BurypointsNewEdaBranchAndPointsInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TBuryPointsNewEdaBranchMapper {


    /**
     * 批量插入
     * @param tBuryPointsNewEdaBrabchList
     */
    int batchInsert(@Param("list") List<TBuryPointsNewEdaBranch> tBuryPointsNewEdaBrabchList);

    /**
     * 根据操作链路id删除已有数据
     * @param optLinkId
     */
    int delByOptLinkId(@Param("optLinkId") String optLinkId);

    /**
     * 根据操作链路id查询操作链路分支ID
     * @param optLinkId
     * @return
     * */
    List<Long> selectEdaBranchIdsByOptLinkId(@Param("optLinkId") String optLinkId);

    /**
     * 根据操作链路id查询操作链路分支明细
     * @param optLinkId
     * @return
     * */
    List<BurypointsNewEdaBranchAndPointsInfo> selectBranchAndPointsInfo(@Param("optLinkId") String optLinkId);
}
