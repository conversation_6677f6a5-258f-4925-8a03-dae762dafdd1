package com.wtyt.lgfesentryedamanagement.dao.mapper;

import com.wtyt.lgfesentryedamanagement.bkc.bean.BkcEdaConfBean;
import com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaCoverBk;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TBurypointsEdaCoverBkMapper {

    /**
     * 批量插入
     * @param tBurypointsEdaCoverBkList
     */
    int batchInsert(@Param("list") List<TBurypointsEdaCoverBk> tBurypointsEdaCoverBkList);
    /**
     * 根据操作链路id删除已有数据
     * @param optLinkId
     */
    int delByOptLinkId(@Param("optLinkId") String optLinkId);


    List<BkcEdaConfBean> queryBkConfigList(@Param("optLinkId") String optLinkId);

    List<BkcEdaConfBean> queryAllBkConfigList();

    List<BkcEdaConfBean> queryImproveBkConfigList(String queryDate);
}
