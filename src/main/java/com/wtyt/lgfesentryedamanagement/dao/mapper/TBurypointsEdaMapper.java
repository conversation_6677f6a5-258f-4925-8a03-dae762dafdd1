package com.wtyt.lgfesentryedamanagement.dao.mapper;

import com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEda;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/29 16:28
 * @vesion 1.0
 * @desc
 */
public interface TBurypointsEdaMapper {
    int deleteByPrimaryKey(Long burypointsEdaId);

    int deleteByEdaNo(String edaNo);

    int deleteByEdaNos(@Param("edaNos") List<String> edaNos);

    int insert(TBurypointsEda record);

    int insertSelective(TBurypointsEda record);

    TBurypointsEda selectByPrimaryKey(Long burypointsEdaId);

    int updateByPrimaryKeySelective(TBurypointsEda record);

    int updateByPrimaryKey(TBurypointsEda record);

    List<TBurypointsEda> queryByEdaNoList(@Param("list") List<String> burypointsEdaRelIdList, @Param("isDel") String isDel);

    List<TBurypointsEda> queryByEdaParentNo(@Param("edaParentNo") String edaParentNo, @Param("isDel") String isDel);

    @Select("select count(1) from T_BURYPOINTS_EDA where EDA_NAME = #{edaBranchName} and EDA_NO != #{edaBranch} AND IS_DEL = 0")
    int existOther(@Param("edaBranch") String edaBranch, @Param("edaBranchName") String edaBranchName);

    TBurypointsEda selectByEdaNo(String edaNo);

}