package com.wtyt.lgfesentryedamanagement.dao.mapper;

import com.wtyt.lgfesentryedamanagement.dao.bean.TBuryPointsSearchTimelinessClosedloop;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545043IBean;

import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface TBurypointsSearchTimelinessClosedloopMapper {

    /**
     * 批量插入
     * @param tBuryPointsSearchTimelinessClosedloopList
     */
    int batchInsert(@Param("list") List<TBuryPointsSearchTimelinessClosedloop> tBuryPointsSearchTimelinessClosedloopList);

    /**
     * 根据操作链路id查询结果
     * @param optLinkId
     * @return
     * */
    List<TBuryPointsSearchTimelinessClosedloop> getSearchListByOptLinkId(@Param("bean") Req5545043IBean bean);

    /**
     * 根据id修改查询结果
     * @param seTimelinessClosedloopId
     * @param searchState 查询状态 0：查询中 1：查询成功 2：查询失败
     * @param searchResult
     * @param searchFailInfo
     * @return
     * */
   int updateOneSearchResultById(@Param("seTimelinessClosedloopId") Long seTimelinessClosedloopId,@Param("searchState") Integer searchState,@Param("searchResult") String searchResult,@Param("searchFailInfo") String searchFailInfo);
}
