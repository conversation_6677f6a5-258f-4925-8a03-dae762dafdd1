package com.wtyt.lgfesentryedamanagement.dao.mapper;

import com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaSyncLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * T_BURYPOINTS_EDA_SYNC_LOG
 */
public interface TBurypointsEdaSyncLogMapper {


    List<TBurypointsEdaSyncLog> queryByList(@Param("syncTableName") String syncTableName, @Param("env") String env, @Param("burypointsEdaRelIdList") List<Long> burypointsEdaRelIdList);

    void saveSyncLog(List<TBurypointsEdaSyncLog> syncList);
}