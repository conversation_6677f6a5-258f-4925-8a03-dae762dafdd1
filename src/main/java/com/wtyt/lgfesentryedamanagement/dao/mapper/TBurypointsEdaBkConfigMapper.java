package com.wtyt.lgfesentryedamanagement.dao.mapper;

import com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaBkConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @date 2024/1/29 16:29
* @vesion 1.0
* @desc 
*/
public interface TBurypointsEdaBkConfigMapper {

    int insert(TBurypointsEdaBkConfig record);

    int insertFt(TBurypointsEdaBkConfig record);

    int insertSelective(TBurypointsEdaBkConfig record);

    int insertSelectiveFt(TBurypointsEdaBkConfig record);

    TBurypointsEdaBkConfig selectByPrimaryKey(Long burypointsEdaBkConfigId);

    int updateByPrimaryKeySelective(TBurypointsEdaBkConfig record);

    int updateByPrimaryKeySelectiveFt(TBurypointsEdaBkConfig record);

    int removeByPrimaryKeySelective(Long burypointsEdaBkConfigId);

    int removeByPrimaryKeySelectiveFt(Long burypointsEdaBkConfigId);

    List<TBurypointsEdaBkConfig> queryByIdList(@Param("list") List<Long> burypointsEdaBkConfigIdList, @Param("isDel") String isDel);

    List<TBurypointsEdaBkConfig> queryByEdaNo(@Param("edaNo") String edaNo, @Param("isDel") String isDel);
}