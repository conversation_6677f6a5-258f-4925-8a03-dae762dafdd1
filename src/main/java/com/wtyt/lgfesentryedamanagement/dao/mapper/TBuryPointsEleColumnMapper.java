package com.wtyt.lgfesentryedamanagement.dao.mapper;

import com.wtyt.lgfesentryedamanagement.dao.bean.TBuryPointsEleColumn;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TBuryPointsEleColumnMapper {


    /**
     * 批量插入
     * @param tBuryPointsEleColumnList
     */
    int batchInsert(@Param("list") List<TBuryPointsEleColumn> tBuryPointsEleColumnList);

    int delByPointsOptLinkId(@Param("optLinkId") String optLinkId);

    int delByCoverOptLinkId(@Param("optLinkId") String optLinkId);
}
