package com.wtyt.lgfesentryedamanagement.cfg.controller;

import com.wtyt.lg.commons.bean.BaseBean;
import com.wtyt.lg.commons.bean.ResDataBean;
import com.wtyt.lgfesentryedamanagement.cfg.bean.param.CommonConfigParam;
import com.wtyt.lgfesentryedamanagement.cfg.bean.vo.CommonConfigVo;
import com.wtyt.lgfesentryedamanagement.cfg.service.CfgQueryService;
import com.wtyt.lgfesentryedamanagement.pub.annotation.Attribute;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.CommonToolkit;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.ValidateFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.groups.Default;

/**
 * <AUTHOR>
 * @date 2024/1/31 15:25
 * @vesion 1.0
 * @desc
 */
@RestController
@RequestMapping(value = "/cfg/query/", method = RequestMethod.POST)
public class CfgQueryController {

    @Autowired
    private CfgQueryService cfgQueryService;


    /**
     * 5545010-获取指定公用配置信息
     * @param data
     * @return
     */
    @RequestMapping(value = "commonConfig", method = RequestMethod.POST)
    @Attribute(sid = "5545010", name = "获取指定公用配置信息")
    public ResDataBean<CommonConfigVo> getCommonConfig(@RequestBody BaseBean<CommonConfigParam> data){
        CommonConfigParam commonConfigParam = CommonToolkit.checkReq(data);
        ValidateFilter.getFilterMessage(commonConfigParam, Default.class);
        return new ResDataBean<CommonConfigVo>().success(cfgQueryService.getCommonConfig(commonConfigParam));
    }
}
