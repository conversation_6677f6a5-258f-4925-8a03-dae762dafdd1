package com.wtyt.lgfesentryedamanagement.cfg.service;

import com.ctrip.framework.apollo.ConfigService;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.wtyt.lgfesentryedamanagement.cfg.bean.ConfigParamJsonDeserializer;
import com.wtyt.lgfesentryedamanagement.cfg.bean.param.ApolloConfigParam;
import com.wtyt.lgfesentryedamanagement.cfg.bean.param.CommonConfigParam;
import com.wtyt.lgfesentryedamanagement.cfg.bean.param.SourceConfigParam;
import com.wtyt.lgfesentryedamanagement.cfg.bean.vo.CommonConfigVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/1/31 15:42
 * @vesion 1.0
 * @desc
 */
@Service
public class CfgQueryService implements InitializingBean {
    private Gson gson;

    /**
     * 获取指定公用配置信息
     * @param bean
     * @return CommonConfigVo
     */
    public CommonConfigVo getCommonConfig(CommonConfigParam bean) {
        List<CommonConfigVo> commonConfigVos = new ArrayList<>();
        //获取配置枚举
        String commonConfigEnum = ConfigService.getAppConfig().getProperty("sourceConfig", "");
        if (StringUtils.isNotBlank(commonConfigEnum)) {
            Map<String, SourceConfigParam> configMap = gson.fromJson(commonConfigEnum, new TypeToken<HashMap<String, SourceConfigParam>>() {
            }.getType());
            //取枚举值
            bean.getTypes().forEach(e -> {
                SourceConfigParam configParam = configMap.get(e);
                if (configParam != null) {
                    if ("apollo".equals(configParam.getSource())) {
                        ApolloConfigParam apolloConfigParam = (ApolloConfigParam) configParam;
                        String value = ConfigService.getConfig(apolloConfigParam.getNamespace()).getProperty(apolloConfigParam.getKey(), "");
                        commonConfigVos.add(new CommonConfigVo(e, value));
                    }
                }
            });
        }
        return new CommonConfigVo(commonConfigVos);
    }

    @Override
    public void afterPropertiesSet() {
        gson = new GsonBuilder().registerTypeAdapter(SourceConfigParam.class, new ConfigParamJsonDeserializer()).disableHtmlEscaping().create();
    }
}
