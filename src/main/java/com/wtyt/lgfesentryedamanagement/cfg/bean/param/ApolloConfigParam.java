package com.wtyt.lgfesentryedamanagement.cfg.bean.param;

import java.io.Serializable;

public class ApolloConfigParam extends SourceConfigParam implements Serializable {
    private static final long serialVersionUID = -515478448528431968L;

    private String namespace;

    private String key;


    public ApolloConfigParam(String namespace, String key) {
        super("apollo");
        this.namespace = namespace;
        this.key = key;
    }

    public String getNamespace() {
        return namespace;
    }

    public void setNamespace(String namespace) {
        this.namespace = namespace;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }
}
