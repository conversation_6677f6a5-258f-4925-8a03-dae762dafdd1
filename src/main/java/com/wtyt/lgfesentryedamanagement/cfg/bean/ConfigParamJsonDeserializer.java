package com.wtyt.lgfesentryedamanagement.cfg.bean;

import com.google.gson.*;
import com.wtyt.lgfesentryedamanagement.cfg.bean.param.ApolloConfigParam;
import com.wtyt.lgfesentryedamanagement.cfg.bean.param.SourceConfigParam;

import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @since 2023/3/17 10:52
 */
public class ConfigParamJsonDeserializer implements JsonDeserializer<SourceConfigParam> {


    @Override
    public SourceConfigParam deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
        JsonObject jsonObject = json.getAsJsonObject();
        String source = jsonObject.get("source").getAsString();
        if ("apollo".equals(source)) {
            String namespace = jsonObject.get("namespace").getAsString();
            String key = jsonObject.get("key").getAsString();
            return new ApolloConfigParam(namespace, key);
        } else{
            throw new JsonParseException("Unknown source: " + source);
        }
    }
}
