package com.wtyt.lgfesentryedamanagement.cfg.bean.param;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import javax.validation.groups.Default;
import java.io.Serializable;
import java.util.List;

public class CommonConfigParam implements Serializable {
    private static final long serialVersionUID = 1780270706765897245L;

    @NotEmpty(message = "配置类型不能为空", groups = {Default.class})
    @Size(message = "配置类型不能超过10", max = 10, groups = {Default.class})
    private List<
            @NotBlank(message = "配置类型不能为空", groups = {Default.class})
            @Size(message = "配置类型过长", max = 2, groups = {Default.class})
            @Pattern(regexp = "^[0-9]*$", message = "配置类型只能是数字", groups = {Default.class}) String> types;


    public List<String> getTypes() {
        return types;
    }

    public void setTypes(List<String> types) {
        this.types = types;
    }
}
