package com.wtyt.lgfesentryedamanagement.log.bean;

/**
 * <AUTHOR>
 * @date 2024/1/29 19:20
 * @vesion 1.0
 * @desc
 */
public class LogUnifySaveBean {
    private Object originalBean;
    /**
     * 修改后bean
     */
    private Object currentBean;

    /**
     * 日志类型 ,3:删除EDA，逻辑删除,2:修改EDA,1:新增EDA
     */
    private Integer logType;

    /**
     * 操作类型:1：EDA分支管理，2:EDA管理，3：EDA后端覆盖率管理 ,3:EDA后端覆盖率配置管理,2:EDA管理,1:EDA分支管理
     */
    private Integer optType;

    /**
     * 操作的团队名称
     */
    private String optTeam;

    /**
     * 操作人
     */
    private String optUserName;

    /**
     * 操作的EDA分支编号/EDA编号/EDA配置id
     */
    private String edaBranchNo;


    public Object getOriginalBean() {
        return originalBean;
    }

    public void setOriginalBean(Object originalBean) {
        this.originalBean = originalBean;
    }

    public Object getCurrentBean() {
        return currentBean;
    }

    public void setCurrentBean(Object currentBean) {
        this.currentBean = currentBean;
    }

    public Integer getLogType() {
        return logType;
    }

    public void setLogType(Integer logType) {
        this.logType = logType;
    }

    public Integer getOptType() {
        return optType;
    }

    public void setOptType(Integer optType) {
        this.optType = optType;
    }

    public String getOptTeam() {
        return optTeam;
    }

    public void setOptTeam(String optTeam) {
        this.optTeam = optTeam;
    }

    public String getOptUserName() {
        return optUserName;
    }

    public void setOptUserName(String optUserName) {
        this.optUserName = optUserName;
    }

    public String getEdaBranchNo() {
        return edaBranchNo;
    }

    public void setEdaBranchNo(String edaBranchNo) {
        this.edaBranchNo = edaBranchNo;
    }
}
