package com.wtyt.lgfesentryedamanagement.log.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.wtyt.generator.toolkit.UidToolkit;
import com.wtyt.lg.commons.exception.BaseTipException;
import com.wtyt.lgfesentryedamanagement.log.bean.LogUnifySaveBean;
import com.wtyt.lgfesentryedamanagement.pub.enums.LogBusiTypeEnum;
import com.wtyt.lgfesentryedamanagement.pub.enums.LogOperTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaLog;
import com.wtyt.lgfesentryedamanagement.dao.mapper.TBurypointsEdaLogMapper;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/1/29 16:29
 * @vesion 1.0
 * @desc
 */
@Service
public class BurypointsEdaLogService {

    public static final Set<String> BIG_DECIMAL_SET = new HashSet<>();

    public static final Set<String> DATA_TIME_SET = new HashSet<>();

    //忽略的字段
    public static final Set<String> IGNORE_FIELD_SET = new HashSet<>();


    static {
        //这些字段是数字类型
        //忽略字段
        //时间字段
        DATA_TIME_SET.add("createdTime");
        DATA_TIME_SET.add("lastModifiedTime");
    }


    private static final Logger log = LoggerFactory.getLogger(BurypointsEdaLogService.class);
    @Resource
    private TBurypointsEdaLogMapper tBurypointsEdaLogMapper;


    public void addLog(LogUnifySaveBean logUnifySaveBean) throws BaseTipException {

        StringBuilder result = new StringBuilder();
        result.append(LogBusiTypeEnum.getLogTableName(logUnifySaveBean.getOptType())).append(":").append(LogOperTypeEnum.getByValue(logUnifySaveBean.getLogType()).getName()).append(".");
        if (LogOperTypeEnum.EDIT.getValue().equals(logUnifySaveBean.getLogType())) {
            Map<String, Object> oMap;
            if (logUnifySaveBean.getOriginalBean() != null) {
                oMap = BeanUtil.beanToMap(logUnifySaveBean.getOriginalBean(), false, true);
            } else {
                oMap = new HashMap<>();
            }
            Map<String, Object> cMap = BeanUtil.beanToMap(logUnifySaveBean.getCurrentBean(), false, true);
            cMap.forEach((k, v) -> {

                //null也写入
                Object o = oMap.get(k);
                if (o == null && StringUtils.isBlank(v.toString())) {
                    //新老值都是空
                    return;
                }
                try {
                    //忽略字段
                    if (IGNORE_FIELD_SET.contains(k)) {
                        return;
                    }
                    if (o != null && StringUtils.isNotBlank(o.toString()) && StringUtils.isNotBlank(v.toString())) {
                        //数字字段
                        if ((BIG_DECIMAL_SET.contains(k))
                                && (new BigDecimal(v.toString()).compareTo(new BigDecimal(o.toString())) == 0)) {
                            return;
                        }
                        //时间字段
                        if (DATA_TIME_SET.contains(k) &&
                                DateUtil.compare(DateUtil.parse(o.toString()), DateUtil.parse(v.toString())) == 0) {
                            return;
                        }
                    }



                    if (v instanceof BigDecimal) {
                        v = ((BigDecimal) v).stripTrailingZeros().toPlainString();
                    }
                    if (o instanceof BigDecimal) {
                        o = ((BigDecimal) o).stripTrailingZeros().toPlainString();
                    }
                    if (v.equals(o)) {
                        //相同不存
                        return;
                    }

                    result.append("字段:").append(k).append(",").append("现值:").append(JSONUtil.toJsonStr(v))
                            .append(",").append("原值:").append(JSONUtil.toJsonStr(o)).append(".");
                } catch (Exception e) {
                    log.error(e.getMessage(), e.getMessage());
                }
            });
        }


        TBurypointsEdaLog tBurypointsEdaLog = new TBurypointsEdaLog();
        tBurypointsEdaLog.setBurypointsEdaLogId(UidToolkit.generateUidDefault());
        BeanUtil.copyProperties(logUnifySaveBean, tBurypointsEdaLog);
        if (result.length() >= 512) {
            tBurypointsEdaLog.setOptNote(result.substring(0, 512));
        } else {
            tBurypointsEdaLog.setOptNote(result.toString());
        }

        this.insertSelective(tBurypointsEdaLog);
    }

    public int insertSelective(TBurypointsEdaLog record) {
        return tBurypointsEdaLogMapper.insertSelective(record);
    }

}
