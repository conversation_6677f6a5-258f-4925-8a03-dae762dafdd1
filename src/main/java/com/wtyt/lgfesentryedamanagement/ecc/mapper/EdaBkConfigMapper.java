package com.wtyt.lgfesentryedamanagement.ecc.mapper;

import com.wtyt.lgfesentryedamanagement.ecc.bean.do_.EdaBkConfigDo;
import com.wtyt.lgfesentryedamanagement.ecc.bean.param.EdaBkConfigListParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/31 10:03
 * @vesion 1.0
 * @desc 后端覆盖率关联查询
 */
public interface EdaBkConfigMapper {
    List<EdaBkConfigDo> edaBkConfigList(EdaBkConfigListParam edaBkConfigListParam);
}
