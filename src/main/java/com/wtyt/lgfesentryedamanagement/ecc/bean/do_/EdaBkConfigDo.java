package com.wtyt.lgfesentryedamanagement.ecc.bean.do_;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/1/31 10:43
 * @vesion 1.0
 * @desc
 */
public class EdaBkConfigDo implements Serializable {

    private static final long serialVersionUID = 3123793452341710552L;
    private String burypointsEdaBkConfigId;// 配置id
    private String edaNo;//EDA编号
    private String edaName;//EDA模块名
    private String edaAbbreveName;//EDA名称缩写
    private String interfaceProjectName;//接口所在项目
    private String interfaceType;//接口类型：structs:3,springmvc:2,卡友地带网关:1,金融网关:0
    private String interfaceTag;//接口标识
    private String interfaceIndexName;//索引名
    private String maintenanceTeam;//维护团队
    private String calType;//统计的数值计算类型 ,减法计算类型:1,加法计算类型:0
    private String filterType;//当前的过滤类型 ,从接口全埋中过滤:1,以日志方式过滤:0
    private String interfaceFilters;//接口类型的过滤条件
    private String linkLocation;//链路位置
    private String edaBranch;//EDA分支埋点元数据ID
    private String edaBranchName;//EDA分支名称
    private String isDel;//数据状态 0-未删除 1-已删除

    private String configType;//覆盖率类型

    private String eleResourceId;//埋点元素id

    private String eleExtField;//元素扩展字段里的翻译字段

    private String eleExtFieldVal;//元素扩展字段里的翻译字段值


    public String getBurypointsEdaBkConfigId() {
        return burypointsEdaBkConfigId;
    }

    public void setBurypointsEdaBkConfigId(String burypointsEdaBkConfigId) {
        this.burypointsEdaBkConfigId = burypointsEdaBkConfigId;
    }

    public String getEdaNo() {
        return edaNo;
    }

    public void setEdaNo(String edaNo) {
        this.edaNo = edaNo;
    }

    public String getEdaName() {
        return edaName;
    }

    public void setEdaName(String edaName) {
        this.edaName = edaName;
    }

    public String getEdaAbbreveName() {
        return edaAbbreveName;
    }

    public void setEdaAbbreveName(String edaAbbreveName) {
        this.edaAbbreveName = edaAbbreveName;
    }

    public String getInterfaceProjectName() {
        return interfaceProjectName;
    }

    public void setInterfaceProjectName(String interfaceProjectName) {
        this.interfaceProjectName = interfaceProjectName;
    }

    public String getInterfaceType() {
        return interfaceType;
    }

    public void setInterfaceType(String interfaceType) {
        this.interfaceType = interfaceType;
    }

    public String getInterfaceTag() {
        return interfaceTag;
    }

    public void setInterfaceTag(String interfaceTag) {
        this.interfaceTag = interfaceTag;
    }

    public String getInterfaceIndexName() {
        return interfaceIndexName;
    }

    public void setInterfaceIndexName(String interfaceIndexName) {
        this.interfaceIndexName = interfaceIndexName;
    }

    public String getMaintenanceTeam() {
        return maintenanceTeam;
    }

    public void setMaintenanceTeam(String maintenanceTeam) {
        this.maintenanceTeam = maintenanceTeam;
    }

    public String getCalType() {
        return calType;
    }

    public void setCalType(String calType) {
        this.calType = calType;
    }

    public String getFilterType() {
        return filterType;
    }

    public void setFilterType(String filterType) {
        this.filterType = filterType;
    }

    public String getInterfaceFilters() {
        return interfaceFilters;
    }

    public void setInterfaceFilters(String interfaceFilters) {
        this.interfaceFilters = interfaceFilters;
    }

    public String getLinkLocation() {
        return linkLocation;
    }

    public void setLinkLocation(String linkLocation) {
        this.linkLocation = linkLocation;
    }

    public String getEdaBranch() {
        return edaBranch;
    }

    public void setEdaBranch(String edaBranch) {
        this.edaBranch = edaBranch;
    }

    public String getEdaBranchName() {
        return edaBranchName;
    }

    public void setEdaBranchName(String edaBranchName) {
        this.edaBranchName = edaBranchName;
    }

    public String getIsDel() {
        return isDel;
    }

    public void setIsDel(String isDel) {
        this.isDel = isDel;
    }

    public String getConfigType() {
        return configType;
    }

    public void setConfigType(String configType) {
        this.configType = configType;
    }

    public String getEleResourceId() {
        return eleResourceId;
    }

    public void setEleResourceId(String eleResourceId) {
        this.eleResourceId = eleResourceId;
    }

    public String getEleExtField() {
        return eleExtField;
    }

    public void setEleExtField(String eleExtField) {
        this.eleExtField = eleExtField;
    }

    public String getEleExtFieldVal() {
        return eleExtFieldVal;
    }

    public void setEleExtFieldVal(String eleExtFieldVal) {
        this.eleExtFieldVal = eleExtFieldVal;
    }
}
