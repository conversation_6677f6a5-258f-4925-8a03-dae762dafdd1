package com.wtyt.lgfesentryedamanagement.ecc.bean.param;

import cn.hutool.core.util.ObjectUtil;
import com.wtyt.lgfesentryedamanagement.pub.bean.PageParam;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.ValidateFilter;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import javax.validation.groups.Default;

/**
 * <AUTHOR>
 * @date 2024/1/31 9:36
 * @vesion 1.0
 * @desc
 */
public class EdaBkConfigListParam extends PageParam {
    private static final long serialVersionUID = 2860727823949561413L;
    @Size(message = "EDA编号过长", max = 64, groups = {Default.class})
    private String edaNo;//EDA编号
    @Size(message = "EDA中文名称过长", max = 64, groups = {Default.class})
    private String edaName;//eda名称
    @Size(message = "EDA简称过长", max = 64, groups = {Default.class})
    private String edaAbbreveName;//eda缩写名称
    @Size(message = "EDA分支编号过长", max = 64, groups = {Default.class})
    private String edaBranch;//eda分支编号
    @Size(message = "接口标识过长", max = 256, groups = {Default.class})
    private String interfaceTag;//接口标识
    @Size(message = "接口所在项目过长", max = 128, groups = {Default.class})
    private String interfaceProjectName;//接口所在项目
    private String maintenanceTeam;//维护团队
    @Pattern(message = "当前的链路位置不合法", regexp = "^|[0-9]*$", groups = {ValidateFilter.Create.class, ValidateFilter.Update.class})
    private String linkLocation;//链路位置
    private String isDel;//数据状态

    public String getEdaNo() {
        return edaNo;
    }

    public void setEdaNo(String edaNo) {
        this.edaNo = edaNo;
    }

    public String getEdaName() {
        return edaName;
    }

    public void setEdaName(String edaName) {
        this.edaName = edaName;
    }

    public String getEdaAbbreveName() {
        return edaAbbreveName;
    }

    public void setEdaAbbreveName(String edaAbbreveName) {
        this.edaAbbreveName = edaAbbreveName;
    }

    public String getInterfaceTag() {
        return interfaceTag;
    }

    public void setInterfaceTag(String interfaceTag) {
        this.interfaceTag = interfaceTag;
    }

    public String getInterfaceProjectName() {
        return interfaceProjectName;
    }

    public void setInterfaceProjectName(String interfaceProjectName) {
        this.interfaceProjectName = interfaceProjectName;
    }

    public String getMaintenanceTeam() {
        return maintenanceTeam;
    }

    public void setMaintenanceTeam(String maintenanceTeam) {
        this.maintenanceTeam = maintenanceTeam;
    }

    public String getLinkLocation() {
        return linkLocation;
    }

    public void setLinkLocation(String linkLocation) {
        this.linkLocation = linkLocation;
    }

    public String getIsDel() {
        return this.isDel;
    }

    public void setIsDel(String isDel) {
        this.isDel = isDel;
    }

    public String getEdaBranch() {
        return edaBranch;
    }

    public void setEdaBranch(String edaBranch) {
        this.edaBranch = edaBranch;
    }
}
