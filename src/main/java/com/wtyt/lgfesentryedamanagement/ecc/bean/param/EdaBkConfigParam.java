package com.wtyt.lgfesentryedamanagement.ecc.bean.param;

import cn.hutool.core.lang.Assert;
import com.google.gson.Gson;
import com.wtyt.generator.toolkit.UidToolkit;
import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaBkConfig;
import com.wtyt.lgfesentryedamanagement.pub.bean.BaseTokenBean;
import com.wtyt.lgfesentryedamanagement.pub.exceptions.ExceptionSupplier;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.ValidateFilter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/1/30 15:16
 * @vesion 1.0
 * @desc
 */
public class EdaBkConfigParam extends BaseTokenBean {
    private static final long serialVersionUID = -1422082811115976798L;
    @NotEmpty(message = "EDA编号不能为空", groups = {ValidateFilter.Create.class})
    @Size(message = "EDA编号过长", max = 64, groups = {ValidateFilter.Create.class})
    private String edaNo;//eda编号
    //@NotEmpty(message = "接口标识不能为空", groups = {ValidateFilter.Create.class})
    @Size(message = "接口标识过长", max = 256, groups = {ValidateFilter.Create.class, ValidateFilter.Update.class})
    private String interfaceTag;//接口标识
    //@NotEmpty(message = "接口所在项目不能为空", groups = {ValidateFilter.Create.class})
    @Size(message = "接口所在项目过长", max = 128, groups = {ValidateFilter.Create.class, ValidateFilter.Update.class})
    private String interfaceProjectName;//接口所在项目
    //@NotEmpty(message = "接口类型不能为空", groups = {ValidateFilter.Create.class})
    @Pattern(message = "接口类型不合法", regexp = "^|[0-9]*$", groups = {ValidateFilter.Create.class, ValidateFilter.Update.class})
    private String interfaceType;//接口类型：structs:3,springmvc:2,卡友地带网关:1,金融网关:0
    //@NotEmpty(message = "索引名不能为空", groups = {ValidateFilter.Create.class})
    @Size(message = "索引名过长", max = 64, groups = {ValidateFilter.Create.class, ValidateFilter.Update.class})
    private String interfaceIndexName;//索引名
    private List<Object> interfaceFilters;//接口类型的过滤条件
    @NotEmpty(message = "配置id不能为空", groups = {ValidateFilter.Update.class, ValidateFilter.Delete.class})
    @Size(message = "配置id过长", max = 20, groups = {ValidateFilter.Update.class, ValidateFilter.Delete.class})
    private String burypointsEdaBkConfigId; //配置id

    @Pattern(message = "当前的过滤类型不合法", regexp = "^|[0-9]*$", groups = {ValidateFilter.Update.class})
    private String filterType;//当前的过滤类型 ,从接口全埋中过滤:1,以日志方式过滤:0

    @Pattern(message = "当前的链路位置不合法", regexp = "^|[0-9]*$", groups = {ValidateFilter.Create.class, ValidateFilter.Update.class})
    private String linkLocation;//链路位置
    @Size(message = "EDA分支编号过长", max = 64, groups = {ValidateFilter.Create.class, ValidateFilter.Update.class})
    private String edaBranch;//EDA分支编号

    private String configType = "1";//覆盖率类型

    private String eleResourceId;//埋点元素id

    private String eleExtField;//元素扩展字段里的翻译字段

    private String eleExtFieldVal;//元素扩展字段里的翻译字段值

    public String getEdaNo() {
        return edaNo;
    }

    public void setEdaNo(String edaNo) {
        this.edaNo = edaNo;
    }

    public String getInterfaceTag() {
        return interfaceTag;
    }

    public void setInterfaceTag(String interfaceTag) {
        this.interfaceTag = interfaceTag;
    }

    public String getInterfaceProjectName() {
        return interfaceProjectName;
    }

    public void setInterfaceProjectName(String interfaceProjectName) {
        this.interfaceProjectName = interfaceProjectName;
    }

    public String getInterfaceType() {
        return interfaceType;
    }

    public void setInterfaceType(String interfaceType) {
        this.interfaceType = interfaceType;
    }

    public String getInterfaceIndexName() {
        return interfaceIndexName;
    }

    public void setInterfaceIndexName(String interfaceIndexName) {
        this.interfaceIndexName = interfaceIndexName;
    }

    public List<Object> getInterfaceFilters() {
        return interfaceFilters;
    }

    public void setInterfaceFilters(List<Object> interfaceFilters) {
        this.interfaceFilters = interfaceFilters;
    }


    public String getBurypointsEdaBkConfigId() {
        return burypointsEdaBkConfigId;
    }

    public void setBurypointsEdaBkConfigId(String burypointsEdaBkConfigId) {
        this.burypointsEdaBkConfigId = burypointsEdaBkConfigId;
    }

    public String getFilterType() {
        return filterType;
    }

    public void setFilterType(String filterType) {
        this.filterType = filterType;
    }

    public String getLinkLocation() {
        return linkLocation;
    }

    public void setLinkLocation(String linkLocation) {
        this.linkLocation = linkLocation;
    }

    public String getEdaBranch() {
        return edaBranch;
    }

    public void setEdaBranch(String edaBranch) {
        this.edaBranch = edaBranch;
    }

    public String getConfigType() {
        return configType;
    }

    public void setConfigType(String configType) {
        this.configType = configType;
    }

    public String getEleResourceId() {
        return eleResourceId;
    }

    public void setEleResourceId(String eleResourceId) {
        this.eleResourceId = eleResourceId;
    }

    public String getEleExtField() {
        return eleExtField;
    }

    public void setEleExtField(String eleExtField) {
        this.eleExtField = eleExtField;
    }

    public String getEleExtFieldVal() {
        return eleExtFieldVal;
    }

    public void setEleExtFieldVal(String eleExtFieldVal) {
        this.eleExtFieldVal = eleExtFieldVal;
    }

    public static TBurypointsEdaBkConfig buildEntity(EdaBkConfigParam edaBkConfigParam) {
        TBurypointsEdaBkConfig burypointsEdaBkConfig = new TBurypointsEdaBkConfig();
        Long id = UidToolkit.generateUidDefault();
        burypointsEdaBkConfig.setBurypointsEdaBkConfigId(id);
        BeanUtils.copyProperties(edaBkConfigParam, burypointsEdaBkConfig);

        Gson gson = new Gson();
        Optional.ofNullable(edaBkConfigParam.getInterfaceType()).filter(StringUtils::isNotBlank).ifPresent(interfaceType -> burypointsEdaBkConfig.setInterfaceType(Integer.valueOf(interfaceType)));
        Optional.ofNullable(edaBkConfigParam.getInterfaceFilters()).ifPresent(interfaceFilters -> burypointsEdaBkConfig.setInterfaceFilters(gson.toJson(interfaceFilters)));
        burypointsEdaBkConfig.setFilterType(1);
        burypointsEdaBkConfig.setCalType(0);
        return burypointsEdaBkConfig;
    }


    public static void checkParam(EdaBkConfigParam edaBkConfigParam) {
        //以日志方式过滤,过滤条件不能修改
        if (StringUtils.isNotBlank(edaBkConfigParam.getFilterType()) && "0".equals(edaBkConfigParam.getFilterType())) {
            Assert.isNull(edaBkConfigParam.getInterfaceFilters(), new ExceptionSupplier("以日志方式过滤,过滤条件必须为空"));
        }
    }

    /**
     * 验证不同条件下的非空
     * @param edaBkConfigParam
     */
    public static void checkParamNotEmpty(EdaBkConfigParam edaBkConfigParam) {
        switch (edaBkConfigParam.getConfigType()) {
            case "1":
                if (StringUtils.isAnyBlank(edaBkConfigParam.getInterfaceTag(), edaBkConfigParam.getInterfaceProjectName(), edaBkConfigParam.getInterfaceType(), edaBkConfigParam.getInterfaceIndexName())) {
                    throw new UnifiedBusinessException("存在必填字段为空");
                }
                break;
            case "2":
                if (StringUtils.isBlank(edaBkConfigParam.getEleResourceId())) {
                    throw new UnifiedBusinessException("存在必填字段为空");
                }
                if ((StringUtils.isBlank(edaBkConfigParam.getEleExtField()) && StringUtils.isNotBlank(edaBkConfigParam.getEleExtFieldVal()))
                        || (StringUtils.isNotBlank(edaBkConfigParam.getEleExtField()) && StringUtils.isBlank(edaBkConfigParam.getEleExtFieldVal()))) {
                    throw new UnifiedBusinessException("拓展字段要么都存在，要么都不存在");
                }
                break;
            default:
                throw new UnifiedBusinessException("configType不合法");
        }
    }

    public static TBurypointsEdaBkConfig buildEntity(EdaBkConfigParam edaBkConfigParam, TBurypointsEdaBkConfig burypointsEdaBkConfigOld) {
        TBurypointsEdaBkConfig burypointsEdaBkConfigNew = new TBurypointsEdaBkConfig();
        BeanUtils.copyProperties(edaBkConfigParam, burypointsEdaBkConfigNew);
        burypointsEdaBkConfigNew.setBurypointsEdaBkConfigId(burypointsEdaBkConfigOld.getBurypointsEdaBkConfigId());
        burypointsEdaBkConfigNew.setEdaNo(burypointsEdaBkConfigOld.getEdaNo());

        if (Objects.equals("0", edaBkConfigParam.getFilterType())) {
            burypointsEdaBkConfigNew.setInterfaceFilters("[]");
        }

        Gson gson = new Gson();
        Optional.ofNullable(edaBkConfigParam.getInterfaceType()).filter(StringUtils::isNotBlank).ifPresent(interfaceType -> burypointsEdaBkConfigNew.setInterfaceType(Integer.valueOf(interfaceType)));
        Optional.ofNullable(edaBkConfigParam.getInterfaceFilters()).ifPresent(interfaceFilters -> burypointsEdaBkConfigNew.setInterfaceFilters(gson.toJson(interfaceFilters)));
        return burypointsEdaBkConfigNew;
    }


}
