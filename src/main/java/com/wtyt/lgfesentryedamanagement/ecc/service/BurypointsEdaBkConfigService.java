package com.wtyt.lgfesentryedamanagement.ecc.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ctrip.framework.apollo.ConfigService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wtyt.generator.toolkit.UidToolkit;
import com.wtyt.lg.commons.exception.BaseTipException;
import com.wtyt.lgfesentryedamanagement.ecc.bean.do_.EdaBkConfigDo;
import com.wtyt.lgfesentryedamanagement.ecc.bean.param.EdaBkConfigListParam;
import com.wtyt.lgfesentryedamanagement.ecc.bean.param.EdaBkConfigParam;
import com.wtyt.lgfesentryedamanagement.ecc.bean.vo.EdaBkConfigVo;
import com.wtyt.lgfesentryedamanagement.ecc.mapper.EdaBkConfigMapper;
import com.wtyt.lgfesentryedamanagement.eda.service.BurypointsEdaMasterService;
import com.wtyt.lgfesentryedamanagement.log.bean.LogUnifySaveBean;
import com.wtyt.lgfesentryedamanagement.log.service.BurypointsEdaLogService;
import com.wtyt.lgfesentryedamanagement.pub.bean.BaseTokenBean;
import com.wtyt.lgfesentryedamanagement.pub.bean.PageVo;
import com.wtyt.lgfesentryedamanagement.pub.consts.InterfaceFilterConsts;
import com.wtyt.lgfesentryedamanagement.pub.enums.LogBusiTypeEnum;
import com.wtyt.lgfesentryedamanagement.pub.enums.LogOperTypeEnum;
import com.wtyt.lgfesentryedamanagement.pub.exceptions.ExceptionSupplier;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.JsonToolkit;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.wtyt.lgfesentryedamanagement.dao.mapper.TBurypointsEdaBkConfigMapper;
import com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaBkConfig;

import java.io.IOException;
import java.text.MessageFormat;
import java.util.*;

import static com.wtyt.lgfesentryedamanagement.pub.consts.InterfaceFilterConsts.*;

/**
 * <AUTHOR>
 * @date 2024/1/29 16:29
 * @vesion 1.0
 * @desc
 */
@Service
public class BurypointsEdaBkConfigService {
    private static final Logger log = LoggerFactory.getLogger(BurypointsEdaBkConfigService.class);
    @Resource
    private TBurypointsEdaBkConfigMapper tBurypointsEdaBkConfigMapper;

    @Resource
    private BurypointsEdaMasterService burypointsEdaMasterService;

    @Resource
    private BurypointsEdaLogService burypointsEdaLogService;

    @Resource
    private EdaBkConfigMapper edaBkConfigMapper;

    /**
     * EDA后端覆盖率配置新增
     * @param edaBkConfigParam
     * @throws BaseTipException
     */
    public void addEdaBkConfig(EdaBkConfigParam edaBkConfigParam) throws BaseTipException {
        //做下版本兼容，linkLocation默认值为1
        edaBkConfigParam.setLinkLocation(StringUtils.defaultIfBlank(edaBkConfigParam.getLinkLocation(), "1"));
        //验证不同类型的必填数据
        EdaBkConfigParam.checkParamNotEmpty(edaBkConfigParam);
        //校验eda是否存在
        burypointsEdaMasterService.checkIsExist(edaBkConfigParam.getEdaNo());
        //构建覆盖率配置
        TBurypointsEdaBkConfig tBurypointsEdaBkConfig = EdaBkConfigParam.buildEntity(edaBkConfigParam);
        //插入数据库
        this.insertSelective(tBurypointsEdaBkConfig);
        //日志增加
        this.addLog(tBurypointsEdaBkConfig, null, edaBkConfigParam, LogOperTypeEnum.ADD.getValue(), LogBusiTypeEnum.T_BURYPOINTS_EDA_BK_CONFIG.getValue());
    }

    /**
     * EDA后端覆盖率配置编辑
     * @param edaBkConfigParam
     * @throws BaseTipException
     */
    public void editEdaBkConfig(EdaBkConfigParam edaBkConfigParam) throws BaseTipException {
        //校验参数
        EdaBkConfigParam.checkParam(edaBkConfigParam);
        //校验eda覆盖率配置是否存在
        TBurypointsEdaBkConfig tBurypointsEdaBkConfigOld = this.checkIsExist(Long.valueOf(edaBkConfigParam.getBurypointsEdaBkConfigId()));
        edaBkConfigParam.setConfigType(StringUtils.defaultIfBlank(tBurypointsEdaBkConfigOld.getConfigType(), "1"));
        //验证不同类型的必填数据
        EdaBkConfigParam.checkParamNotEmpty(edaBkConfigParam);
        //校验eda是否存在
        burypointsEdaMasterService.checkIsExist(tBurypointsEdaBkConfigOld.getEdaNo());
        //构建覆盖率配置
        TBurypointsEdaBkConfig tBurypointsEdaBkConfiNew = EdaBkConfigParam.buildEntity(edaBkConfigParam, tBurypointsEdaBkConfigOld);
        //更新数据库
        this.updateByPrimaryKeySelective(tBurypointsEdaBkConfiNew);
        //日志增加
        this.addLog(tBurypointsEdaBkConfiNew, tBurypointsEdaBkConfigOld, edaBkConfigParam, LogOperTypeEnum.EDIT.getValue(), LogBusiTypeEnum.T_BURYPOINTS_EDA_BK_CONFIG.getValue());
    }

    /**
     * EDA后端覆盖率配置删除
     * @param edaBkConfigParam
     * @throws BaseTipException
     */
    public void deleteEdaBkConfig(EdaBkConfigParam edaBkConfigParam) throws BaseTipException {
        //校验eda覆盖率配置是否存在
        TBurypointsEdaBkConfig tBurypointsEdaBkConfigOld = this.checkIsExist(Long.valueOf(edaBkConfigParam.getBurypointsEdaBkConfigId()));
        //构建覆盖率配置
        TBurypointsEdaBkConfig tBurypointsEdaBkConfigNew = new TBurypointsEdaBkConfig();
        tBurypointsEdaBkConfigNew.setIsDel(1);
        tBurypointsEdaBkConfigNew.setBurypointsEdaBkConfigId(tBurypointsEdaBkConfigOld.getBurypointsEdaBkConfigId());
        tBurypointsEdaBkConfigNew.setConfigType(StringUtils.defaultIfBlank(tBurypointsEdaBkConfigOld.getConfigType(), "1"));
        //更新数据库
        this.removeByPrimaryKeySelective(tBurypointsEdaBkConfigNew);
        //日志增加
        this.addLog(tBurypointsEdaBkConfigNew, tBurypointsEdaBkConfigOld, edaBkConfigParam, LogOperTypeEnum.DEL.getValue(), LogBusiTypeEnum.T_BURYPOINTS_EDA_BK_CONFIG.getValue());
    }


    /**
     * EDA后端覆盖率配置列表
     * @param edaBkConfigListParam
     * @return
     */
    public PageVo<EdaBkConfigVo> edaBkConfigList(EdaBkConfigListParam edaBkConfigListParam) {
        edaBkConfigListParam.setIsDel(StringUtils.defaultIfBlank(edaBkConfigListParam.getIsDel(), "0"));
        //分页开始
        PageHelper.startPage(edaBkConfigListParam.pageNumber(), edaBkConfigListParam.pageSize());
        List<EdaBkConfigDo> edaBkConfigs = edaBkConfigMapper.edaBkConfigList(edaBkConfigListParam);
        if (CollectionUtil.isEmpty(edaBkConfigs)) {
            return PageVo.buildEmpty();
        }
        PageInfo<EdaBkConfigDo> pageInfo = new PageInfo<>(edaBkConfigs);
        //结果处理
        List<EdaBkConfigVo> vos = handleResult(pageInfo.getList());
        return PageVo.builderResult(pageInfo.getTotal(), vos);
    }

    private List<EdaBkConfigVo> handleResult(List<EdaBkConfigDo> edaBkConfigs) {
        List<EdaBkConfigVo> vos = new ArrayList<>();
        edaBkConfigs.forEach(edaBkConfig -> {
            EdaBkConfigVo configVo = EdaBkConfigVo.builder(edaBkConfig);
            //es tag 处理
            configVo.setInterfaceFilters(esFilterTagHandle(edaBkConfig.getInterfaceFilters()));
            vos.add(configVo);
        });
        return vos;
    }

    /**
     * es filter标签处理
     *
     * @param interfaceFiltersStr
     * @return List<String>
     */
    public List<String> esFilterTagHandle(String interfaceFiltersStr) {
        String interfacefilterFrelType = ConfigService.getAppConfig().getProperty("interfaceFilter.frel.type", InterfaceFilterConsts.INTERFACEFILTER_FREL_TYPE);
        Map<String, String> filterFrelTypeMap = JSONUtil.toBean(interfacefilterFrelType, Map.class);
        List<String> interfaceFilterList = CollectionUtil.newArrayList();
        Optional.ofNullable(interfaceFiltersStr)
                .map(JSONUtil::parseArray)
                .filter(interfaceFilters -> !interfaceFilters.isEmpty())
                .ifPresent(interfaceFilters -> interfaceFilters.forEach(interfaceFilter -> {
                    if (!(interfaceFilter instanceof JSONObject)) {
                        return;
                    }
                    JSONObject interfaceFilterObject = (JSONObject) interfaceFilter;
                    String frel = (String) interfaceFilterObject.getOrDefault(FREL, FREL_DEFAULT);
                    if (filterFrelTypeMap.containsKey(frel)) {
                        interfaceFilterList.add(MessageFormat.format(filterFrelTypeMap.get(frel)
                                , Optional.ofNullable(interfaceFilterObject.getStr(FKEY)).orElse(StringUtils.EMPTY)
                                , Optional.ofNullable(interfaceFilterObject.getStr(FVALUE))
                                        .map(BurypointsEdaBkConfigService::tryGetFieldValues)
                                        .filter(CollectionUtil::isNotEmpty)
                                        .map(fValues -> {
                                            StringBuilder tag = new StringBuilder();
                                            for (int i = 0; i < fValues.size(); i++) {
                                                tag.append(fValues.get(i));
                                                if (i < fValues.size() - 1) {
                                                    tag.append(",");
                                                }
                                            }
                                            return tag.toString();
                                        })
                                        .orElse(StringUtils.EMPTY)
                        ));
                    }
                }));
        return interfaceFilterList;
    }

    /**
     * 统一日志入口
     * @param tBurypointsEdaBkConfig
     * @param oldTBurypointsEdaBkConfig
     * @param bean
     * @param logType
     * @param optType
     * @throws BaseTipException
     */
    private void addLog(TBurypointsEdaBkConfig tBurypointsEdaBkConfig, TBurypointsEdaBkConfig oldTBurypointsEdaBkConfig, BaseTokenBean bean, int logType, int optType) throws BaseTipException {
        LogUnifySaveBean logUnifySaveBean = new LogUnifySaveBean();
        logUnifySaveBean.setCurrentBean(tBurypointsEdaBkConfig);
        logUnifySaveBean.setOriginalBean(oldTBurypointsEdaBkConfig);
        logUnifySaveBean.setOptTeam(bean.getOptTeam());
        logUnifySaveBean.setOptUserName(bean.getOptUserName());
        logUnifySaveBean.setLogType(logType);
        logUnifySaveBean.setOptType(optType);
        logUnifySaveBean.setEdaBranchNo(String.valueOf(tBurypointsEdaBkConfig.getBurypointsEdaBkConfigId()));
        burypointsEdaLogService.addLog(logUnifySaveBean);
    }

    public TBurypointsEdaBkConfig checkIsExist(Long burypointsEdaBkConfigId) {
        TBurypointsEdaBkConfig tBurypointsEdaBkConfig = tBurypointsEdaBkConfigMapper.selectByPrimaryKey(burypointsEdaBkConfigId);
        Assert.notNull(tBurypointsEdaBkConfig, new ExceptionSupplier("当前EDA覆盖率配置不存在"));
        Assert.isTrue(tBurypointsEdaBkConfig.getIsDel() == 0, new ExceptionSupplier("当前EDA覆盖率配置已删除"));
        return tBurypointsEdaBkConfig;
    }

    /**
     * 获取fieldValue列表
     * @param filterValue
     * @return
     */
    private static List<String> tryGetFieldValues(String filterValue) {
        List<String> list = new ArrayList<>();
        if (JsonToolkit.isJsonArrayObject(filterValue)) {
            try {
                JSONArray array = new JSONArray(filterValue);
                for (int i = 0; i < array.length(); i++) {
                    Object eachValue = array.get(i);
                    if (null != eachValue) {
                        String eachArrayValue = String.valueOf(eachValue);
                        if (StringUtils.isNotEmpty(eachArrayValue)) {
                            list.add(eachArrayValue);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("将" + filterValue + "转换成jsonArray发生异常！！" + e.getMessage());
                list.add(filterValue);
            }
        } else {
            list.add(filterValue);
        }
        return list;
    }


    public int insertSelective(TBurypointsEdaBkConfig record) {
        record.setBurypointsEdaBkConfigId(UidToolkit.generateUidDefault());
        if (StringUtils.equals("2", record.getConfigType())) {
            //前端埋点
            return tBurypointsEdaBkConfigMapper.insertSelectiveFt(record);
        } else {
            //后端埋点
            return tBurypointsEdaBkConfigMapper.insertSelective(record);
        }
    }

    public int updateByPrimaryKeySelective(TBurypointsEdaBkConfig record) {
        if (StringUtils.equals("2", record.getConfigType())) {
            //前端埋点
            return tBurypointsEdaBkConfigMapper.updateByPrimaryKeySelectiveFt(record);
        } else {
            //后端埋点
            return tBurypointsEdaBkConfigMapper.updateByPrimaryKeySelective(record);
        }
    }

    public int removeByPrimaryKeySelective(TBurypointsEdaBkConfig record) {
        if (StringUtils.equals("2", record.getConfigType())) {
            //前端埋点
            return tBurypointsEdaBkConfigMapper.removeByPrimaryKeySelectiveFt(record.getBurypointsEdaBkConfigId());
        } else {
            //后端埋点
            return tBurypointsEdaBkConfigMapper.removeByPrimaryKeySelective(record.getBurypointsEdaBkConfigId());
        }
    }

    /**
     * 提供查询可选接口类型的过滤条件
     * @return
     */
    public Map queryCoverRateInterfaceFilters() throws IOException {
        Map<String, Object> returnMap = new HashMap();
        String interfaceFilters = ConfigService.getAppConfig().getProperty("eda.cover.rate.interfaceFilters","");
        returnMap.put("interfaceFilterList", JsonToolkit.jsonToObject(interfaceFilters, List.class));
        return returnMap;
    }

    //eda.cover.rate.interfaceFilters
}
