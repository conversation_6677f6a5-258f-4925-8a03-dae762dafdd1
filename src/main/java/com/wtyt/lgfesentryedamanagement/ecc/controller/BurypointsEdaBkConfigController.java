package com.wtyt.lgfesentryedamanagement.ecc.controller;

import com.wtyt.lg.commons.bean.BaseBean;
import com.wtyt.lg.commons.bean.ResDataBean;
import com.wtyt.lg.commons.exception.BaseTipException;
import com.wtyt.lgfesentryedamanagement.ecc.bean.param.EdaBkConfigListParam;
import com.wtyt.lgfesentryedamanagement.ecc.bean.param.EdaBkConfigParam;
import com.wtyt.lgfesentryedamanagement.ecc.bean.vo.EdaBkConfigVo;
import com.wtyt.lgfesentryedamanagement.ecc.service.BurypointsEdaBkConfigService;
import com.wtyt.lgfesentryedamanagement.pub.annotation.Attribute;
import com.wtyt.lgfesentryedamanagement.pub.bean.PageVo;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.CommonToolkit;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.ValidateFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.groups.Default;
import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/1/30 15:13
 * @vesion 1.0
 * @desc
 */
@RestController
@RequestMapping("/edaBkConfig/")
public class BurypointsEdaBkConfigController {

    @Autowired
    private BurypointsEdaBkConfigService burypointsEdaBkConfigService;


    /**
     * EDA后端覆盖率配置新增接口
     * @param bean
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "add", method = RequestMethod.POST)
    @Attribute(sid = "5545005", name = "EDA后端覆盖率配置新增接口")
    public ResDataBean<?> addEdaBkConfig(@RequestBody BaseBean<EdaBkConfigParam> bean) throws Exception {
        EdaBkConfigParam edaBkConfigParam = CommonToolkit.checkReq(bean);
        ValidateFilter.getFilterMessage(edaBkConfigParam, ValidateFilter.Create.class);
        burypointsEdaBkConfigService.addEdaBkConfig(edaBkConfigParam);
        return new ResDataBean<>().success();
    }

    /**
     * EDA后端覆盖率配置修改接口
     * @param bean
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "edit", method = RequestMethod.POST)
    @Attribute(sid = "5545006", name = "EDA后端覆盖率配置修改接口")
    public ResDataBean<?> editEdaBkConfig(@RequestBody BaseBean<EdaBkConfigParam> bean) throws Exception {
        EdaBkConfigParam edaBkConfigParam = CommonToolkit.checkReq(bean);
        ValidateFilter.getFilterMessage(edaBkConfigParam, ValidateFilter.Update.class);
        burypointsEdaBkConfigService.editEdaBkConfig(edaBkConfigParam);
        return new ResDataBean<>().success();
    }


    /**
     * EDA后端覆盖率配置删除接口
     * @param bean
     * @return
     * @throws BaseTipException
     */
    @RequestMapping(value = "delete", method = RequestMethod.POST)
    @Attribute(sid = "5545007", name = "EDA后端覆盖率配置删除接口")
    public ResDataBean<?> deleteEdaBkConfig(@RequestBody BaseBean<EdaBkConfigParam> bean) throws BaseTipException {
        EdaBkConfigParam edaBkConfigParam = CommonToolkit.checkReq(bean);
        ValidateFilter.getFilterMessage(edaBkConfigParam, ValidateFilter.Delete.class);
        burypointsEdaBkConfigService.deleteEdaBkConfig(edaBkConfigParam);
        return new ResDataBean<>().success();
    }


    /**
     * EDA后端覆盖率配置列表接口
     * @param bean
     * @return PageVo
     */
    @RequestMapping(value = "list", method = RequestMethod.POST)
    @Attribute(sid = "5545008", name = "EDA后端覆盖率配置列表接口")
    public ResDataBean<PageVo<EdaBkConfigVo>> edaBkConfigList(@RequestBody BaseBean<EdaBkConfigListParam> bean)  {
        EdaBkConfigListParam edaBkConfigListParam = CommonToolkit.checkReq(bean);
        ValidateFilter.getFilterMessage(edaBkConfigListParam, Default.class);
        return new ResDataBean<PageVo<EdaBkConfigVo>>().success(burypointsEdaBkConfigService.edaBkConfigList(edaBkConfigListParam));
    }

    /**
     * 提供查询可选接口类型的过滤条件
     * @return
     */
    @RequestMapping(value = "queryCoverRateInterfaceFilters", method = RequestMethod.POST)
    @Attribute(sid = "5545028", name = "提供查询可选接口类型的过滤条件")
    public ResDataBean<Map> queryCoverRateInterfaceFilters() throws IOException {
        return new ResDataBean<Map>().success(burypointsEdaBkConfigService.queryCoverRateInterfaceFilters());
    }

}
