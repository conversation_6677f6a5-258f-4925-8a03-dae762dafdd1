package com.wtyt.lgfesentryedamanagement.eda.bean.vo;

import com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaMaster;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.Optional;

import static com.wtyt.lgfesentryedamanagement.pub.toolkits.DateToolkit.YYYY_MM_DD_TIME;

/**
 * <AUTHOR>
 * @date 2024/1/30 13:35
 * @vesion 1.0
 * @desc
 */
public class EdaMasterVo implements Serializable {
    private static final long serialVersionUID = 4949579899183487400L;
    private String edaNo;//EDA编号
    private String edaName;//EDA中文名称
    private Integer edaType;
    private String edaAbbreveName;//EDA名称缩写
    private String edaStatus;//EDA状态：0: 不呈现，1：呈现
    private String maintenanceTeam;//维护团队
    private String maintenanceUi;//负责ui
    private String coverageBaseline;//覆盖率基线（百分比）
    private String lastModifiedTime;//最近修改时间
    private String floatingRatio;//浮动比例（百分比）
    private String unconventionalBaselineReason;//非常规覆盖率基线原因
    private String edaDesc;// EDA描述信息
    private String edaBizLine;// 业务线
    private String edaProduct;// 产品服务
    private String edaModule;// 产品模块
    private String createdTime;//创建时间

    public String getEdaNo() {
        return edaNo;
    }

    public void setEdaNo(String edaNo) {
        this.edaNo = edaNo;
    }

    public String getEdaName() {
        return edaName;
    }

    public void setEdaName(String edaName) {
        this.edaName = edaName;
    }

    public Integer getEdaType() {
        return edaType;
    }

    public void setEdaType(Integer edaType) {
        this.edaType = edaType;
    }

    public String getEdaAbbreveName() {
        return edaAbbreveName;
    }

    public void setEdaAbbreveName(String edaAbbreveName) {
        this.edaAbbreveName = edaAbbreveName;
    }

    public String getEdaStatus() {
        return edaStatus;
    }

    public void setEdaStatus(String edaStatus) {
        this.edaStatus = edaStatus;
    }

    public String getMaintenanceTeam() {
        return maintenanceTeam;
    }

    public void setMaintenanceTeam(String maintenanceTeam) {
        this.maintenanceTeam = maintenanceTeam;
    }

    public String getMaintenanceUi() {
        return maintenanceUi;
    }

    public void setMaintenanceUi(String maintenanceUi) {
        this.maintenanceUi = maintenanceUi;
    }

    public String getCoverageBaseline() {
        return coverageBaseline;
    }

    public void setCoverageBaseline(String coverageBaseline) {
        this.coverageBaseline = coverageBaseline;
    }

    public String getLastModifiedTime() {
        return lastModifiedTime;
    }

    public void setLastModifiedTime(String lastModifiedTime) {
        this.lastModifiedTime = lastModifiedTime;
    }

    public String getFloatingRatio() {
        return floatingRatio;
    }

    public void setFloatingRatio(String floatingRatio) {
        this.floatingRatio = floatingRatio;
    }

    public String getUnconventionalBaselineReason() {
        return unconventionalBaselineReason;
    }

    public void setUnconventionalBaselineReason(String unconventionalBaselineReason) {
        this.unconventionalBaselineReason = unconventionalBaselineReason;
    }

    public String getEdaDesc() {
        return edaDesc;
    }

    public void setEdaDesc(String edaDesc) {
        this.edaDesc = edaDesc;
    }

    public String getEdaBizLine() {
        return edaBizLine;
    }

    public void setEdaBizLine(String edaBizLine) {
        this.edaBizLine = edaBizLine;
    }

    public String getEdaProduct() {
        return edaProduct;
    }

    public void setEdaProduct(String edaProduct) {
        this.edaProduct = edaProduct;
    }

    public String getEdaModule() {
        return edaModule;
    }

    public void setEdaModule(String edaModule) {
        this.edaModule = edaModule;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public static EdaMasterVo builder(TBurypointsEdaMaster edaMaster){
        EdaMasterVo edaMasterVo = new EdaMasterVo();
        BeanUtils.copyProperties(edaMaster, edaMasterVo);
        edaMasterVo.setCreatedTime(edaMaster.getCreatedTime().format(YYYY_MM_DD_TIME));
        edaMasterVo.setLastModifiedTime(edaMaster.getLastModifiedTime().format(YYYY_MM_DD_TIME));
        edaMasterVo.setEdaStatus(String.valueOf(edaMaster.getEdaStatus()));
//        edaMasterVo.setFloatingRatio(edaMaster.getFloatingRatio().toPlainString());
//        Optional.ofNullable(edaMaster.getCoverageBaseline()).ifPresent(coverageBaseline -> edaMasterVo.setCoverageBaseline(coverageBaseline.toPlainString()));
        return edaMasterVo;
    }
}
