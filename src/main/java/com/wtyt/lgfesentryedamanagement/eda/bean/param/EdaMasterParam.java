package com.wtyt.lgfesentryedamanagement.eda.bean.param;

import com.wtyt.generator.toolkit.UidToolkit;
import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaMaster;
import com.wtyt.lgfesentryedamanagement.pub.bean.BaseTokenBean;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.ValidateFilter;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.validation.Number;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/1/29 18:36
 * @vesion 1.0
 * @desc
 */
public class EdaMasterParam extends BaseTokenBean {
    private static final long serialVersionUID = -6096189886452903667L;
    private static final BigDecimal COMMON_COVERAGE_BASE_LINE = new BigDecimal("100");

    @NotEmpty(message = "EDA编号不能为空", groups = {ValidateFilter.Create.class, ValidateFilter.Update.class, ValidateFilter.Delete.class})
    @Size(message = "EDA编号过长", max = 64, groups = {ValidateFilter.Create.class, ValidateFilter.Update.class, ValidateFilter.Delete.class})
    private String edaNo;//EDA编号
    @NotEmpty(message = "EDA中文名称不能为空", groups = {ValidateFilter.Create.class})
    @Size(message = "EDA中文名称过长", max = 64, groups = {ValidateFilter.Create.class, ValidateFilter.Update.class})
    private String edaName;//EDA中文名称
    @Pattern(message = "EDA状态不合法", regexp = "^|[0-9]*$", groups = {ValidateFilter.Create.class, ValidateFilter.Update.class})
    private String edaStatus;//EDA状态：0: 不呈现，1：呈现
    private String edaDesc;//EDA描述信息
    private String maintenanceTeam;//维护团队
    private String maintenanceUi;//负责ui
    @Number(message = "覆盖率基线（百分比）不合法", maxIntLen = 10, maxFloatLen = 2, groups = {ValidateFilter.Create.class, ValidateFilter.Update.class})
    @NotEmpty(message = "覆盖率基线（百分比）不能为空", groups = {ValidateFilter.Create.class})
    private String coverageBaseline;//覆盖率基线（百分比）
    @Number(message = "浮动比例（百分比）不合法", maxIntLen = 10, maxFloatLen = 2, groups = {ValidateFilter.Create.class, ValidateFilter.Update.class})
    private String floatingRatio;//浮动比例（百分比）
    private String unconventionalBaselineReason;//非常规覆盖率基线原因
    private String edaBizLine;//业务线
    private String edaProduct;//产品服务
    private String edaModule;//产品模块

    public String getEdaNo() {
        return edaNo;
    }

    public void setEdaNo(String edaNo) {
        this.edaNo = edaNo;
    }

    public String getEdaName() {
        return edaName;
    }

    public void setEdaName(String edaName) {
        this.edaName = edaName;
    }

    public String getEdaStatus() {
        return edaStatus;
    }

    public void setEdaStatus(String edaStatus) {
        this.edaStatus = edaStatus;
    }

    public String getEdaDesc() {
        return edaDesc;
    }

    public void setEdaDesc(String edaDesc) {
        this.edaDesc = edaDesc;
    }

    public String getMaintenanceTeam() {
        return maintenanceTeam;
    }

    public void setMaintenanceTeam(String maintenanceTeam) {
        this.maintenanceTeam = maintenanceTeam;
    }

    public String getMaintenanceUi() {
        return maintenanceUi;
    }

    public void setMaintenanceUi(String maintenanceUi) {
        this.maintenanceUi = maintenanceUi;
    }

    public String getCoverageBaseline() {
        return coverageBaseline;
    }

    public void setCoverageBaseline(String coverageBaseline) {
        this.coverageBaseline = coverageBaseline;
    }

    public String getFloatingRatio() {
        return floatingRatio;
    }

    public void setFloatingRatio(String floatingRatio) {
        this.floatingRatio = floatingRatio;
    }

    public String getUnconventionalBaselineReason() {
        return unconventionalBaselineReason;
    }

    public void setUnconventionalBaselineReason(String unconventionalBaselineReason) {
        this.unconventionalBaselineReason = unconventionalBaselineReason;
    }

    public String getEdaBizLine() {
        return edaBizLine;
    }

    public void setEdaBizLine(String edaBizLine) {
        this.edaBizLine = edaBizLine;
    }

    public String getEdaProduct() {
        return edaProduct;
    }

    public void setEdaProduct(String edaProduct) {
        this.edaProduct = edaProduct;
    }

    public String getEdaModule() {
        return edaModule;
    }

    public void setEdaModule(String edaModule) {
        this.edaModule = edaModule;
    }

    public static void paramCheck(EdaMasterParam edaMasterParam) {
        if (StringUtils.isBlank(edaMasterParam.getEdaStatus())) {
            edaMasterParam.setEdaStatus("0");
        }
        if (StringUtils.isBlank(edaMasterParam.getFloatingRatio())) {
            edaMasterParam.setFloatingRatio("5");
        }

        if (COMMON_COVERAGE_BASE_LINE.compareTo(new BigDecimal(edaMasterParam.getCoverageBaseline())) != 0 && StringUtils.isBlank(edaMasterParam.getUnconventionalBaselineReason())) {
            throw new UnifiedBusinessException("非常规覆盖率基线原因不能为空");
        }
    }

    public static void updateParamCheck(EdaMasterParam edaMasterParam) {
        if ((StringUtils.isNotBlank(edaMasterParam.getCoverageBaseline()) && COMMON_COVERAGE_BASE_LINE.compareTo(new BigDecimal(edaMasterParam.getCoverageBaseline())) != 0)
                && StringUtils.isBlank(edaMasterParam.getUnconventionalBaselineReason())) {
            throw new UnifiedBusinessException("非常规覆盖率基线原因不能为空");
        }
    }

    public static TBurypointsEdaMaster buildEntity(EdaMasterParam edaMasterParam) {
        TBurypointsEdaMaster tBurypointsEdaMaster = new TBurypointsEdaMaster();
        Long id = UidToolkit.generateUidDefault();
        tBurypointsEdaMaster.setBurypointsEdaMasterId(id);
        BeanUtils.copyProperties(edaMasterParam, tBurypointsEdaMaster);

        Optional.ofNullable(edaMasterParam.getEdaStatus()).filter(StringUtils::isNotBlank).ifPresent(edaStatus -> tBurypointsEdaMaster.setEdaStatus(Integer.valueOf(edaStatus)));
        Optional.ofNullable(edaMasterParam.getCoverageBaseline()).filter(StringUtils::isNotBlank).ifPresent(coverageBaseline -> tBurypointsEdaMaster.setCoverageBaseline(new BigDecimal(coverageBaseline)));
        Optional.ofNullable(edaMasterParam.getFloatingRatio()).filter(StringUtils::isNotBlank).ifPresent(floatingRatio -> tBurypointsEdaMaster.setFloatingRatio(new BigDecimal(floatingRatio)));
        return tBurypointsEdaMaster;
    }

    public static TBurypointsEdaMaster buildEntity(EdaMasterParam edaMasterParam, TBurypointsEdaMaster burypointsEdaMasterOld) {
        TBurypointsEdaMaster burypointsEdaMasterNew = new TBurypointsEdaMaster();
        burypointsEdaMasterNew.setBurypointsEdaMasterId(burypointsEdaMasterOld.getBurypointsEdaMasterId());
        burypointsEdaMasterNew.setEdaNo(burypointsEdaMasterOld.getEdaNo());
        BeanUtils.copyProperties(edaMasterParam, burypointsEdaMasterNew);

        Optional.ofNullable(edaMasterParam.getEdaStatus()).filter(StringUtils::isNotBlank).ifPresent(edaStatus -> burypointsEdaMasterNew.setEdaStatus(Integer.valueOf(edaStatus)));
        Optional.ofNullable(edaMasterParam.getCoverageBaseline()).filter(StringUtils::isNotBlank).ifPresent(coverageBaseline -> burypointsEdaMasterNew.setCoverageBaseline(new BigDecimal(coverageBaseline)));
        Optional.ofNullable(edaMasterParam.getFloatingRatio()).filter(StringUtils::isNotBlank).ifPresent(floatingRatio -> burypointsEdaMasterNew.setFloatingRatio(new BigDecimal(floatingRatio)));
        return burypointsEdaMasterNew;
    }

}
