package com.wtyt.lgfesentryedamanagement.eda.bean.yida;

import java.util.HashMap;
import java.util.Map;

public class YidaTableChangedThreadLocal {

    /**
     * 记录当前是否已修改过的table(主要是全量同步时减少表的修改操作，只需要修改一次即可)
     */
    private static final ThreadLocal<Map<String, String>> tableChangedMap = new ThreadLocal<>();


    public static void init() {
        tableChangedMap.set(new HashMap<>());
    }

    public static void clear() {
        Map<String, String> map = tableChangedMap.get();
        if (map != null) {
            map.clear();
        }
        tableChangedMap.remove();
    }

    public static void setTableChanged(String tableName) {
        if (tableChangedMap.get() == null) {
            init();
        }
        tableChangedMap.get().put(tableName, "1");
    }

    public static boolean checkTableChanged(String tableName) {
        if (tableChangedMap.get() == null) {
            return false;
        }
        return tableChangedMap.get().containsKey(tableName);
    }

}
