package com.wtyt.lgfesentryedamanagement.eda.bean.param;

import com.wtyt.lgfesentryedamanagement.pub.bean.BaseTokenBean;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.ValidateFilter;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date ：2024/5/10 17:41
 */
@Data
public class Req5545027IBean  extends BaseTokenBean {
    @NotEmpty(message = "eda操作链路编号不能为空", groups = {ValidateFilter.Create.class})
    private String burypointsEdaRelId;
}
