package com.wtyt.lgfesentryedamanagement.eda.bean.param;

import com.wtyt.lgfesentryedamanagement.pub.toolkits.ValidateFilter;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
public class Req5545045IBean implements Serializable {
    @NotEmpty(message = "数据id不能为空", groups = {ValidateFilter.Create.class})
    private Long drawioFemErrorExtId;

    @NotEmpty(message = "异常id不能为空", groups = {ValidateFilter.Create.class})
    private Long drawioFemErrorId;

    @NotEmpty(message = "异常原因不能为空", groups = {ValidateFilter.Create.class})
    @Size(message = "异常原因过长", max = 512, groups = {ValidateFilter.Create.class})
    private String errorReason;//异常原因

    @Size(message = "异常解决方案过长", max = 256, groups = {ValidateFilter.Create.class})
    private String errorSolution;//异常解决方案

    private String issueHandler;//处理人
    private String realName; // 问题上报人
}