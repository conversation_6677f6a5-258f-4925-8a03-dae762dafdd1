package com.wtyt.lgfesentryedamanagement.eda.bean.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.groups.Default;

@Data
public class Req5545021IBean {
    /**
     * 操作类型  1-新增/修改 2-删除
     */
    private String optType;
    /**
     * 表单uuid
     */
    @NotBlank(message = "formUuid 不能为空", groups = {Default.class})
    private String formUuid;
    /**
     * 表单实例id
     */
    @NotBlank(message = "表单实例id不能为空", groups = {Default.class})
    private String formInstanceId;
}
