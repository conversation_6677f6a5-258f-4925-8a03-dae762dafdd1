package com.wtyt.lgfesentryedamanagement.eda.bean.param;

import com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEda;
import com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaMaster;
import com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaRel;
import com.wtyt.lgfesentryedamanagement.pub.bean.BaseTokenBean;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper=false)
public class Req5545015IBean extends BaseTokenBean {

    private static final long serialVersionUID = -8778530425190199609L;

    private List<TBurypointsEdaRel> edaRelList;

    private List<TBurypointsEda> edaBranchList;

}
