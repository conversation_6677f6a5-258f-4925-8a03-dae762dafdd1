package com.wtyt.lgfesentryedamanagement.eda.bean.param;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.ValidateFilter;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
@Data
public class Req5545041IBean  implements Serializable {
    @NotEmpty(message = "操作链路id不能为空", groups = {ValidateFilter.Create.class})
    private String optLinkId;//操作链路id

    private String edaBranchNos;//操作链路分支编号

    @NotEmpty(message = "search ds不能为空", groups = {ValidateFilter.Create.class})
    private String searchDs;// 查询分区 例：20250601，默认昨日
    
    private String startDateId;// 开始日期 例：20250601，默认昨日

    private String endDateId;// 结束日期 例：20250601，默认昨日

    private String orgIds;

    private String realUserIds;

    private String type;

    private String env;
}
