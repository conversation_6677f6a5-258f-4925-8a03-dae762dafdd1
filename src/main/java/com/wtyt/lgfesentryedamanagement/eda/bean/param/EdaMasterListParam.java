package com.wtyt.lgfesentryedamanagement.eda.bean.param;


import com.wtyt.lgfesentryedamanagement.pub.bean.PageParam;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.ValidateFilter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import javax.validation.groups.Default;

/**
 * <AUTHOR>
 * @date 2024/1/30 10:41
 * @vesion 1.0
 * @desc
 */
public class EdaMasterListParam extends PageParam {
    private static final long serialVersionUID = -6864980596087625996L;
    @Size(message = "EDA编号过长", max = 64, groups = {Default.class})
    private String edaNo;//EDA编号
    @Size(message = "EDA中文名称过长", max = 64, groups = {Default.class})
    private String edaName;//EDA中文名称
    @Size(message = "EDA缩写名称过长", max = 64, groups = {Default.class})
    private String edaAbbreveName;//EDA名称缩写
    private String maintenanceTeam;//维护团队
    @Pattern(message = "筛选状态不合法", regexp = "^|[0-9]*|(-[0-9]+)$", groups = {Default.class})
    private String filterStatus;//筛选状态：0: 不呈现，1：呈现, -1:删除
    @NotEmpty(message = "EDA类型不能为空", groups = {ValidateFilter.Create.class, ValidateFilter.Update.class, ValidateFilter.Delete.class})
    private String edaType;//EDA类型 ,1:功能点,0:操作链路

    public String getEdaNo() {
        return edaNo;
    }

    public void setEdaNo(String edaNo) {
        this.edaNo = edaNo;
    }

    public String getEdaName() {
        return edaName;
    }

    public void setEdaName(String edaName) {
        this.edaName = edaName;
    }

    public String getEdaAbbreveName() {
        return edaAbbreveName;
    }

    public void setEdaAbbreveName(String edaAbbreveName) {
        this.edaAbbreveName = edaAbbreveName;
    }

    public String getMaintenanceTeam() {
        return maintenanceTeam;
    }

    public void setMaintenanceTeam(String maintenanceTeam) {
        this.maintenanceTeam = maintenanceTeam;
    }

    public String getFilterStatus() {
        return filterStatus;
    }

    public void setFilterStatus(String filterStatus) {
        this.filterStatus = filterStatus;
    }

    public String getEdaType() {
        return edaType;
    }

    public void setEdaType(String edaType) {
        this.edaType = edaType;
    }
}
