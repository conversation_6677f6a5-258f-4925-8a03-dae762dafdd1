package com.wtyt.lgfesentryedamanagement.eda.bean.param;

import com.wtyt.lgfesentryedamanagement.pub.toolkits.ValidateFilter;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
public class Req5545034IBean implements Serializable {

    private String optLinkId;//操作链路id

    private List<Req5545034IBean> dataList;

    private String configType;//配置类型

    private String edaMxcellId;//EDA XML节点对应ID

    private String eleResourceId;//埋点元素id
    private String appTag;//产品端_环境端
    private String eleExtField;//元素扩展字段key
    private String eleExtFieldVal;//元素扩展字段value
    private String eleExtJson;
    private String eleColumnJson;

    private String interfaceProjectName;//接口所在的项目名
    private String interfaceTag;//接口标识（主要是接口id）
    private String interfaceType;//接口类型
    private List<Object> interfaceFilters;//接口类型的过滤条件
    private String interfaceIndexName;//使用接口全埋时的索引名称
    private String interfaceExcludeFilters;//接口类型的排除过滤条件

}
