package com.wtyt.lgfesentryedamanagement.eda.bean.param;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper=false)
public class Req5545017IBean {

    private String edaType;//EDA类型 ,1:功能点,0:操作链路
    private String optType;//操作类型  1-新增 2-修改 3-删除
    private String formInstanceId;//表单实例id
    private String processInstanceId;//流程实例id
    private String optUserId;//操作人
    private String optTeamId;//操作人团队
    private String cratedTime;//创建时间
    private String modifiedTime;//修改时间
    private String edaNo;//EDA编码
    private String edaName;//操作链路
    private String edaLinkUrl;//EDA链接
    private String buryCoverage;//埋点覆盖率
    private String coverageBase;//覆盖率基线
    private List<String> responsibleDevTeam;//负责研发
    private List<String> responsibleUI;//负责UI
    private String responsibleUIName;
    private String responsibleDevTeamName;
    private List<FormInfoBean> skuForm;//SKU表单信息
    private List<FormInfoBean> featureModule;//功能模块
    private List<FormInfoBean> productPortList;//产品端口
    private String isShow;// 是否呈现
    private String isBury;// 是否埋点
    private String isReach;//是否达标
    private String notReachReason;//不达标原因
    private String notReachReasonDesc;//不达标原因说明
    private String confirmStatus;//确认状态

    /**
     * 关联表单信息Bean
     */
    @Data
    public static class FormInfoBean {
        /**
         * 表单类型
         */
        private String formType;
        /**
         * 表单uuid
         */
        private String formUuid;
        /**
         * 实例id
         */
        private String instanceId;
        /**
         * 主要信息
         */
        private String title;
        /**
         * 次要信息
         */
        private String subTitle;
        /**
         * 应用编码
         */
        private String appType;
        /**
         *
         */
        private String pid;
    }

}
