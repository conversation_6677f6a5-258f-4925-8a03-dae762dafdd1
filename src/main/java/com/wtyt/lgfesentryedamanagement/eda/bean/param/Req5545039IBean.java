package com.wtyt.lgfesentryedamanagement.eda.bean.param;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.ValidateFilter;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
@Data
public class Req5545039IBean implements Serializable {

    @NotEmpty(message = "操作链路id不能为空", groups = {ValidateFilter.Create.class})
    private String optLinkId;//操作链路id

    @NotEmpty(message = "操作名称不能为空", groups = {ValidateFilter.Create.class})
    private String optLinkName;//操作名称

    private String maintenanceTeam;//研发团队

    private String maintenanceUi; //关联UI
    @NotEmpty(message = "EDA图状态不能为空", groups = {ValidateFilter.Create.class})
    private String edaStatus;//EDA图状态 ,3:未达标,2:达标,1:待呈现,0:不呈现

}