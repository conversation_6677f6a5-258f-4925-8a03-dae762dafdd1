package com.wtyt.lgfesentryedamanagement.eda.bean.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.groups.Default;

@Data
public class Req5545023IBean {
    /**
     * 迭代id
     */
    @NotBlank(message = "taskId 不能为空", groups = {Default.class})
    private String taskId;
    /**
     * teambition执行人id
     */
    private String operatorId;
    /**
     * 实际评审人id
     */
    private String archReviewerId;
    /**
     * 架构评审状态
     */
    private String archReviewStatus;

}
