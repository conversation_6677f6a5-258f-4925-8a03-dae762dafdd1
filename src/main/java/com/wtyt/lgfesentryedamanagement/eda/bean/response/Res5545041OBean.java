package com.wtyt.lgfesentryedamanagement.eda.bean.response;
import lombok.Data;

@Data
public class Res5545041OBean {
    private String edaBranchNo;// 操作链路分支名称
    private String startNum;// 入口数
    private String closeNum;// 闭环数
    private String closeRate;// 闭环率
    private String avgCostTime;// 平均时效
    private String per50CostTime;// 50分位时效
    private String per75CostTime;// 75分位时效
    private String per80CostTime;// 80分位时效
    private String per85CostTime;// 85分位时效
    private String per90CostTime;// 90分位时效
}
