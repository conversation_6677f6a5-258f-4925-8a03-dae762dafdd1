package com.wtyt.lgfesentryedamanagement.eda.bean.yida;

import com.wtyt.lgfesentryedamanagement.pub.toolkits.JsonToolkit;
import lombok.Data;

@Data
public class InstanceValueDto {
    /**
     * 组件名称
     */
    private String componentName;
    private String fieldDataUpdated;
    /**
     * 组件唯一标识
     */
    private String fieldId;
    /**
     * 字段数据
     */
    private FieldDataDto fieldData;

    /**
     * 日期格式，日期类型及日期区间类型有用, 如：yyyy-MM-dd
     */
    private String dateType;
    /**
     * 对数据的补充描述，暂时没有使用到
     */
    private Object options;

    /**
     * 默认获取数据的方式
     * @return
     */
    public String defaultFieldValue() {
        if (ifDataEmpty()) {
            return null;
        }
        Object value = fieldData.getValue();
        if (value instanceof String || value instanceof Number) {
            // 字符串或数字直接返回数据
            return String.valueOf(value);
        }
        // 对象或数组的返回json字符串
        return JsonToolkit.objectToJson(value);
    }

    public boolean ifDataEmpty() {
        return fieldData == null || fieldData.getValue() == null;
    }

    /**
     * 字段数据类
     */
    @Data
    public static class FieldDataDto {
        /**
         * 字段数据，不同类型的组件数据格式不一样，可能是 String, int, Object, Array
         */
        private Object value;
    }

}
