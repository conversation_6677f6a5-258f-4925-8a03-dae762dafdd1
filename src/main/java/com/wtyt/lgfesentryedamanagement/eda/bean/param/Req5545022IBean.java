package com.wtyt.lgfesentryedamanagement.eda.bean.param;

import com.wtyt.lgfesentryedamanagement.pub.bean.BaseTokenBean;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.groups.Default;

@Data
public class Req5545022IBean extends BaseTokenBean {
    /**
     * 表单uuid, 多个用逗号分隔
     */
    @NotBlank(message = "formUuids 不能为空", groups = {Default.class})
    private String formUuids;
    /**
     * 隐藏字段，如果需要同步所有表单，则这个字段传1，一般不会使用（如果一开始formUuid会有很多则使用这个方式）
     */
    private String allForm;

}
