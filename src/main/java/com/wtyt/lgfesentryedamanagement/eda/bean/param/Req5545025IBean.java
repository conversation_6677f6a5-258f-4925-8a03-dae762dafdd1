package com.wtyt.lgfesentryedamanagement.eda.bean.param;

import com.wtyt.lgfesentryedamanagement.pub.bean.BaseTokenBean;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.ValidateFilter;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date ：2024/5/10 9:13
 */
@Data
public class Req5545025IBean  extends BaseTokenBean {
    private String burypointsEdaRelId;
    @NotEmpty(message = "eda操作链路编号不能为空", groups = {ValidateFilter.Create.class})
    private String eleEdaNo;
    @NotEmpty(message = "eda操作链路简称不能为空", groups = {ValidateFilter.Create.class})
    private String edaAbBreveName;
    @NotEmpty(message = "埋点元素id不能为空", groups = {ValidateFilter.Create.class})
    private String eleResourceId;
    @NotEmpty(message = "eda分支编不能为空", groups = {ValidateFilter.Create.class})
    private String edaBranch;
    @NotEmpty(message = "eda分支编号链路起始位置不能为空", groups = {ValidateFilter.Create.class})
    private String isStart;
    @NotEmpty(message = "eda分支编号链路结束位置不能为空", groups = {ValidateFilter.Create.class})
    private String isEnd;
    @NotEmpty(message = "eda分支是否主流程不能为空", groups = {ValidateFilter.Create.class})
    private String isMBranch;
    private String eleExtField;
    private String eleExtFieldVal;
    private String edaId;
    private String edaName;
    private String edaDesc;
    private String edaBranchId;
    @NotEmpty(message = "eda分支名称不能为空", groups = {ValidateFilter.Create.class})
    private String edaBranchName;
    private String edaBranchDesc;
    @NotEmpty(message = "环境端不能为空", groups = {ValidateFilter.Create.class})
    private String env;
    private String burDomainRelId;
    private Integer logType;
}
