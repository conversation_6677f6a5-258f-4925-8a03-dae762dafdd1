package com.wtyt.lgfesentryedamanagement.eda.bean.param;

import com.wtyt.lgfesentryedamanagement.pub.toolkits.ValidateFilter;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
public class Req5545035IBean implements Serializable {

    @NotEmpty(message = "操作链路id不能为空", groups = {ValidateFilter.Create.class})
    @Size(message = "操作链路id过长", max = 64, groups = {ValidateFilter.Create.class})
    private String optLinkId;//操作链路id

    private String edaMxcellId;//EDA XML节点对应ID

}
