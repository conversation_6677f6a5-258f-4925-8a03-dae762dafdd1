package com.wtyt.lgfesentryedamanagement.eda.controller;

import com.wtyt.lg.commons.bean.BaseBean;
import com.wtyt.lg.commons.bean.ResDataBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545037IBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545038IBean;
import com.wtyt.lgfesentryedamanagement.eda.service.BurypointsNewEdaBranchService;
import com.wtyt.lgfesentryedamanagement.pub.annotation.Attribute;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.CommonToolkit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 */
@RestController
@RequestMapping("/newEdaBranch/")
public class BurypointsNewEdaBranchController {

    @Autowired
    private BurypointsNewEdaBranchService burypointsNewEdaBranchService;

    /**
     *  5545037-批量维护eda分支（新增或更新） t_burypoints_eda_branch/t_eda_branch_points
     * @param bean
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "batchInsert", method = RequestMethod.POST)
    @Attribute(sid = "5545037", name = "批量维护eda分支(新增或更新)")
    public ResDataBean<?> batchInsert(@RequestBody BaseBean<Req5545037IBean> bean) throws Exception{
        Req5545037IBean data = CommonToolkit.checkReq(bean);
        burypointsNewEdaBranchService.batchInsert(data);
        return new ResDataBean<>().success();
    }

    /**
     * 根据操作链路id查询分支及关联信息 5545038
     * @param String optLinkId
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "selectBranchAndPointsInfo", method = RequestMethod.POST)
    @Attribute(sid = "5545038", name = "根据操作链路id查询分支及关联信息")
    public ResDataBean<?> selectBranchAndPointsInfo(@RequestBody BaseBean<Req5545038IBean> bean) throws Exception{
        Req5545038IBean data = CommonToolkit.checkReq(bean);
        return new ResDataBean<>().success(burypointsNewEdaBranchService.selectBranchAndPointsInfo(data));
    }

}
