package com.wtyt.lgfesentryedamanagement.eda.controller;

import com.wtyt.lg.commons.bean.BaseBean;
import com.wtyt.lg.commons.bean.ResDataBean;
import com.wtyt.lgfesentryedamanagement.bkc.bean.response.Res5545025OBean;
import com.wtyt.lgfesentryedamanagement.ecc.bean.param.EdaBkConfigParam;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545025IBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545027IBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545029IBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.response.Res5545027OBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.response.Res5545029OBean;
import com.wtyt.lgfesentryedamanagement.eda.service.BurypointsEdaBranchService;
import com.wtyt.lgfesentryedamanagement.pub.annotation.Attribute;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.CommonToolkit;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.ValidateFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.groups.Default;

/**
 * <AUTHOR>
 * @date ：2024/5/10 9:08
 */
@RestController
@RequestMapping("/edaBranch/")
public class BurypointsEdaBranchController {
    @Autowired
    private BurypointsEdaBranchService  burypointsEdaBranchService;

    @RequestMapping(value = "add", method = RequestMethod.POST)
    @Attribute(sid = "5545025", name = "新增eda分支")
    public ResDataBean<Res5545025OBean> add(@RequestBody BaseBean<Req5545025IBean> bean) throws Exception{
        Req5545025IBean data = CommonToolkit.checkReq(bean);
        ValidateFilter.getFilterMessage(data, ValidateFilter.Create.class);
        Res5545025OBean oBean = burypointsEdaBranchService.add(bean.getData());
         return new ResDataBean<Res5545025OBean>().success(oBean);
    }

    @RequestMapping(value = "update", method = RequestMethod.POST)
    @Attribute(sid = "5545026", name = "修改eda分支")
    public ResDataBean<Res5545025OBean> update(@RequestBody BaseBean<Req5545025IBean> bean) throws Exception{
        Req5545025IBean data = CommonToolkit.checkReq(bean);
        ValidateFilter.getFilterMessage(data, ValidateFilter.Create.class);
        Res5545025OBean oBean = burypointsEdaBranchService.update(data);
        return new ResDataBean<Res5545025OBean>().success(oBean);
    }

    @RequestMapping(value = "info", method = RequestMethod.POST)
    @Attribute(sid = "5545027", name = "eda分支详情接口")
    public ResDataBean<Res5545027OBean> info(@RequestBody BaseBean<Req5545027IBean> bean) throws Exception{
        Req5545027IBean data = CommonToolkit.checkReq(bean);
        ValidateFilter.getFilterMessage(data, ValidateFilter.Create.class);
        Res5545027OBean oBean = burypointsEdaBranchService.info(data);
        return new ResDataBean<Res5545027OBean>().success(oBean);
    }

    @RequestMapping(value = "delete", method = RequestMethod.POST)
    @Attribute(sid = "5545029", name = "删除eda分支接口")
    public ResDataBean<Res5545029OBean> delete(@RequestBody BaseBean<Req5545029IBean> bean) throws Exception{
        Req5545029IBean data = CommonToolkit.checkReq(bean);
        ValidateFilter.getFilterMessage(data, ValidateFilter.Create.class);
        Res5545029OBean oBean = burypointsEdaBranchService.delete(data);
        return new ResDataBean<Res5545029OBean>().success(oBean);
    }
}
