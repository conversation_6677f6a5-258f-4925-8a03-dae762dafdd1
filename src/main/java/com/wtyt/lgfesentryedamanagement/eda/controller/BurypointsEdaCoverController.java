package com.wtyt.lgfesentryedamanagement.eda.controller;

import com.wtyt.lg.commons.bean.BaseBean;
import com.wtyt.lg.commons.bean.ResDataBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545034IBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545035IBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.response.Res5545035OBean;
import com.wtyt.lgfesentryedamanagement.eda.service.BurypointsEdaCoverService;
import com.wtyt.lgfesentryedamanagement.pub.annotation.Attribute;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.CommonToolkit;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.ValidateFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 */
@RestController
@RequestMapping("/edaCover/")
public class BurypointsEdaCoverController {

    @Autowired
    private BurypointsEdaCoverService burypointsEdaCoverService;

    /**
     *  5545034-批量维护eda覆盖率(新增或更新) T_BURYPOINTS_EDA_COVER_BK/T_BURYPOINTS_EDA_COVER_FT
     * @param bean
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "batchInsert", method = RequestMethod.POST)
    @Attribute(sid = "5545034", name = "批量维护eda覆盖率(新增或更新)")
    public ResDataBean<?> batchInsert(@RequestBody BaseBean<Req5545034IBean> bean) throws Exception{
        Req5545034IBean data = CommonToolkit.checkReq(bean);
        burypointsEdaCoverService.batchInsert(data);
        return new ResDataBean<>().success();
    }

    /**
     * 5545035-查询节点维护的eda覆盖率配置列表
     * 暂未用到，未开发
     * @param bean
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "queryEdaCoverList", method = RequestMethod.POST)
    @Attribute(sid = "5545035", name = "查询节点维护的eda覆盖率配置列表")
    public ResDataBean<Res5545035OBean> queryEdaCoverList(@RequestBody BaseBean<Req5545035IBean> bean) throws Exception{
        Req5545035IBean data = CommonToolkit.checkReq(bean);
        ValidateFilter.getFilterMessage(data, ValidateFilter.Create.class);
        //burypointsEdaCoverService.queryEdaCoverList(data)
        return new ResDataBean<Res5545035OBean>().success();
    }



}
