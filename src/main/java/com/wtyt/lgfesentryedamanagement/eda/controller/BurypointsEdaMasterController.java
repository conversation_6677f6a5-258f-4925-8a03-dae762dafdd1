package com.wtyt.lgfesentryedamanagement.eda.controller;

import com.wtyt.lg.commons.bean.BaseBean;
import com.wtyt.lg.commons.bean.ResDataBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.EdaMasterListParam;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.EdaMasterParam;
import com.wtyt.lgfesentryedamanagement.eda.bean.vo.EdaMasterVo;
import com.wtyt.lgfesentryedamanagement.eda.service.BurypointsEdaMasterService;
import com.wtyt.lgfesentryedamanagement.pub.annotation.Attribute;
import com.wtyt.lgfesentryedamanagement.pub.bean.PageVo;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.CommonToolkit;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.ValidateFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.groups.Default;

/**
 * <AUTHOR>
 * @date 2024/1/29 18:34
 * @vesion 1.0
 * @desc
 */
@RestController
@RequestMapping("/edaMaster/")
public class BurypointsEdaMasterController {

    @Autowired
    private BurypointsEdaMasterService burypointsEdaMasterService;

    /**
     * 新增EDA接口
     * @param bean
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "add", method = RequestMethod.POST)
    @Attribute(sid = "5545001", name = "新增EDA接口")
    public ResDataBean<?> addEdaMaster(@RequestBody BaseBean<EdaMasterParam> bean) throws Exception {
        EdaMasterParam edaMasterParam = CommonToolkit.checkReq(bean);
        ValidateFilter.getFilterMessage(edaMasterParam, ValidateFilter.Create.class);
        burypointsEdaMasterService.addEdaMaster(edaMasterParam);
        return new ResDataBean<>().success();
    }


    /**
     * 修改EDA接口
     * @param bean
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "edit", method = RequestMethod.POST)
    @Attribute(sid = "5545002", name = "修改EDA接口")
    public ResDataBean<?> editEdaMaster(@RequestBody BaseBean<EdaMasterParam> bean) throws Exception {
        EdaMasterParam edaMasterParam = CommonToolkit.checkReq(bean);
        ValidateFilter.getFilterMessage(edaMasterParam, ValidateFilter.Update.class);
        burypointsEdaMasterService.updateEdaMaster(edaMasterParam);
        return new ResDataBean<>().success();
    }

    /**
     * EDA删除接口
     * @param bean
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "delete", method = RequestMethod.POST)
    @Attribute(sid = "5545003", name = "EDA删除接口")
    public ResDataBean<?> deleteEdaMaster(@RequestBody BaseBean<EdaMasterParam> bean) throws Exception {
        EdaMasterParam edaMasterParam = CommonToolkit.checkReq(bean);
        ValidateFilter.getFilterMessage(edaMasterParam, ValidateFilter.Delete.class);
        burypointsEdaMasterService.deleteEdaMaster(edaMasterParam);
        return new ResDataBean<>().success();
    }

    /**
     * EDA列表接口
     * @param bean
     * @return
     */
    @RequestMapping(value = "list", method = RequestMethod.POST)
    @Attribute(sid = "5545004", name = "EDA列表接口")
    public ResDataBean<PageVo<EdaMasterVo>> edaMasterList(@RequestBody BaseBean<EdaMasterListParam> bean) {
        EdaMasterListParam edaMasterListParam = CommonToolkit.checkReq(bean);
        ValidateFilter.getFilterMessage(edaMasterListParam, Default.class);
        return new ResDataBean<PageVo<EdaMasterVo>>().success(burypointsEdaMasterService.edaMasterList(edaMasterListParam));
    }
}
