package com.wtyt.lgfesentryedamanagement.eda.controller;

import com.wtyt.lg.commons.bean.BaseBean;
import com.wtyt.lg.commons.bean.ResDataBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545017IBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545021IBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545022IBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545023IBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.response.Res5545017OBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.yida.YidaTableChangedThreadLocal;
import com.wtyt.lgfesentryedamanagement.eda.service.BurypointsEdaYidaService;
import com.wtyt.lgfesentryedamanagement.pub.annotation.Attribute;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.CommonToolkit;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.ValidateFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.groups.Default;

/**
 * 宜搭连接器同步接口
 */
@RestController
@RequestMapping("/yida/")
public class BurypointsEdaYidaController {

    @Autowired
    private BurypointsEdaYidaService burypointsEdaYdService;

    @RequestMapping(value = "syncOperateLink", method = RequestMethod.POST)
    @Attribute(sid = "5545017", name = "宜搭连接器-同步操作链路")
    public ResDataBean<Res5545017OBean> syncOperateLink(@RequestBody Req5545017IBean data) throws Exception {
        // 同步操作链路
        burypointsEdaYdService.syncOperateLink(data);

        // 流程后续使用到 eda 编码
        Res5545017OBean response = new Res5545017OBean();
        response.setEdaNo(data.getEdaNo());

        return new ResDataBean<Res5545017OBean>().success(response);
    }

    @RequestMapping(value = "syncYidaFormDataByInstId", method = RequestMethod.POST)
    @Attribute(sid = "5545021", name = "宜搭连接器-同步宜搭表单及元数据")
    public ResDataBean syncYidaFormDataByInstId(@RequestBody Req5545021IBean data) throws Exception {
        // 请求数据校验
        ValidateFilter.getFilterMessage(data, Default.class);
        YidaTableChangedThreadLocal.init();
        try {
            // 同步宜搭表单数据（根据实例id）
            burypointsEdaYdService.syncYidaFormDataByInstId(data);
        } finally {
            YidaTableChangedThreadLocal.clear();
        }

        return new ResDataBean<>().success();
    }


    /**
     *
     * @param baseBean
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "syncYidaFormDataAll", method = RequestMethod.POST)
    @Attribute(sid = "5545022", name = "宜搭表单数据全量同步")
    public ResDataBean syncYidaFormDataAll(@RequestBody BaseBean<Req5545022IBean> baseBean) throws Exception {
        Req5545022IBean data = CommonToolkit.checkReq(baseBean);
        // 请求数据校验
        ValidateFilter.getFilterMessage(data, Default.class);
        // 同步宜搭表单数据（根据表单）
        burypointsEdaYdService.syncYidaFormDataAll(data);

        return new ResDataBean<>().success();
    }

    /**
     * 5545023-宜搭连接器-修改 teambition 架构评审状态
     * @param data
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "syncYidaToTeambition", method = RequestMethod.POST)
    @Attribute(sid = "5545023", name = "宜搭连接器-修改 teambition 架构评审状态")
    public ResDataBean syncYidaToTeambition(@RequestBody Req5545023IBean data) throws Exception {
        // 请求数据校验
        ValidateFilter.getFilterMessage(data, Default.class);
        // 同步宜搭表单数据（根据表单）
        burypointsEdaYdService.syncYidaToTeambition(data);

        return new ResDataBean<>().success();
    }

}
