package com.wtyt.lgfesentryedamanagement.eda.controller;

import com.wtyt.lg.commons.bean.BaseBean;
import com.wtyt.lg.commons.bean.ResDataBean;

import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545041IBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545043IBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.response.Res5545041OBean;
import com.wtyt.lgfesentryedamanagement.eda.service.EdaBranchStatisticsService;
import com.wtyt.lgfesentryedamanagement.pub.annotation.Attribute;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.CommonToolkit;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.ValidateFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import com.wtyt.lgfesentryedamanagement.dao.bean.TBuryPointsSearchTimelinessClosedloop;
import java.util.*;


@RestController
@RequestMapping("/edaBranchStatistics/")
public class EdaBranchStatisticsController {
    @Autowired
    private EdaBranchStatisticsService  edaBranchStatisticsService;

    @RequestMapping(value = "getBranchCloseRateAndCostTime", method = RequestMethod.POST)
    @Attribute(sid = "5545041", name = "直接从dataphin获取eda分支闭环率和时效")
    public ResDataBean<List<Res5545041OBean>> getBranchCloseRateAndCostTime(@RequestBody BaseBean<Req5545041IBean> bean) throws Exception{
        Req5545041IBean data = CommonToolkit.checkReq(bean);
        ValidateFilter.getFilterMessage(data, ValidateFilter.Create.class);
        List<Res5545041OBean> oBean = edaBranchStatisticsService.getBranchCloseRateAndCostTime(data);
        return new ResDataBean<List<Res5545041OBean>>().success(oBean);
    }

    @RequestMapping(value = "addSearch", method = RequestMethod.POST)
    @Attribute(sid = "5545042", name = "新增查询时效闭环任务")
    public ResDataBean<?> addSearch(@RequestBody BaseBean<Req5545041IBean> bean) throws Exception{
        Req5545041IBean data = CommonToolkit.checkReq(bean);
        ValidateFilter.getFilterMessage(data, ValidateFilter.Create.class);
        edaBranchStatisticsService.addSearch(data);
        return new ResDataBean<>().success();
    }

    @RequestMapping(value = "getSearchList", method = RequestMethod.POST)
    @Attribute(sid = "5545043", name = "根据optLinkId&edaBranchNo查询闭环时效结果列表-按照创建时间倒叙排只返回前10条")
    public ResDataBean<List<TBuryPointsSearchTimelinessClosedloop>> getSearchList(@RequestBody BaseBean<Req5545043IBean> bean) throws Exception{
        Req5545043IBean data = CommonToolkit.checkReq(bean);
        ValidateFilter.getFilterMessage(data, ValidateFilter.Create.class);
        List<TBuryPointsSearchTimelinessClosedloop> oBean = edaBranchStatisticsService.getSearchList(data);
        return new ResDataBean<List<TBuryPointsSearchTimelinessClosedloop>>().success(oBean);
    }
    
}
