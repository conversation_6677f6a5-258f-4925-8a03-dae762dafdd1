package com.wtyt.lgfesentryedamanagement.eda.controller;

import com.wtyt.lg.commons.bean.BaseBean;
import com.wtyt.lg.commons.bean.ResDataBean;

import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545044IBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545045IBean;
import com.wtyt.lgfesentryedamanagement.eda.service.TBurypointsFemErrorExtService;
import com.wtyt.lgfesentryedamanagement.pub.annotation.Attribute;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.CommonToolkit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/edaFemErrorExt/")
public class TBurypointsFemErrorExtController {
    @Autowired
    private TBurypointsFemErrorExtService  tBurypointsFemErrorExtService;

     /**
     *  批量新增异常原因上报 5545044
     * @param bean
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "batchInsert", method = RequestMethod.POST)
    @Attribute(sid = "5545044", name = "批量新增异常原因上报")
    public ResDataBean<?> batchInsert(@RequestBody BaseBean<Req5545044IBean> bean) throws Exception{
        Req5545044IBean data = CommonToolkit.checkReq(bean);
        tBurypointsFemErrorExtService.batchInsert(data);
        return new ResDataBean<>().success();
    }

    /**
     *  修改异常原因上报 5545045
     * @param bean
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "updateOne", method = RequestMethod.POST)
    @Attribute(sid = "5545045", name = "修改异常原因上报")
    public ResDataBean<?> updateOne(@RequestBody BaseBean<Req5545045IBean> bean) throws Exception{
        Req5545045IBean data = CommonToolkit.checkReq(bean);
        tBurypointsFemErrorExtService.updateOne(data);
        return new ResDataBean<>().success();
    }
    
}
