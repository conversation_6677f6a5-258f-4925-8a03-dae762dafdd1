package com.wtyt.lgfesentryedamanagement.eda.controller;

import com.wtyt.lg.commons.bean.BaseBean;
import com.wtyt.lg.commons.bean.ResDataBean;
import com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaFileBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.*;
import com.wtyt.lgfesentryedamanagement.eda.bean.response.Res5545029OBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.vo.EdaMasterVo;
import com.wtyt.lgfesentryedamanagement.eda.service.BurypointsEdaMasterService;
import com.wtyt.lgfesentryedamanagement.eda.service.BurypointsEdaSyncService;
import com.wtyt.lgfesentryedamanagement.pub.annotation.Attribute;
import com.wtyt.lgfesentryedamanagement.pub.bean.PageVo;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.CommonToolkit;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.ValidateFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.groups.Default;
import java.util.HashMap;
import java.util.Map;

/**
 *
 */
@RestController
@RequestMapping("/sync/")
public class BurypointsEdaSyncController {

    @Autowired
    private BurypointsEdaSyncService burypointsEdaSyncService;

    /**
     * 【废弃】5545011-同步EDA模块数据
     * @param bean
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "syncMasterEda", method = RequestMethod.POST)
    @Attribute(sid = "5545011", name = "同步EDA模块数据")
    public ResDataBean<?> syncMasterEda(@RequestBody BaseBean<Req5545011IBean> bean) throws Exception {
        burypointsEdaSyncService.syncMasterEda(bean.getData());
        return new ResDataBean<>().success();
    }

    /**
     * 【废弃】5545012-同步EDA分支与埋点元素关系数据
     * @param bean
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "syncEdaFeature", method = RequestMethod.POST)
    @Attribute(sid = "5545012", name = "同步EDA分支与埋点元素关系数据")
    public ResDataBean<?> syncEdaFeature(@RequestBody BaseBean<Req5545012IBean> bean) throws Exception {
        burypointsEdaSyncService.syncEdaFeature(bean.getData());
        return new ResDataBean<>().success();
    }

    /**
     * 【废弃】5545013-同步EDA后端埋点的元数据
     * @param bean
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "syncEdaBkConfig", method = RequestMethod.POST)
    @Attribute(sid = "5545013", name = "同步EDA后端埋点的元数据")
    public ResDataBean<?> syncEdaBkConfig(@RequestBody BaseBean<Req5545013IBean> bean) throws Exception {
        burypointsEdaSyncService.syncEdaBkConfig(bean.getData());
        return new ResDataBean<>().success();
    }

    /**
     * 测试入口
     * @param bean
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "syncEdaMasterFromYiDa", method = RequestMethod.POST)
    public ResDataBean<?> syncEdaMasterFromYiDa(@RequestBody BaseBean<TransEdaMasterBean> bean) throws Exception {
        burypointsEdaSyncService.syncEdaMasterFromYiDa(bean.getData());
        return new ResDataBean<>().success();
    }

    /**
     * 5545018-同步EDA模块数据删除
     * @param bean
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "syncMasterEdaDel", method = RequestMethod.POST)
    @Attribute(sid = "5545018", name = "同步EDA模块数据删除")
    public ResDataBean<?> syncMasterEdaDel(@RequestBody BaseBean<Req5545018IBean> bean) throws Exception {
        burypointsEdaSyncService.syncMasterEdaDel(bean.getData());
        return new ResDataBean<>().success();
    }

    /**
     * 5545019-同步EDA模块数据删除-服务端接口
     * @param bean
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "syncMasterEdaDelServer", method = RequestMethod.POST)
    @Attribute(sid = "5545019", name = "同步EDA模块数据删除-服务端接口")
    public ResDataBean<?> syncMasterEdaDelServer(@RequestBody BaseBean<Req5545019IBean> bean) throws Exception {
        burypointsEdaSyncService.syncMasterEdaDelServer(bean.getData());
        return new ResDataBean<>().success();
    }



    /**
     * 5545014-同步EDA模块(操作链路)
     * @param bean
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "transMasterEda", method = RequestMethod.POST)
    @Attribute(sid = "5545014", name = "同步EDA模块(操作链路)")
    public ResDataBean<?> transMasterEda(@RequestBody BaseBean<Req5545014IParam> bean) throws Exception {
        burypointsEdaSyncService.transMasterEda(bean.getData());
        return new ResDataBean<>().success();
    }

    /**
     * 同步EDA模块数据-服务端接口
     * @param bean
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "transMasterEdaServer", method = RequestMethod.POST)
    public ResDataBean<?> transMasterEdaServer(@RequestBody BaseBean<Req5545014IBean> bean) throws Exception {
        burypointsEdaSyncService.transMasterEdaServer(bean.getData());
        return new ResDataBean<>().success();
    }

    /**
     * 5545015-同步EDA分支与埋点元素关系数据
     * @param bean
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "transEdaBranch", method = RequestMethod.POST)
    @Attribute(sid = "5545015", name = "同步EDA分支与埋点元素关系数据")
    public ResDataBean<?> transEdaBranch(@RequestBody BaseBean<Req5545012IBean> bean) throws Exception {
        burypointsEdaSyncService.transEdaBranch(bean.getData());
        return new ResDataBean<>().success();
    }

    /**
     * 同步EDA分支与埋点元素关系数据-服务端接口
     * @param bean
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "transEdaBranchServer", method = RequestMethod.POST)
    public ResDataBean<?> transEdaBranchServer(@RequestBody BaseBean<Req5545015IBean> bean) throws Exception {
        burypointsEdaSyncService.transEdaBranchServer(bean.getData());
        return new ResDataBean<>().success();
    }

    /**
     * 5545016-同步EDA埋点覆盖率配置
     * @param bean
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "transEdaBkConfig", method = RequestMethod.POST)
    @Attribute(sid = "5545016", name = "同步EDA埋点覆盖率配置")
    public ResDataBean<?> transEdaBkConfig(@RequestBody BaseBean<Req5545013IBean> bean) throws Exception {
        burypointsEdaSyncService.transEdaBkConfig(bean.getData());
        return new ResDataBean<>().success();
    }
    /**
     * 同步EDA后端埋点的元数据-服务端接口
     * @param bean
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "transEdaBkConfigServer", method = RequestMethod.POST)
    public ResDataBean<?> transEdaBkConfigServer(@RequestBody BaseBean<Req5545016IBean> bean) throws Exception {
        burypointsEdaSyncService.transEdaBkConfigServer(bean.getData());
        return new ResDataBean<>().success();
    }



    /**
     * 5545039-雄鹰新增/修改操作链路同步EDA
     * @param bean
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "syncEagleSystemToEda", method = RequestMethod.POST)
    public ResDataBean<?> syncEagleSystemToEda(@RequestBody BaseBean<Req5545039IBean> bean) throws  Exception{
        String edaUrl = burypointsEdaSyncService.syncEagleSystemToEda(bean.getData());
        Map<String,String> parameters = new HashMap<String,String>();
        parameters.put("edaUrl", edaUrl);
        return new ResDataBean<Map<String,String>>().success(parameters);
    }

}
