package com.wtyt.lgfesentryedamanagement.eda.controller;

import com.wtyt.lg.commons.bean.BaseBean;
import com.wtyt.lg.commons.bean.ResDataBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545030IBean;
import com.wtyt.lgfesentryedamanagement.eda.service.BurypointsEdaPointsService;
import com.wtyt.lgfesentryedamanagement.pub.annotation.Attribute;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.CommonToolkit;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.ValidateFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date ：2024/5/10 9:08
 */
@RestController
@RequestMapping("/edaPoints/")
public class BurypointsEdaPointsController {

    @Autowired
    private BurypointsEdaPointsService burypointsEdaPointsService;

    /**
     *  5545030-批量新增eda流程节点表(删除并新增) LGFEM.T_BURYPOINTS_EDA_POINTS
     * @param bean
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "batchInsert", method = RequestMethod.POST)
    @Attribute(sid = "5545030", name = "批量新增eda流程节点表(删除并新增)")
    public ResDataBean<?> batchInsert(@RequestBody BaseBean<Req5545030IBean> bean) throws Exception{
        Req5545030IBean data = CommonToolkit.checkReq(bean);
        ValidateFilter.getFilterMessage(data, ValidateFilter.Create.class);
        burypointsEdaPointsService.batchInsert(data);
        return new ResDataBean<>().success();
    }
}
