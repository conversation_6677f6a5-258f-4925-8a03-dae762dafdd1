package com.wtyt.lgfesentryedamanagement.eda.service;

import com.aliyun.dingtalkyida_1_0.models.BatchGetFormDataByIdListResponseBody;
import com.aliyun.dingtalkyida_1_0.models.GetFieldDefByUuidResponseBody;
import com.aliyun.dingtalkyida_1_0.models.SearchFormDatasResponseBody;
import com.google.common.collect.Lists;
import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.*;
import com.wtyt.lgfesentryedamanagement.eda.bean.yida.InstanceValueDto;
import com.wtyt.lgfesentryedamanagement.eda.bean.yida.YidaTableChangedThreadLocal;
import com.wtyt.lgfesentryedamanagement.pub.rpc.BbsGwayService;
import com.wtyt.lgfesentryedamanagement.pub.rpc.LugeService;
import com.wtyt.lgfesentryedamanagement.pub.rpc.RpcYdService;
import com.wtyt.lgfesentryedamanagement.pub.rpc.bean.request.Bbs5251117Request;
import com.wtyt.lgfesentryedamanagement.pub.rpc.bean.request.Bbs5251118Request;
import com.wtyt.lgfesentryedamanagement.pub.rpc.bean.request.Luge8000533Request;
import com.wtyt.lgfesentryedamanagement.pub.rpc.bean.request.Luge8000535Request;
import com.wtyt.lgfesentryedamanagement.pub.rpc.bean.response.Bbs5251117Response;
import com.wtyt.lgfesentryedamanagement.pub.rpc.bean.response.Bbs5251118Response;
import com.wtyt.lgfesentryedamanagement.pub.rpc.bean.response.Luge8000533Response;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.JsonToolkit;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.YidaDataToolKit;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.math.BigDecimal;
import java.util.List;

@Service
public class BurypointsEdaYidaService {

    private static final Logger log = LoggerFactory.getLogger(BurypointsEdaYidaService.class);

    @Autowired
    private Environment env;
    @Autowired
    private BurypointsEdaSyncService burypointsEdaSyncService;
    @Autowired
    private BbsGwayService bbsGwayService;
    @Autowired
    private RpcYdService rpcYdService;
    @Autowired
    private BurypointsEdaYidaDbService burypointsEdaYidaDbService;
    @Autowired
    private LugeService lugeService;

    public void syncOperateLink(Req5545017IBean data) throws Exception {
        TransEdaMasterBean transEdaMasterBean = new TransEdaMasterBean();
        // 数据转换
        transEdaMasterBean.setOptType(data.getOptType());
        transEdaMasterBean.setOptLinkId(data.getFormInstanceId());
        transEdaMasterBean.setEdaNo(data.getEdaNo());
        transEdaMasterBean.setEdaName(data.getEdaName());
        //edaType
        transEdaMasterBean.setEdaType(StringUtils.isBlank(data.getEdaType()) ? 0 : Integer.parseInt(data.getEdaType()));
        if (CollectionUtils.isNotEmpty(data.getSkuForm())) {
            transEdaMasterBean.setEdaProduct(data.getSkuForm().get(0).getInstanceId());
        }
        if (CollectionUtils.isNotEmpty(data.getFeatureModule())) {
            transEdaMasterBean.setEdaModule(data.getFeatureModule().get(0).getInstanceId());
        }
        // 维护ui, 需要获取到名称
        if (StringUtils.equals("1", data.getEdaType())) {
            //如果是功能点数据，则UI名称需要从接口获取
            if (CollectionUtils.isNotEmpty(data.getResponsibleUI()) && StringUtils.isNotBlank(data.getResponsibleUI().get(0))) {
                Bbs5251117Request bbs5251117Request = new Bbs5251117Request();
                bbs5251117Request.setUserId(data.getResponsibleUI().get(0));
                Bbs5251117Response bbs5251117Response = bbsGwayService.invoke5251117(bbs5251117Request);
                if (bbs5251117Response != null && null != bbs5251117Response.getUserInfo()) {
                    transEdaMasterBean.setMaintenanceUi(String.format("%s(%s)", bbs5251117Response.getUserInfo().getNickName(), bbs5251117Response.getUserInfo().getUserName()));
                }
            }
        } else {
            transEdaMasterBean.setMaintenanceUi(data.getResponsibleUIName());
        }
        // 维护团队，调用接口根据id获取到名称
        if (CollectionUtils.isNotEmpty(data.getResponsibleDevTeam())) {
            String depId = data.getResponsibleDevTeam().get(0);
            Bbs5251118Request bbs5251118Request = new Bbs5251118Request();
            bbs5251118Request.setDeptId(depId);
            Bbs5251118Response bbs5251118Response = bbsGwayService.invoke5251118(bbs5251118Request);
            if (bbs5251118Response != null) {
                transEdaMasterBean.setMaintenanceTeam(bbs5251118Response.getDeptName());
            }
        }
        //处理呈现状态
        if (StringUtils.equals("1", data.getIsShow())) {
            transEdaMasterBean.setEdaStatus(1);
        } else if (StringUtils.equals("2", data.getIsShow())) {
            transEdaMasterBean.setEdaStatus(2);
        } else if (StringUtils.equals("3", data.getIsShow())) {
            transEdaMasterBean.setEdaStatus(3);
        } else {
            transEdaMasterBean.setEdaStatus(0);
        }
        if (StringUtils.isNotBlank(data.getCoverageBase())) {
            transEdaMasterBean.setCoverageBaseline(new BigDecimal(data.getCoverageBase()));
        }
        transEdaMasterBean.setUnconventionalBaselineReason(data.getNotReachReason());

        log.info("操作链路同步到eda系统数据：{}", transEdaMasterBean);

        burypointsEdaSyncService.syncEdaMasterFromYiDa(transEdaMasterBean);

    }

    public void syncYidaFormDataByInstId(Req5545021IBean data) throws Exception {
        String formUuid = data.getFormUuid();
        String tableName = YidaDataToolKit.getTableNameByFormUuid(formUuid);
        String formInstanceId = data.getFormInstanceId();
        // openApi
        if ("2".equals(data.getOptType())) {
            // 删除处理
            if (burypointsEdaYidaDbService.checkTableExists(tableName)) {
                // 存在才删除，不存在则不处理
                // update {formUuid} set is_del = 0 where form_instance_id = {fromInstanceId}
                log.info("开始删除表数据：{},{}",tableName, formInstanceId);
                burypointsEdaYidaDbService.logicalDelete(tableName, formInstanceId);
            }
        } else {
            // 新增/修改处理
            BatchGetFormDataByIdListResponseBody.BatchGetFormDataByIdListResponseBodyResult instanceById = rpcYdService.getInstanceById(formUuid, formInstanceId);
            // 通过实例数据新增或更新表记录
            saveOrUpdateByInstanceData(formUuid, instanceById, "1");
        }
    }

    private void saveOrUpdateByInstanceData(String formUuid, BatchGetFormDataByIdListResponseBody.BatchGetFormDataByIdListResponseBodyResult instanceById, String syncType) throws Exception {
        try {
            String tableName = YidaDataToolKit.getTableNameByFormUuid(formUuid);
            // 实例json数据转换成对象
            List<InstanceValueDto> instanceComponantValueList = JsonToolkit.jsonToObject(instanceById.getInstanceValue(), JsonToolkit.getCollectionType(List.class, InstanceValueDto.class));
            if (CollectionUtils.isEmpty(instanceComponantValueList)) {
                throw new UnifiedBusinessException("数据异常或转换数据异常");
            }
            // 暂时从 title 中获取表备注
            String formName = YidaDataToolKit.fetchFormNameByTitle(instanceById.getTitle());
            // 判断表（表名：fromUuid）是否存在
            if (!burypointsEdaYidaDbService.checkTableExists(tableName)) {
                // 创建表, 查询表单组件信息
                log.info("开始创建表: {}", tableName);
                List<GetFieldDefByUuidResponseBody.GetFieldDefByUuidResponseBodyResult> fieldDefList = rpcYdService.getFieldDefByUuid(formUuid);
                burypointsEdaYidaDbService.createTable(tableName, formName, fieldDefList);
            } else {
                // 判断 instanceValue 中的字段是否多于 表中的字段, 多于则需要新增字段
                if (!YidaTableChangedThreadLocal.checkTableChanged(tableName) && !burypointsEdaYidaDbService.checkColumns(tableName, instanceComponantValueList)) {
                    // 有缺少的字段，需要增加缺少的字段
                    log.info("开始新增表字段: {}", tableName);
                    List<GetFieldDefByUuidResponseBody.GetFieldDefByUuidResponseBodyResult> fieldDefList = rpcYdService.getFieldDefByUuid(formUuid);
                    burypointsEdaYidaDbService.updateTable(tableName, fieldDefList);
                }
            }

            // 新增或更新数据
            burypointsEdaYidaDbService.saveOrUpdate(formUuid, instanceById, syncType);
        } catch (Exception e) {
            log.error("数据同步失败：formUuid:{}, formInstanceId:{}", formUuid, instanceById.getFormInstanceId());
            throw e;
        }

    }

    public void syncYidaFormDataAll(Req5545022IBean data) throws Exception {
        String formUuids = data.getFormUuids();
        List<String> formUuidList = Lists.newArrayList(formUuids.split(","));
        if (formUuidList.size() > 20) {
            throw new UnifiedBusinessException("一次最多支持20个表单全量同步");
        }
        String syncType = "2";// 全量同步

        YidaTableChangedThreadLocal.init();
        long successSize = 0;
        long failSize = 0;
        MultiValueMap<String, String> failListMap = new LinkedMultiValueMap<>();
        try {
            for (String formUuid : formUuidList) {
                log.info("开始同步表单:{}", formUuid);
                int currentPage = 1;
                while (true) {
                    List<SearchFormDatasResponseBody.SearchFormDatasResponseBodyData> dataList = rpcYdService.pageSearchFormDataList(formUuid, currentPage);
                    if (CollectionUtils.isEmpty(dataList)) {
                        log.info("已没有数据，跳出循环");
                        break;
                    }
                    for (SearchFormDatasResponseBody.SearchFormDatasResponseBodyData bodyData : dataList) {
                        try {
                            // 转换对象，两个对象的字段名基本一致
                            BatchGetFormDataByIdListResponseBody.BatchGetFormDataByIdListResponseBodyResult instanceData = JsonToolkit.jsonToObject(
                                    JsonToolkit.objectToJson(bodyData), BatchGetFormDataByIdListResponseBody.BatchGetFormDataByIdListResponseBodyResult.class
                            );
                            // 部分字段不一致，需要手动设置
                            instanceData.setCreateTimeGMT(bodyData.getCreatedTimeGMT());
                            instanceData.setModifier(bodyData.getModifierUserId());

                            saveOrUpdateByInstanceData(formUuid, instanceData, syncType);
                            successSize++;
                        } catch (Exception e) {
                            failListMap.add(formUuid, bodyData.getFormInstanceId());
                            failSize++;
                            log.warn("数据失败：{}, {}",formUuid, bodyData.getFormInstanceId(), e);
                        }
                    }
                    currentPage++;
                }
            }
        } finally {
            log.info("同步完成，总成功个数：{}, 失败个数：{}", successSize, failSize);
            if (failSize > 0) {
                log.info("失败的数据：{}", JsonToolkit.objectToJson(failListMap));
            }
            failListMap.clear();
            failListMap = null;
            YidaTableChangedThreadLocal.clear();
        }
    }

    public void syncYidaToTeambition(Req5545023IBean data) throws Exception {
        String operatorId = "";
        // 先从实际评审人员中获取，（钉钉userId -> tb userId）
        String archReviewerId = data.getArchReviewerId();
        if (StringUtils.isNotBlank(archReviewerId)) {
            Luge8000533Request luge8000533Request = new Luge8000533Request();
            luge8000533Request.setRefId("dinga4a007414b2a0b4c");
            luge8000533Request.setExtraUserId(archReviewerId);
            Luge8000533Response luge8000533Response = lugeService.invoke8000533(luge8000533Request);
            if (luge8000533Response != null && StringUtils.isNotBlank(luge8000533Response.getTbUserId())) {
                operatorId = luge8000533Response.getTbUserId();
            }
        }
        if (StringUtils.isBlank(operatorId)) {
            // 如果上面实际评审人是空，则还是使用执行者
            operatorId = data.getOperatorId();
        }

        // 8000535 接口
        Luge8000535Request luge8000535Request = new Luge8000535Request();
        luge8000535Request.setTaskId(data.getTaskId());
        luge8000535Request.setOperatorId(operatorId);
        luge8000535Request.setCustomfieldName(env.getProperty("yd.app.secondCard.tb.archReviewStatus", "架构评审"));
        luge8000535Request.addValue(data.getArchReviewStatus());
        lugeService.invoke8000535(luge8000535Request);
    }

}
