package com.wtyt.lgfesentryedamanagement.eda.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wtyt.lg.commons.exception.BaseTipException;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.EdaMasterListParam;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.EdaMasterParam;
import com.wtyt.lgfesentryedamanagement.eda.bean.vo.EdaMasterVo;
import com.wtyt.lgfesentryedamanagement.log.bean.LogUnifySaveBean;
import com.wtyt.lgfesentryedamanagement.log.service.BurypointsEdaLogService;
import com.wtyt.lgfesentryedamanagement.pub.bean.BaseTokenBean;
import com.wtyt.lgfesentryedamanagement.pub.bean.PageVo;
import com.wtyt.lgfesentryedamanagement.pub.enums.LogBusiTypeEnum;
import com.wtyt.lgfesentryedamanagement.pub.enums.LogOperTypeEnum;
import com.wtyt.lgfesentryedamanagement.pub.exceptions.ExceptionSupplier;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaMaster;
import com.wtyt.lgfesentryedamanagement.dao.mapper.TBurypointsEdaMasterMapper;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/1/29 16:29
 * @vesion 1.0
 * @desc
 */
@Service
public class BurypointsEdaMasterService {

    @Resource
    private TBurypointsEdaMasterMapper tBurypointsEdaMasterMapper;

    @Resource
    private BurypointsEdaLogService burypointsEdaLogService;


    /**
     * EDA增加
     * @param edaMasterParam
     * @throws BaseTipException
     */
    public void addEdaMaster(EdaMasterParam edaMasterParam) throws BaseTipException {
        //参数校验
        EdaMasterParam.paramCheck(edaMasterParam);
        //唯一性校验
        this.edaMasterUniqueCheck(edaMasterParam);
        //构建实体
        TBurypointsEdaMaster tBurypointsEdaMaster = EdaMasterParam.buildEntity(edaMasterParam);
        //插入数据库
        this.insertSelective(tBurypointsEdaMaster);
        //日志增加
        this.addLog(tBurypointsEdaMaster, null, edaMasterParam, LogOperTypeEnum.ADD.getValue(), LogBusiTypeEnum.T_BURYPOINTS_EDA_MASTER.getValue());
    }

    /**
     * 更新EDA
     * @param edaMasterParam
     * @throws BaseTipException
     */
    public void updateEdaMaster(EdaMasterParam edaMasterParam) throws BaseTipException {
        //参数校验
        EdaMasterParam.updateParamCheck(edaMasterParam);
        //校验EDA是否存在
        TBurypointsEdaMaster burypointsEdaMasterOld = checkIsExist(edaMasterParam.getEdaNo());
        //唯一性校验
        this.edaNameUniqueCheck(edaMasterParam);
        //构建实体
        TBurypointsEdaMaster tBurypointsEdaMasterNew = EdaMasterParam.buildEntity(edaMasterParam, burypointsEdaMasterOld);
        //更新数据库
        this.updateByPrimaryKeySelective(tBurypointsEdaMasterNew);
        //日志增加
        this.addLog(tBurypointsEdaMasterNew, burypointsEdaMasterOld, edaMasterParam, LogOperTypeEnum.EDIT.getValue(), LogBusiTypeEnum.T_BURYPOINTS_EDA_MASTER.getValue());
    }


    /**
     * 删除EDA
     * @param edaMasterParam
     * @throws BaseTipException
     */
    public void deleteEdaMaster(EdaMasterParam edaMasterParam) throws BaseTipException {
        //校验EDA是否存在
        TBurypointsEdaMaster burypointsEdaMasterOld = checkIsExist(edaMasterParam.getEdaNo());
        //构建实体
        TBurypointsEdaMaster tBurypointsEdaMasterNew = new TBurypointsEdaMaster();
        tBurypointsEdaMasterNew.setBurypointsEdaMasterId(burypointsEdaMasterOld.getBurypointsEdaMasterId());
        tBurypointsEdaMasterNew.setIsDel(1);
        tBurypointsEdaMasterNew.setEdaNo(burypointsEdaMasterOld.getEdaNo());
        tBurypointsEdaMasterNew.setEdaStatus(0);
        //更新数据库
        this.updateByPrimaryKeySelective(tBurypointsEdaMasterNew);
        //日志增加
        this.addLog(tBurypointsEdaMasterNew, burypointsEdaMasterOld, edaMasterParam, LogOperTypeEnum.DEL.getValue(), LogBusiTypeEnum.T_BURYPOINTS_EDA_MASTER.getValue());
    }

    /**
     * EDA分页列表
     * @param edaMasterListParam
     * @return
     */
    public PageVo<EdaMasterVo> edaMasterList(EdaMasterListParam edaMasterListParam) {

        //分页开始
        PageHelper.startPage(edaMasterListParam.pageNumber(),edaMasterListParam.pageSize());
        if (StringUtils.isBlank(edaMasterListParam.getEdaType())) {
            edaMasterListParam.setEdaType("0");
        }
        List<TBurypointsEdaMaster> edaMasterList =tBurypointsEdaMasterMapper.edaMasterList(edaMasterListParam);
        if (CollectionUtil.isEmpty(edaMasterList)) {
            return PageVo.buildEmpty();
        }
        PageInfo<TBurypointsEdaMaster> pageInfo = new PageInfo<>(edaMasterList);
        //结果处理
        List<EdaMasterVo> vos = handleResult(pageInfo.getList());
        return PageVo.builderResult(pageInfo.getTotal(), vos);
    }


    private List<EdaMasterVo> handleResult(List<TBurypointsEdaMaster> edaMasterList) {
        List<EdaMasterVo> vos = new ArrayList<>();
        edaMasterList.forEach(edaMaster -> vos.add(EdaMasterVo.builder(edaMaster)));
        return vos;
    }


    /**
     * 统一日志入口
     * @param tBurypointsEdaMaster
     * @param oldTBurypointsEdaMaster
     * @param bean
     * @param logType
     * @param optType
     * @throws BaseTipException
     */
    private void addLog(TBurypointsEdaMaster tBurypointsEdaMaster, TBurypointsEdaMaster oldTBurypointsEdaMaster, BaseTokenBean bean, int logType, int optType) throws BaseTipException {
        LogUnifySaveBean logUnifySaveBean = new LogUnifySaveBean();
        logUnifySaveBean.setCurrentBean(tBurypointsEdaMaster);
        logUnifySaveBean.setOriginalBean(oldTBurypointsEdaMaster);
        logUnifySaveBean.setOptTeam(bean.getOptTeam());
        logUnifySaveBean.setOptUserName(bean.getOptUserName());
        logUnifySaveBean.setLogType(logType);
        logUnifySaveBean.setOptType(optType);
        logUnifySaveBean.setEdaBranchNo(tBurypointsEdaMaster.getEdaNo());
        burypointsEdaLogService.addLog(logUnifySaveBean);
    }

    private void edaMasterUniqueCheck(EdaMasterParam edaMasterParam) {
        edaNoUniqueCheck(edaMasterParam);
        edaNameUniqueCheck(edaMasterParam);
    }

    private void edaNoUniqueCheck(EdaMasterParam edaMasterParam) {
        Assert.isNull(tBurypointsEdaMasterMapper.selectByEdaNo(edaMasterParam.getEdaNo()), new ExceptionSupplier("当前EDA编号已存在"));
    }

    private void edaNameUniqueCheck(EdaMasterParam edaMasterParam) {
        Assert.isNull(Optional.ofNullable(tBurypointsEdaMasterMapper.selectByEdaName(edaMasterParam.getEdaName()))
                .filter(edaMaster -> ObjectUtil.notEqual(edaMasterParam.getEdaNo(), edaMaster.getEdaNo()))
                .orElse(null), new ExceptionSupplier("当前EDA名已存在"));
    }

    public TBurypointsEdaMaster checkIsExist(String edaNo) {
        TBurypointsEdaMaster tBurypointsEdaMaster = tBurypointsEdaMasterMapper.selectByEdaNo(edaNo);
        Assert.notNull(tBurypointsEdaMaster, new ExceptionSupplier("当前EDA不存在"));
        Assert.isTrue(tBurypointsEdaMaster.getIsDel() == 0, new ExceptionSupplier("当前EDA已删除"));
        return tBurypointsEdaMaster;
    }


    public int insertSelective(TBurypointsEdaMaster record) {
        return tBurypointsEdaMasterMapper.insertSelective(record);
    }


    public int updateByPrimaryKeySelective(TBurypointsEdaMaster record) {
        return tBurypointsEdaMasterMapper.updateByPrimaryKeySelective(record);
    }

}
