package com.wtyt.lgfesentryedamanagement.eda.service;

import com.aliyun.dingtalkyida_1_0.models.BatchGetFormDataByIdListResponseBody;
import com.aliyun.dingtalkyida_1_0.models.GetFieldDefByUuidResponseBody;
import com.wtyt.generator.toolkit.UidToolkit;
import com.wtyt.generator.utils.DateUtils;
import com.wtyt.lgfesentryedamanagement.dao.mapper.EdaYidaSyncMapper;
import com.wtyt.lgfesentryedamanagement.eda.bean.yida.InstanceValueDto;
import com.wtyt.lgfesentryedamanagement.eda.bean.yida.YidaTableChangedThreadLocal;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.DateToolkit;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.JsonToolkit;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.YidaDataToolKit;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 宜搭同步数表操作服务：创建、修改表，表数据增删改等操作封装
 */
@Service
public class BurypointsEdaYidaDbService {

    private static final Logger log = LoggerFactory.getLogger(BurypointsEdaYidaDbService.class);

    @Resource
    private EdaYidaSyncMapper edaYidaSyncMapper;

    /** 创建表sql模板 */
    private String CREATE_TABLE_TEMPLATE = "CREATE TABLE {tableName} (\n" +
            "   primary_key_id BIGINT NOT NULL PRIMARY KEY COMMENT '主键',\n" +
            "   form_instance_id VARCHAR(64) NULL COMMENT '表单实例id',\n" +
            "   title VARCHAR(512) NULL COMMENT '标题',\n" +
            "   creator_user_id VARCHAR(64) NULL COMMENT '创建人id',\n" +
            "   modifier_user_id VARCHAR(64) NULL COMMENT '修改人id',\n" +
            "   is_del TINYINT NOT NULL DEFAULT 0 COMMENT '是否删除 ,1:已删除,0:未删除',\n" +
            "   created_time DATETIME NULL COMMENT '创建时间',\n" +
            "   last_modified_time DATETIME NULL COMMENT '修改时间',\n" +
            "   first_sync_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '第一次同步时间',\n" +
            "   last_sync_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最近一次同步时间',\n" +
            "   sync_type TINYINT NOT NULL DEFAULT 1 COMMENT '同步方式 ,1:增量同步,2:全量同步',\n" +
            "   {formFieldListSql}\n" +
            ") COMMENT '{tableComment}'";
    // ALTER TABLE {tableName} ADD INDEX IDX_20240304105938040 (first_sync_time)
    /** 创建表索引模板 */
    private String TABLE_IDX_TEMPLATE = "ALTER TABLE {tableName} ADD INDEX {indexName} ({columnName})";
    /** 增加表字段sql模板 */
    private String UPDATE_TABLE_TEMPLATE = "ALTER TABLE {tableName} ADD COLUMN {columnName} {columnType} NULL COMMENT '{columnComment}'";

    /**
     * 检查表是否存在
     * @param tableName
     * @return
     */
    public boolean checkTableExists(String tableName) {
        Map<String, String> result = edaYidaSyncMapper.checkTableExistsWithShow(tableName);
        return MapUtils.isNotEmpty(result);
    }

    /**
     * 查询表所有字段名(已转换成大写字母)
     * @param tableName
     * @return
     */
    public Set<String> allColumNames(String tableName) {
        List<Map<String, String>> result = edaYidaSyncMapper.allColumnsFromTableWithShow(tableName);
        if (CollectionUtils.isEmpty(result)) {
            return new HashSet<>();
        } else {
            return result.stream().map(map -> map.get("Field").toUpperCase()).collect(Collectors.toSet());
        }
    }

    /**
     * 创建表
     *
     * @param tableName    : 表名：formUuid
     * @param formName
     * @param fieldDefList : 表单组件定义
     * @return
     */
    public void createTable(String tableName, String formName, List<GetFieldDefByUuidResponseBody.GetFieldDefByUuidResponseBodyResult> fieldDefList) {
        String createTableSql = CREATE_TABLE_TEMPLATE.replace("{tableName}", tableName).replace("{tableComment}", formName);
        StringBuilder formFieldListSql = new StringBuilder();
        for (int i = 0; i < fieldDefList.size(); i++) {
            GetFieldDefByUuidResponseBody.GetFieldDefByUuidResponseBodyResult field = fieldDefList.get(i);
            String fieldId = field.getFieldId();
            String labelName  = YidaDataToolKit.fetchComponentDbComment(field);
            String dbType = YidaDataToolKit.getComponentDbType(field.getComponentName());
            // 拼装字段sql
            formFieldListSql.append(" ").append(fieldId).append(" ").append(dbType).append(" NULL COMMENT '").append(labelName).append("'");
            if (i == fieldDefList.size() -1) {
                // 随后一个不需要增加最后的分隔符
            } else {
                formFieldListSql.append(",\n");
            }
        }
        createTableSql = createTableSql.replace("{formFieldListSql}", formFieldListSql.toString());

        // 创建表
        edaYidaSyncMapper.executeSql(createTableSql);

        // 索引： 给 FORM_INSTANCE_ID 和 FIRST_SYNC_TIME 字段增加索引
        String nowFmt = DateUtils.formatDate(new Date(), "yyyyMMddHHmmssSSS");
        String formInstIdIdxSql = TABLE_IDX_TEMPLATE.replace("{tableName}", tableName).replace("{indexName}", "IDX_FORM_INST_" + nowFmt).replace("{columnName}", "form_instance_id");
        String firstSyncTimeIdxSql = TABLE_IDX_TEMPLATE.replace("{tableName}", tableName).replace("{indexName}", "IDX_SYNC_TIME_" + nowFmt).replace("{columnName}", "first_sync_time");
        edaYidaSyncMapper.executeSql(formInstIdIdxSql);
        edaYidaSyncMapper.executeSql(firstSyncTimeIdxSql);
    }


    public boolean checkColumns(String formUuid, List<InstanceValueDto> valueList) throws Exception {
        Set<String> columnSet = allColumNames(formUuid);
        for (InstanceValueDto instanceValueDto : valueList) {
            // 转换成大写判断
            if (!columnSet.contains(instanceValueDto.getFieldId().toUpperCase())) {
                // 字段其中有一个没有则，不满足条件
                return false;
            }
        }
        return true;
    }

    /**
     * 更新表，主要是新增字段
     * @param tableName
     * @param fieldDefList
     */
    public void updateTable(String tableName, List<GetFieldDefByUuidResponseBody.GetFieldDefByUuidResponseBodyResult> fieldDefList) {
        Set<String> columnSet = allColumNames(tableName);
        for (GetFieldDefByUuidResponseBody.GetFieldDefByUuidResponseBodyResult field : fieldDefList) {
            if (columnSet.contains(field.getFieldId().toUpperCase())) {
                // 已存在的字段，不处理
                continue;
            }
            String fieldId = field.getFieldId();
            String labelName  = YidaDataToolKit.fetchComponentDbComment(field);
            String dbType = YidaDataToolKit.getComponentDbType(field.getComponentName());
            log.info("新增字段：{}-{}-{}", fieldId, labelName, dbType);
            // 不存在的字段，需要新增字段
            String sql = UPDATE_TABLE_TEMPLATE.replace("{tableName}", tableName)
                    .replace("{columnName}", fieldId)
                    .replace("{columnType}", dbType)
                    .replace("{columnComment}", labelName);
            edaYidaSyncMapper.executeSql(sql);
        }
        YidaTableChangedThreadLocal.setTableChanged(tableName);
    }

    /**
     * 逻辑删除数据
     * @param tableName
     * @param formInstanceId
     */
    public void logicalDelete(String tableName, String formInstanceId) {
        edaYidaSyncMapper.logicDelete(tableName, formInstanceId);
    }

    public void saveOrUpdate(String formUuid, BatchGetFormDataByIdListResponseBody.BatchGetFormDataByIdListResponseBodyResult instanceById, String syncType) throws Exception {
        // String formUuid = instanceById.getFormUuid(); 实际返回的是null!!!!!
        String tableName = YidaDataToolKit.getTableNameByFormUuid(formUuid); // formUuid 有特殊字符-,
        String formInstanceId = instanceById.getFormInstanceId();
        if (edaYidaSyncMapper.checkExistByFormInstanceId(tableName, formInstanceId) > 0) {
            // 更新
            log.info("开始更新数据：{}, {}", formUuid, formInstanceId);
            Map<String, String> dataMap = fetchDataMap(instanceById, "2", syncType, tableName);

            edaYidaSyncMapper.updateByInstanceId(tableName, formInstanceId, dataMap);
        } else {
            // 新增
            // INSERT INTO T_DBA_SAMPLE01(DBA_SAMPLE01_ID,COL1,COL2,COL3,IS_DEL,CREATED_TIME,LAST_MODIFIED_TIME,NOTE) VALUES (1,99,'AAA','{"name":"Polr","age":18}',0,SYSDATE(),SYSDATE(),'DBA测试数据');
            log.info("开始新增数据：{}, {}", formUuid, formInstanceId);
            Map<String, String> dataMap = fetchDataMap(instanceById, "1", syncType, tableName);

            edaYidaSyncMapper.insertData(tableName, dataMap);
        }
    }

    /**
     *
     * @param instanceById
     * @param type  类型， 1-新增，2-更新
     * @param syncType 同步类型，1:增量同步，2：全量同步
     * @return
     * @throws IOException
     */
    private Map<String, String> fetchDataMap(BatchGetFormDataByIdListResponseBody.BatchGetFormDataByIdListResponseBodyResult instanceById, String type, String syncType, String tableName) throws Exception {
        Map<String, String> dataMap = new LinkedHashMap<>();

        // 公共字段
        if ("1".equals(type)) {
             // 新增时多于的字段
            dataMap.put("primary_key_id", UidToolkit.generateUidString());
            dataMap.put("form_instance_id", instanceById.getFormInstanceId());
        }
        dataMap.put("title", blankNull(YidaDataToolKit.fetchRealTitleZhCn(instanceById.getTitle())));
        dataMap.put("creator_user_id", instanceById.getCreatorUserId());
        dataMap.put("modifier_user_id", instanceById.getModifyUser().getUserId());
        dataMap.put("created_time", DateToolkit.getFormatDate(instanceById.getCreateTimeGMT(), "yyyy-MM-dd'T'HH:mm'Z'", DateToolkit.YYYY_MM_DD_HH_MM)); // 2024-03-05T14:35Z
        dataMap.put("last_modified_time", DateToolkit.getFormatDate(instanceById.getModifiedTimeGMT(), "yyyy-MM-dd'T'HH:mm'Z'", DateToolkit.YYYY_MM_DD_HH_MM));
        if ("2".equals(type)) {
            // 更新时多于的字段
            dataMap.put("last_sync_time", DateUtils.formatByDateTimePattern(new Date()));
        }
        dataMap.put("sync_type", syncType);// 1:增量同步，2：全量同步

        Set<String> allColumNames = allColumNames(tableName);
        // 组件字段
        List<InstanceValueDto> instanceComponantValueList = JsonToolkit.jsonToObject(instanceById.getInstanceValue(), JsonToolkit.getCollectionType(List.class, InstanceValueDto.class));
        for (InstanceValueDto instanceValueDto : instanceComponantValueList) {
            String fieldId = instanceValueDto.getFieldId();
            if (!allColumNames.contains(fieldId.toUpperCase())) {
                // 有些老数据会有多余的组件，存在已有数据时删除表单中的某个组件则会导致老数据会存在多余的组件，需要过滤掉
                log.info("存在多余的组件：{}，过滤掉", fieldId);
                continue;
            }
            // 需要根据组件类型从不同格式中取数据，暂时都使用默认的取数方式
            dataMap.put(fieldId, blankNull(YidaDataToolKit.fetch2DbValue(instanceValueDto)));
        }
        log.info("实际获取数据：{}", JsonToolkit.objectToJson(dataMap));
        return dataMap;
    }

    /**
     * 如果字符串为空，则返回null
     * @param source
     * @return
     */
    private String blankNull(String source) {
        if (StringUtils.isBlank(source)) {
            return null;
        }
        return source;
    }

    private String dbCharValue(String value) {
        if (StringUtils.isBlank(value)) {
            return "null";
        } else if (StringUtils.isEmpty(value)) {
            return "''";
        }
        value = YidaDataToolKit.escapeDbStr(value);
        return "'" + value + "'";
    }

    public static void main(String[] args) {
        String value = "[{\"formType\":\"receipt\",\"instanceId\":\"FINST-G9766T716VFIM4C9EDN8DDJ4FWUV2OEXP8YSLZ581\",\"formUuid\":\"FORM-26442A65A42743B0AFB0CDDB80DE92D54AYV\",\"subTitle\":\"[\\\"rz090\\\"]\",\"appType\":\"APP_B4YS2RVCKTP8NI5V14W7\",\"title\":\"运输任务管理\"}]";
        System.out.println(YidaDataToolKit.escapeDbStr(value));
    }
}
