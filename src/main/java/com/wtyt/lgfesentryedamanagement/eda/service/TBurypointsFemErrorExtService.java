package com.wtyt.lgfesentryedamanagement.eda.service;

import com.wtyt.generator.toolkit.UidToolkit;
import com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsFemErrorExtBean;
import com.wtyt.lgfesentryedamanagement.dao.mapper.TBurypointsFemErrorExtMapper;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545044IBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545045IBean;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class TBurypointsFemErrorExtService {

    @Resource
    private TBurypointsFemErrorExtMapper tBurypointsFemErrorExtMapper;


    /**
     * 批量新增异常原因上报 5545044
     * @param data
     * @throws Exception
     */
    public void batchInsert(Req5545044IBean data)  throws Exception {
        // log.info("token注入的user信息："+data);
        List<TBurypointsFemErrorExtBean> insertList = new ArrayList<>();

        TBurypointsFemErrorExtBean bean = new TBurypointsFemErrorExtBean();
        Long drawioFemErrorExtId = UidToolkit.generateUidDefault();
        bean.setDrawioFemErrorExtId(drawioFemErrorExtId);
        bean.setDrawioFemErrorId(data.getDrawioFemErrorId());
        bean.setErrorReason(data.getErrorReason());
        bean.setReportUserName(data.getRealName());
       
        insertList.add(bean);

        tBurypointsFemErrorExtMapper.batchInsert(insertList);
    }

    /**
     * 修改异常上报 5545045
     * @param data
     * @throws Exception
     */
    public void updateOne(Req5545045IBean data)  throws Exception {
        // log.info("token注入的user信息："+data);
        TBurypointsFemErrorExtBean bean = new TBurypointsFemErrorExtBean();

        bean.setDrawioFemErrorExtId(data.getDrawioFemErrorExtId());
        bean.setDrawioFemErrorId(data.getDrawioFemErrorId());
        bean.setErrorReason(data.getErrorReason());
        bean.setErrorSolution(data.getErrorSolution());
        bean.setIssueHandler(data.getIssueHandler());
        bean.setReportUserName(data.getRealName());

        tBurypointsFemErrorExtMapper.updateOne(bean);
    }

}
