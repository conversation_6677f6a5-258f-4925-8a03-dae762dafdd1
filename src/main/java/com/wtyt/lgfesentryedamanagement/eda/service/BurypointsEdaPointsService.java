package com.wtyt.lgfesentryedamanagement.eda.service;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.wtyt.generator.toolkit.UidToolkit;
import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import com.wtyt.lg.commons.toolkits.CollectionToolkit;
import com.wtyt.lgfesentryedamanagement.dao.bean.TBuryPointsEdaPoints;
import com.wtyt.lgfesentryedamanagement.dao.bean.TBuryPointsEleColumn;
import com.wtyt.lgfesentryedamanagement.dao.bean.TBuryPointsEleField;
import com.wtyt.lgfesentryedamanagement.dao.mapper.TBuryPointsEdaPointsMapper;
import com.wtyt.lgfesentryedamanagement.dao.mapper.TBuryPointsEleColumnMapper;
import com.wtyt.lgfesentryedamanagement.dao.mapper.TBuryPointsEleFieldMapper;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545030IBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date ：2024/5/10 9:09
 */
@Service
@Slf4j
public class BurypointsEdaPointsService {

    @Resource
    private TBuryPointsEdaPointsMapper edaPointsMapper;
    @Resource
    private TBuryPointsEleColumnMapper tBuryPointsEleColumnMapper;
    @Resource
    private TBuryPointsEleFieldMapper tBuryPointsEleFieldMapper;

    public void batchInsert(Req5545030IBean data)  throws Exception {
        List<TBuryPointsEdaPoints> dataList = data.getDataList();
        List<TBuryPointsEleColumn> tBuryPointsEleColumnList = new ArrayList<>();
        List<TBuryPointsEleField> tBuryPointsEleFieldList = new ArrayList<>();
        if (CollectionToolkit.isEmpty(dataList)) {
            throw new UnifiedBusinessException("dataList不能为空");
        }
        String optLinkId = dataList.get(0).getOptLinkId();
        if (StringUtils.isBlank(optLinkId)) {
            throw new UnifiedBusinessException("操作链路不能为空");
        }
        // 先删除操作链路下的所有数据
        edaPointsMapper.delByOptLinkId(optLinkId);
        // 删除关联的数据
        tBuryPointsEleColumnMapper.delByPointsOptLinkId(optLinkId);
        tBuryPointsEleFieldMapper.delByOptLinkId(optLinkId, "1");
        for (TBuryPointsEdaPoints points : dataList) {
            // 设置特定字段数据, 及空字段的默认值
            points.setBurypointsEdaPointsId(UidToolkit.generateUidDefault());
            if (StringUtils.isBlank(points.getEdaMxcellId())) {
                points.setEdaMxcellId("-1");
            }
            if (StringUtils.isBlank(points.getEdaMxcellParentId())) {
                points.setEdaMxcellParentId("-1");
            }
            if (StringUtils.isBlank(points.getEdaMxcellSourceId())) {
                points.setEdaMxcellSourceId("-1");
            }
            if (StringUtils.isBlank(points.getEdaMxcellTargetId())) {
                points.setEdaMxcellTargetId("-1");
            }
            if (points.getEdaLinkLoc() == null) {
                points.setEdaLinkLoc(-1L);
            }
            //处理埋点表字段的查询条件
            if (StringUtils.isNotBlank(points.getEleColumnJson())) {
                JSONObject eleColumnJson = JSONUtil.parseObj(points.getEleColumnJson());
                TBuryPointsEleColumn tBuryPointsEleColumn = new TBuryPointsEleColumn();
                tBuryPointsEleColumn.setBurypointsEleColumnId(UidToolkit.generateUidString());
                if (eleColumnJson.containsKey("RESOURCE_NAME")) {
                    tBuryPointsEleColumn.setResourceName(eleColumnJson.getStr("RESOURCE_NAME"));
                }
                if (eleColumnJson.containsKey("EVENT_DESC")) {
                    tBuryPointsEleColumn.setEventDesc(eleColumnJson.getStr("EVENT_DESC"));
                }
                if (eleColumnJson.containsKey("FROM_PAGE_RESOURCE_ID")) {
                    tBuryPointsEleColumn.setFromPageResourceId(eleColumnJson.getStr("FROM_PAGE_RESOURCE_ID"));
                }
                if (eleColumnJson.containsKey("FROM_RESOURCE_ID")) {
                    tBuryPointsEleColumn.setFromResourceId(eleColumnJson.getStr("FROM_RESOURCE_ID"));
                }
                if (eleColumnJson.containsKey("BELONG_RESOURCE_NAME")) {
                    tBuryPointsEleColumn.setBelongResourceName(eleColumnJson.getStr("BELONG_RESOURCE_NAME"));
                }
                if (eleColumnJson.containsKey("BELONG_RESOURCE_ID")) {
                    tBuryPointsEleColumn.setBelongResourceId(eleColumnJson.getStr("BELONG_RESOURCE_ID"));
                }
                tBuryPointsEleColumnList.add(tBuryPointsEleColumn);
                points.setBurypointsEleColumnId(tBuryPointsEleColumn.getBurypointsEleColumnId());
            }
            //处理ext的查询条件
            if (StringUtils.isNotBlank(points.getEleExtJson())) {
                JSONObject eleExtJson = JSONUtil.parseObj(points.getEleExtJson());
                Set<String> keys = eleExtJson.keySet();
                for (String key : keys) {
                    TBuryPointsEleField tBuryPointsEleField = new TBuryPointsEleField();
                    tBuryPointsEleField.setBurypointsEleFieldId(UidToolkit.generateUidString());
                    tBuryPointsEleField.setRelType("1");
                    tBuryPointsEleField.setRelId(String.valueOf(points.getBurypointsEdaPointsId()));
                    tBuryPointsEleField.setEleField(key);
                    tBuryPointsEleField.setEleFieldVal(eleExtJson.getStr(key));
                    JSONObject temp = new JSONObject();
                    temp.set(tBuryPointsEleField.getEleField(), tBuryPointsEleField.getEleFieldVal());
                    tBuryPointsEleField.setEleJson(JSONUtil.toJsonStr(temp));
                    tBuryPointsEleFieldList.add(tBuryPointsEleField);
                }
            }
        }
        if (CollectionToolkit.isNotEmpty(tBuryPointsEleColumnList)) {
            tBuryPointsEleColumnMapper.batchInsert(tBuryPointsEleColumnList);
        }
        if (CollectionToolkit.isNotEmpty(tBuryPointsEleFieldList)) {
            tBuryPointsEleFieldMapper.batchInsert(tBuryPointsEleFieldList);
        }
        // 再批量新增
        edaPointsMapper.batchInsert(dataList);
    }
}
