package com.wtyt.lgfesentryedamanagement.eda.service;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.gson.Gson;
import com.wtyt.generator.toolkit.UidToolkit;
import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import com.wtyt.lg.commons.toolkits.CollectionToolkit;
import com.wtyt.lgfesentryedamanagement.dao.bean.TBuryPointsEleColumn;
import com.wtyt.lgfesentryedamanagement.dao.bean.TBuryPointsEleField;
import com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaCoverBk;
import com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaCoverFt;
import com.wtyt.lgfesentryedamanagement.dao.mapper.TBuryPointsEleColumnMapper;
import com.wtyt.lgfesentryedamanagement.dao.mapper.TBuryPointsEleFieldMapper;
import com.wtyt.lgfesentryedamanagement.dao.mapper.TBurypointsEdaCoverBkMapper;
import com.wtyt.lgfesentryedamanagement.dao.mapper.TBurypointsEdaCoverFtMapper;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545034IBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 *
 */
@Service
@Slf4j
public class BurypointsEdaCoverService {

    @Resource
    private TBurypointsEdaCoverBkMapper tBurypointsEdaCoverBkMapper;
    @Resource
    private TBurypointsEdaCoverFtMapper tBurypointsEdaCoverFtMapper;
    @Resource
    private TBuryPointsEleColumnMapper tBuryPointsEleColumnMapper;
    @Resource
    private TBuryPointsEleFieldMapper tBuryPointsEleFieldMapper;

    /**
     * 批量维护eda覆盖率(新增或更新)
     * @param data
     * @throws Exception
     */
    public void batchInsert(Req5545034IBean data)  throws Exception {
        List<Req5545034IBean> dataList = data.getDataList();
        if (CollectionToolkit.isEmpty(dataList)) {
            throw new UnifiedBusinessException("dataList不能为空");
        }
        for (Req5545034IBean req5545034IBean : dataList) {
            if (StringUtils.isBlank(req5545034IBean.getEdaMxcellId())) {
                throw new UnifiedBusinessException("edaMxcellId不能为空");
            }
        }
        String optLinkId = data.getOptLinkId();
        if (StringUtils.isBlank(optLinkId)) {
            throw new UnifiedBusinessException("操作链路不能为空");
        }
        List<TBurypointsEdaCoverBk> tBurypointsEdaCoverBkList = new ArrayList<>();
        List<TBurypointsEdaCoverFt> tBurypointsEdaCoverFtList = new ArrayList<>();
        List<TBuryPointsEleColumn> tBuryPointsEleColumnList = new ArrayList<>();
        List<TBuryPointsEleField> tBuryPointsEleFieldList = new ArrayList<>();
        // 先删除操作链路下的所有数据
        tBurypointsEdaCoverBkMapper.delByOptLinkId(optLinkId);
        tBurypointsEdaCoverFtMapper.delByOptLinkId(optLinkId);
        // 删除关联的数据
        tBuryPointsEleColumnMapper.delByCoverOptLinkId(optLinkId);
        tBuryPointsEleFieldMapper.delByOptLinkId(optLinkId, "2");
        for (Req5545034IBean req5545034IBean : dataList) {
            if (StringUtils.equals("1", req5545034IBean.getConfigType())) {
                //后端覆盖率
                tBurypointsEdaCoverBkList.add(buildEntityForBk(req5545034IBean, optLinkId));
            } else if (StringUtils.equals("2", req5545034IBean.getConfigType())) {
                //前端覆盖率
                tBurypointsEdaCoverFtList.add(buildEntityForFt(req5545034IBean, optLinkId, tBuryPointsEleColumnList, tBuryPointsEleFieldList));
            } else {
                throw new UnifiedBusinessException("configType不合法");
            }
        }
        // 再批量新增
        if (CollectionToolkit.isNotEmpty(tBurypointsEdaCoverBkList)) {
            tBurypointsEdaCoverBkMapper.batchInsert(tBurypointsEdaCoverBkList);
        }
        if (CollectionToolkit.isNotEmpty(tBurypointsEdaCoverFtList)) {
            tBurypointsEdaCoverFtMapper.batchInsert(tBurypointsEdaCoverFtList);
        }
        if (CollectionToolkit.isNotEmpty(tBuryPointsEleColumnList)) {
            tBuryPointsEleColumnMapper.batchInsert(tBuryPointsEleColumnList);
        }
        if (CollectionToolkit.isNotEmpty(tBuryPointsEleFieldList)) {
            tBuryPointsEleFieldMapper.batchInsert(tBuryPointsEleFieldList);
        }
    }

    /**
     * 组装后端覆盖率数据
     * @param edaBkConfigParam
     * @param optLinkId
     * @return
     */
    public static TBurypointsEdaCoverBk buildEntityForBk(Req5545034IBean edaBkConfigParam, String optLinkId) {
        TBurypointsEdaCoverBk burypointsEdaBkConfig = new TBurypointsEdaCoverBk();
        BeanUtils.copyProperties(edaBkConfigParam, burypointsEdaBkConfig);
        burypointsEdaBkConfig.setBurypointsEdaCoverBkId(UidToolkit.generateUidString());
        burypointsEdaBkConfig.setOptLinkId(optLinkId);
        Gson gson = new Gson();
        Optional.ofNullable(edaBkConfigParam.getInterfaceFilters()).ifPresent(interfaceFilters -> burypointsEdaBkConfig.setInterfaceFilters(gson.toJson(interfaceFilters)));
        return burypointsEdaBkConfig;
    }

    /**
     * 组装前端覆盖率数据
     * @param edaBkConfigParam
     * @param optLinkId
     * @return
     */
    public static TBurypointsEdaCoverFt buildEntityForFt(Req5545034IBean edaBkConfigParam, String optLinkId, List<TBuryPointsEleColumn> tBuryPointsEleColumnList, List<TBuryPointsEleField> tBuryPointsEleFieldList) {
        TBurypointsEdaCoverFt burypointsEdaFtConfig = new TBurypointsEdaCoverFt();
        BeanUtils.copyProperties(edaBkConfigParam, burypointsEdaFtConfig);
        burypointsEdaFtConfig.setBurypointsEdaCoverFtId(UidToolkit.generateUidString());
        burypointsEdaFtConfig.setOptLinkId(optLinkId);
        //处理埋点表字段的查询条件
        if (StringUtils.isNotBlank(edaBkConfigParam.getEleColumnJson())) {
            JSONObject eleColumnJson = JSONUtil.parseObj(edaBkConfigParam.getEleColumnJson());
            TBuryPointsEleColumn tBuryPointsEleColumn = new TBuryPointsEleColumn();
            tBuryPointsEleColumn.setBurypointsEleColumnId(UidToolkit.generateUidString());
            if (eleColumnJson.containsKey("RESOURCE_NAME")) {
                tBuryPointsEleColumn.setResourceName(eleColumnJson.getStr("RESOURCE_NAME"));
            }
            if (eleColumnJson.containsKey("EVENT_DESC")) {
                tBuryPointsEleColumn.setEventDesc(eleColumnJson.getStr("EVENT_DESC"));
            }
            if (eleColumnJson.containsKey("FROM_PAGE_RESOURCE_ID")) {
                tBuryPointsEleColumn.setFromPageResourceId(eleColumnJson.getStr("FROM_PAGE_RESOURCE_ID"));
            }
            if (eleColumnJson.containsKey("FROM_RESOURCE_ID")) {
                tBuryPointsEleColumn.setFromResourceId(eleColumnJson.getStr("FROM_RESOURCE_ID"));
            }
            if (eleColumnJson.containsKey("BELONG_RESOURCE_NAME")) {
                tBuryPointsEleColumn.setBelongResourceName(eleColumnJson.getStr("BELONG_RESOURCE_NAME"));
            }
            if (eleColumnJson.containsKey("BELONG_RESOURCE_ID")) {
                tBuryPointsEleColumn.setBelongResourceId(eleColumnJson.getStr("BELONG_RESOURCE_ID"));
            }
            tBuryPointsEleColumnList.add(tBuryPointsEleColumn);
            burypointsEdaFtConfig.setBurypointsEleColumnId(tBuryPointsEleColumn.getBurypointsEleColumnId());
        }
        //处理ext的查询条件
        if (StringUtils.isNotBlank(edaBkConfigParam.getEleExtJson())) {
            JSONObject eleExtJson = JSONUtil.parseObj(edaBkConfigParam.getEleExtJson());
            Set<String> keys = eleExtJson.keySet();
            for (String key : keys) {
                TBuryPointsEleField tBuryPointsEleField = new TBuryPointsEleField();
                tBuryPointsEleField.setBurypointsEleFieldId(UidToolkit.generateUidString());
                tBuryPointsEleField.setRelType("2");
                tBuryPointsEleField.setRelId(String.valueOf(burypointsEdaFtConfig.getBurypointsEdaCoverFtId()));
                tBuryPointsEleField.setEleField(key);
                tBuryPointsEleField.setEleFieldVal(eleExtJson.getStr(key));
                JSONObject temp = new JSONObject();
                temp.set(tBuryPointsEleField.getEleField(), tBuryPointsEleField.getEleFieldVal());
                tBuryPointsEleField.setEleJson(JSONUtil.toJsonStr(temp));
                tBuryPointsEleFieldList.add(tBuryPointsEleField);
            }
        }
        return burypointsEdaFtConfig;
    }
}
