package com.wtyt.lgfesentryedamanagement.eda.service;

import com.wtyt.generator.toolkit.UidToolkit;
import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import com.wtyt.lg.commons.toolkits.CollectionToolkit;
import com.wtyt.lgfesentryedamanagement.dao.bean.TBuryPointsNewEdaBranchPoints;
import com.wtyt.lgfesentryedamanagement.dao.mapper.NewEdaBranchPointsExtFieldsMapper;
import com.wtyt.lgfesentryedamanagement.dao.mapper.TBuryPointsNewEdaBranchMapper;
import com.wtyt.lgfesentryedamanagement.dao.mapper.TBuryPointsNewEdaBranchPointsMapper;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545037IBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545038IBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.NewEdaBranchAndPointsBean;
import com.wtyt.lgfesentryedamanagement.dao.bean.NewEdaBranchPointsExtFields;
import com.wtyt.lgfesentryedamanagement.dao.bean.TBuryPointsNewEdaBranch;
import com.wtyt.lgfesentryedamanagement.eda.bean.response.BurypointsNewEdaBranchAndPointsInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 *
 */
@Service
@Slf4j
public class BurypointsNewEdaBranchService {

    @Resource
    private TBuryPointsNewEdaBranchMapper tBuryPointsNewEdaBranchMapper;
    @Resource
    private TBuryPointsNewEdaBranchPointsMapper tBuryPointsNewEdaBranchPointsMapper;
    @Resource
    private NewEdaBranchPointsExtFieldsMapper newEdaBranchPointsExtFieldsMapper;

    /**
     * 批量维护eda分支(新增或更新) 5545037
     * @param data
     * @throws Exception
     */
    public void batchInsert(Req5545037IBean data)  throws Exception {
        String optLinkId = data.getOptLinkId();//操作链路id
        List<NewEdaBranchAndPointsBean> branchAndPointsList = data.getBranchList();
        if (StringUtils.isBlank(optLinkId)) {
            throw new UnifiedBusinessException("操作链路不能为空");
        }
        if (CollectionToolkit.isEmpty(branchAndPointsList)) {
            throw new UnifiedBusinessException("branchList不能为空");
        }
        for (NewEdaBranchAndPointsBean newEdaBranchAndPointsBean : branchAndPointsList) {
            if (CollectionToolkit.isEmpty(newEdaBranchAndPointsBean.getPointList())) {
                throw new UnifiedBusinessException("pointList不能为空");
            }
        }
      
        // 删除ele_ext_json对应T_BRANCH_POINTS_EXTFIELDS扩展表数据
        newEdaBranchPointsExtFieldsMapper.delByOptLinkId(optLinkId);
        // 删除关联的数据
        tBuryPointsNewEdaBranchPointsMapper.delByOptLinkId(optLinkId);
        // 先删除操作链路下的所有分支数据
        tBuryPointsNewEdaBranchMapper.delByOptLinkId(optLinkId);
        
        List<TBuryPointsNewEdaBranch> insertBranchList = new ArrayList<>();
        List<TBuryPointsNewEdaBranchPoints> insertBranchPointList = new ArrayList<>();
        List<NewEdaBranchPointsExtFields> insertBranchPointsExtFieldsList = new ArrayList<>();//新增扩展字段表数据

        for (NewEdaBranchAndPointsBean branchAndPoints : branchAndPointsList) {
            Long BurypointsEdaBranchId = UidToolkit.generateUidDefault();
            TBuryPointsNewEdaBranch branch = new TBuryPointsNewEdaBranch();

            branch.setBurypointsEdaBranchId(BurypointsEdaBranchId);
            branch.setOptLinkId(optLinkId);
            branch.setEdaBranchNo(branchAndPoints.getEdaBranchNo());
            branch.setEdaBranchName(branchAndPoints.getEdaBranchName());
            branch.setAppTag(branchAndPoints.getAppTag());
            branch.setProductEnv(branchAndPoints.getProductEnv());
            branch.setDeviceEnv(branchAndPoints.getDeviceEnv());
            branch.setBizType(branchAndPoints.getBizType());

            insertBranchList.add(branch);

            List<TBuryPointsNewEdaBranchPoints> pointList = branchAndPoints.getPointList();
            if (CollectionToolkit.isNotEmpty(pointList)) {
                for (TBuryPointsNewEdaBranchPoints point : pointList) {
                    point.setEdaBranchPointsId(UidToolkit.generateUidDefault());
                    point.setEdaBranchId(BurypointsEdaBranchId);
                    point.setEdaBranchNo(branch.getEdaBranchNo());

                    if (StringUtils.isNotBlank(point.getEleColumnJson())) {
                       Long eleColumnId = tBuryPointsNewEdaBranchPointsMapper.selectOneEleColumnId(branch.getOptLinkId(),point);
                       point.setBurypointsEleColumnId(eleColumnId);

                    } else {
                        point.setBurypointsEleColumnId(null);
                    }

                    if (StringUtils.isNotBlank(point.getEleExtJson())) {
                        // 解析扩展字段json的key value 值，插入到T_BRANCH_POINTS_EXTFIELDS表中  
                        JSONObject jsonObject = new JSONObject(point.getEleExtJson());
                        // 遍历所有键值对
                        String eleExtField1 = null;
                        String eleExtFieldVal1 = null;
                        String eleExtField2 = null;
                        String eleExtFieldVal2 = null;
                        for (String key : jsonObject.keySet()) {
                            Object value = jsonObject.get(key);
                            if(StringUtils.isBlank(eleExtField1)){
                                eleExtField1 = key;
                                eleExtFieldVal1 = value.toString(); 
                            } else if(StringUtils.isBlank(eleExtField2)){
                                eleExtField2 = key;
                                eleExtFieldVal2 = value.toString();
                            }
                        }
                        

                        if (StringUtils.isNotBlank(eleExtField1) && StringUtils.isNotBlank(eleExtFieldVal1)) {
                            NewEdaBranchPointsExtFields eleExtFields = new NewEdaBranchPointsExtFields();
                            Long eleExtFieldsId = UidToolkit.generateUidDefault();
                            eleExtFields.setBranchPointsExtfieldsId(eleExtFieldsId);
                            eleExtFields.setEleExtField1(eleExtField1);
                            eleExtFields.setEleExtFieldVal1(eleExtFieldVal1);

                            if(StringUtils.isNotBlank(eleExtFieldVal2) && StringUtils.isNotBlank(eleExtField2)){
                                eleExtFields.setEleExtField2(eleExtField2);
                                eleExtFields.setEleExtFieldVal2(eleExtFieldVal2);
                            }
                           
                            insertBranchPointsExtFieldsList.add(eleExtFields);

                            point.setBranchPointsExtfieldsId(eleExtFieldsId);
                        }
                        
                        
                    }

                    insertBranchPointList.add(point);
                }
            }
        }
        // 再批量新增
        if (CollectionToolkit.isNotEmpty(insertBranchList)) {
            tBuryPointsNewEdaBranchMapper.batchInsert(insertBranchList);
        }
        if (CollectionToolkit.isNotEmpty(insertBranchPointList)) {
            tBuryPointsNewEdaBranchPointsMapper.batchInsert(insertBranchPointList);
        }
        if (CollectionToolkit.isNotEmpty(insertBranchPointsExtFieldsList)) {
            newEdaBranchPointsExtFieldsMapper.batchInsert(insertBranchPointsExtFieldsList); 
        }
       
    }

    /**
     * 根据操作链路id查询分支及关联信息 5545038
     * @param optLinkId
     * @return
     */
    public List<BurypointsNewEdaBranchAndPointsInfo> selectBranchAndPointsInfo(Req5545038IBean bean) throws Exception{
        return tBuryPointsNewEdaBranchMapper.selectBranchAndPointsInfo(bean.getOptLinkId()); 
    }
}
