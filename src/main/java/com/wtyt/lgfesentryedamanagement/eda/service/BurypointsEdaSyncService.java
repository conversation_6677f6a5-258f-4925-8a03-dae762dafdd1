package com.wtyt.lgfesentryedamanagement.eda.service;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ctrip.framework.apollo.ConfigService;
import com.wtyt.gateway.sdk.toolkit.HttpClientToolkit;
import com.wtyt.generator.toolkit.UidToolkit;
import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import com.wtyt.lg.commons.toolkits.AlarmToolkit;
import com.wtyt.lg.commons.toolkits.CollectionToolkit;
import com.wtyt.lgfesentryedamanagement.dao.bean.*;
import com.wtyt.lgfesentryedamanagement.dao.mapper.*;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.*;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 *
 */
@Service
public class BurypointsEdaSyncService {

    private static final Logger log = LoggerFactory.getLogger(BurypointsEdaSyncService.class);

    @Resource
    private TBurypointsEdaMasterMapper tBurypointsEdaMasterMapper;
    @Resource
    private TBurypointsEdaMapper tBurypointsEdaMapper;
    @Resource
    private TBurypointsEdaRelMapper tBurypointsEdaRelMapper;
    @Resource
    private TBurypointsEdaSyncLogMapper tBurypointsEdaSyncLogMapper;
    @Resource
    private TBurypointsEdaBkConfigMapper tBurypointsEdaBkConfigMapper;

    @Resource
    private TBurypointsEdaFileMapper tBurypointsEdaFileMapper;

    @Resource(name = "commonThreadPool")
    private ThreadPoolExecutor commonThreadPool;

    /**
     * 同步EDA模块数据
     * @param data
     */
    public void syncMasterEda(Req5545011IBean data) throws Exception {
        //入参校验
        JSONObject envConfig = check5545011Param(data);
        //查询需要同步的数据
        List<TBurypointsEdaMaster> edaMasterList = tBurypointsEdaMasterMapper.queryByEdaNoList(data.getEdaNoList(), "0");
        if (CollectionToolkit.isEmpty(edaMasterList)) {
            throw new UnifiedBusinessException("未查询到数据");
        }
        JSONObject param = new JSONObject();
        JSONObject dataParam = new JSONObject();
        //清空创建时间数据
        edaMasterList.stream().forEach(item->item.setCreatedTime(null));
        dataParam.putOpt("edaMasterList", edaMasterList);
        param.putOpt("data", dataParam);
        //调用对应环境接口推送数据
        String resultStr = HttpClientToolkit.doPost(String.format("%s/sync/syncMasterEdaServer", envConfig.getStr(data.getEnv())), param.toString());
        JSONObject rsJson = JSONUtil.parseObj(resultStr);
        if (!StringUtils.equals("0", rsJson.getStr("reCode"))) {
            throw new UnifiedBusinessException("同步EDA模块异常：%s", rsJson.getStr("reInfo"));
        }
        //记录下同步的日志
        BuryPointToolkit.saveEdaSyncLog(BuryPointToolkit.transEdaMasterSyncLog(data, "1", data.getEnv(), edaMasterList));
    }

    /**
     * 5545011入参校验
     * @param data
     */
    private JSONObject check5545011Param(Req5545011IBean data) {
        if (CollectionToolkit.isEmpty(data.getEdaNoList())) {
            throw new UnifiedBusinessException("EDA模块EDA编号列表不能为空");
        }
        if (StringUtils.isEmpty(data.getEnv())) {
            throw new UnifiedBusinessException("目标环境不能为空");
        }
        return checkEnv(data.getEnv());
    }

    /**
     * 5545014入参校验
     * @param data
     */
    private JSONObject check5545014Param(Req5545014IParam data) {
        if (CollectionToolkit.isEmpty(data.getEdaNoList())) {
            throw new UnifiedBusinessException("EDA模块EDA编号列表不能为空");
        }
        if (StringUtils.isEmpty(data.getEnv())) {
            throw new UnifiedBusinessException("目标环境不能为空");
        }
        return checkEnv(data.getEnv());
    }

    /**
     * 5545018入参校验
     * @param data
     */
    private JSONObject check5545018Param(Req5545018IBean data) {
        if (CollectionToolkit.isEmpty(data.getEdaNoList())) {
            throw new UnifiedBusinessException("EDA模块EDA编号列表不能为空");
        }
        if (StringUtils.isEmpty(data.getEnv())) {
            throw new UnifiedBusinessException("目标环境不能为空");
        }
        return checkEnv(data.getEnv());
    }

    /**
     * 同步EDA模块数据删除
     * @param data
     */
    public void syncMasterEdaDel(Req5545018IBean data) throws Exception {
        //入参校验
        JSONObject envConfig = check5545018Param(data);
        JSONObject param = new JSONObject();
        JSONObject dataParam = new JSONObject();
        dataParam.putOpt("edaNoList", data.getEdaNoList());
        param.putOpt("data", dataParam);
        //调用对应环境接口推送数据
        String resultStr = HttpClientToolkit.doPost(String.format("%s/sync/syncMasterEdaDelServer", envConfig.getStr(data.getEnv())), param.toString());
        JSONObject rsJson = JSONUtil.parseObj(resultStr);
        if (!StringUtils.equals("0", rsJson.getStr("reCode"))) {
            throw new UnifiedBusinessException("同步EDA模块删除异常：%s", rsJson.getStr("reInfo"));
        }
        //记录下同步的日志
        //BuryPointToolkit.saveEdaSyncLog(BuryPointToolkit.transEdaMasterSyncLog(data, "1", data.getEnv(), edaMasterList));
    }

    /**
     * 同步EDA模块数据删除-服务端接口
     * @param data
     */
    public void syncMasterEdaDelServer(Req5545019IBean data) {
        if (CollectionToolkit.isEmpty(data.getEdaNoList())) {
            throw new UnifiedBusinessException("EDA_NO列表不能为空");
        }
        //查询已存在的数据
        tBurypointsEdaMasterMapper.removeByEdaNoList(data.getEdaNoList());
    }


    /**
     * 同步EDA分支与埋点元素关系数据
     * @param data
     */
    public void syncEdaFeature(Req5545012IBean data) throws Exception {
        //入参校验
        JSONObject envConfig = check5545012Param(data);
        //查询EDA埋点元素关系数据
        List<TBurypointsEdaRel> edaRelList = tBurypointsEdaRelMapper.queryByIdList(data.getBurypointsEdaRelIdList(), "0");
        if (CollectionToolkit.isEmpty(edaRelList)) {
            throw new UnifiedBusinessException("数据异常,请检查EDA埋点元素关系数据");
        }
        //查询缓存同步的主键关系
        List<TBurypointsEdaSyncLog> syncLog = tBurypointsEdaSyncLogMapper.queryByList("T_BURYPOINTS_EDA_REL", data.getEnv(), data.getBurypointsEdaRelIdList());
        //记录下同步的日志
        List<Map<String, String>> mainTableRows = BuryPointToolkit.transEdaFeatureSyncLog(data, "2", data.getEnv(), edaRelList);
        //需要新增的同步关系数据列表
        List<TBurypointsEdaSyncLog> newSyncList = new ArrayList<>();
        //根据主键关系，把主键替换成之前同步过的，如果没找到，则生成一条新的数据
        out : for (TBurypointsEdaRel tBurypointsEdaRel : edaRelList) {
            for (TBurypointsEdaSyncLog tBurypointsEdaSyncLog : syncLog) {
                if (tBurypointsEdaRel.getBurypointsEdaRelId().compareTo(tBurypointsEdaSyncLog.getSourceId()) == 0) {
                    tBurypointsEdaRel.setBurypointsEdaRelId(tBurypointsEdaSyncLog.getTargetId());
                    continue out;
                }
            }
            //生成新的关系数据
            TBurypointsEdaSyncLog newSync = new TBurypointsEdaSyncLog();
            newSync.setBurypointsEdaSyncLogId(UidToolkit.generateUidDefault());
            newSync.setSyncTableName("T_BURYPOINTS_EDA_REL");
            newSync.setEnv(data.getEnv());
            newSync.setSourceId(tBurypointsEdaRel.getBurypointsEdaRelId());
            newSync.setTargetId(UidToolkit.generateUidDefault());
            newSyncList.add(newSync);
            //设置新的主键
            tBurypointsEdaRel.setBurypointsEdaRelId(newSync.getTargetId());
        }
        //异步先把关系数据保存到数据库，就算后面调用接口失败，也不影响这个数据存下来
        saveSyncLog(newSyncList);
        //获取到EDA分支数据
        List<String> edaBranchNoList = edaRelList.stream().filter(e->StringUtils.isNotBlank(e.getEdaBranch())).map(TBurypointsEdaRel::getEdaBranch).distinct().collect(Collectors.toList());
        //查询EDA分支数据
        List<TBurypointsEda> edaBranchList = CollectionToolkit.isEmpty(edaBranchNoList) ? new ArrayList<>() : tBurypointsEdaMapper.queryByEdaNoList(edaBranchNoList, "0");
        //开始同步数据
        JSONObject param = new JSONObject();
        JSONObject dataParam = new JSONObject();
        edaBranchList.stream().forEach(item->item.setCreatedTime(null));
        dataParam.putOpt("edaBranchList", edaBranchList);
        edaRelList.stream().forEach(item->item.setCreatedTime(null));
        dataParam.putOpt("edaRelList", edaRelList);
        param.putOpt("data", dataParam);
        //调用对应环境接口推送数据
        String resultStr = HttpClientToolkit.doPost(String.format("%s/sync/syncEdaFeatureServer", envConfig.getStr(data.getEnv())), param.toString());
        JSONObject rsJson = JSONUtil.parseObj(resultStr);
        if (!StringUtils.equals("0", rsJson.getStr("reCode"))) {
            throw new UnifiedBusinessException("同步EDA分支与埋点元素关系数据异常：%s", rsJson.getStr("reInfo"));
        }
        //记录下同步的日志
        BuryPointToolkit.saveEdaSyncLog(mainTableRows);
        BuryPointToolkit.saveEdaSyncLog(BuryPointToolkit.transEdaBranchSyncLog(data, "3", data.getEnv(), edaBranchList));
    }

    /**
     * 5545012入参校验
     * @param data
     */
    private JSONObject check5545012Param(Req5545012IBean data) {
        if (CollectionToolkit.isEmpty(data.getBurypointsEdaRelIdList())) {
            throw new UnifiedBusinessException("全埋点元素与EDA关系ID列表不能为空");
        }
        if (StringUtils.isEmpty(data.getEnv())) {
            throw new UnifiedBusinessException("目标环境不能为空");
        }
        return checkEnv(data.getEnv());
    }


    /**
     * 同步EDA后端埋点的元数据
     * @param data
     */
    public void syncEdaBkConfig(Req5545013IBean data) throws Exception {
        //入参校验
        JSONObject envConfig = check5545013Param(data);
        //查询EDA后端埋点的元数据
        List<TBurypointsEdaBkConfig> edaBkConfigList = tBurypointsEdaBkConfigMapper.queryByIdList(data.getBurypointsEdaBkConfigIdList(), "0");
        if (CollectionToolkit.isEmpty(edaBkConfigList)) {
            throw new UnifiedBusinessException("数据异常,请检查EDA后端埋点的元数据");
        }
        //记录下同步的日志
        List<Map<String, String>> mainTableRows = BuryPointToolkit.transEdaBkConfigSyncLog(data, "4", data.getEnv(), edaBkConfigList);
        //查询缓存同步的主键关系
        List<TBurypointsEdaSyncLog> syncLog = tBurypointsEdaSyncLogMapper.queryByList("T_BURYPOINTS_EDA_BK_CONFIG", data.getEnv(), data.getBurypointsEdaBkConfigIdList());
        //需要新增的同步关系数据列表
        List<TBurypointsEdaSyncLog> newSyncList = new ArrayList<>();
        //根据主键关系，把主键替换成之前同步过的，如果没找到，则生成一条新的数据
        out : for (TBurypointsEdaBkConfig tBurypointsEdaBkConfig : edaBkConfigList) {
            for (TBurypointsEdaSyncLog tBurypointsEdaSyncLog : syncLog) {
                if (tBurypointsEdaBkConfig.getBurypointsEdaBkConfigId().compareTo(tBurypointsEdaSyncLog.getSourceId()) == 0) {
                    tBurypointsEdaBkConfig.setBurypointsEdaBkConfigId(tBurypointsEdaSyncLog.getTargetId());
                    continue out;
                }
            }
            //生成新的关系数据
            TBurypointsEdaSyncLog newSync = new TBurypointsEdaSyncLog();
            newSync.setBurypointsEdaSyncLogId(UidToolkit.generateUidDefault());
            newSync.setSyncTableName("T_BURYPOINTS_EDA_BK_CONFIG");
            newSync.setEnv(data.getEnv());
            newSync.setSourceId(tBurypointsEdaBkConfig.getBurypointsEdaBkConfigId());
            newSync.setTargetId(UidToolkit.generateUidDefault());
            newSyncList.add(newSync);
            //设置新的主键
            tBurypointsEdaBkConfig.setBurypointsEdaBkConfigId(newSync.getTargetId());
        }
        //异步先把关系数据保存到数据库，就算后面调用接口失败，也不影响这个数据存下来
        saveSyncLog(newSyncList);
        //开始同步数据
        JSONObject param = new JSONObject();
        JSONObject dataParam = new JSONObject();
        edaBkConfigList.stream().forEach(item->item.setCreatedTime(null));
        dataParam.putOpt("edaBkConfigList", edaBkConfigList);
        param.putOpt("data", dataParam);
        //调用对应环境接口推送数据
        String resultStr = HttpClientToolkit.doPost(String.format("%s/sync/syncEdaBkConfigServer", envConfig.getStr(data.getEnv())), param.toString());
        JSONObject rsJson = JSONUtil.parseObj(resultStr);
        if (!StringUtils.equals("0", rsJson.getStr("reCode"))) {
            throw new UnifiedBusinessException("同步EDA后端埋点的元数据异常：%s", rsJson.getStr("reInfo"));
        }
        //记录下同步的日志
        BuryPointToolkit.saveEdaSyncLog(mainTableRows);
    }

    /**
     * 5545012入参校验
     * @param data
     */
    private JSONObject check5545013Param(Req5545013IBean data) {
        if (CollectionToolkit.isEmpty(data.getBurypointsEdaBkConfigIdList())) {
            throw new UnifiedBusinessException("EDA后端埋点的元数据ID列表不能为空");
        }
        if (StringUtils.isEmpty(data.getEnv())) {
            throw new UnifiedBusinessException("目标环境不能为空");
        }
        return checkEnv(data.getEnv());
    }

    /**
     * 查询并验证env
     * @param env
     */
    private JSONObject checkEnv(String env) {
        String envConfigStr = ConfigService.getAppConfig().getProperty("eda.sync.env.config", "");
        if (StringUtils.isBlank(envConfigStr)) {
            throw new UnifiedBusinessException("检查eda.sync.env.config是否配置");
        }
        JSONObject envConfig = JSONUtil.parseObj(envConfigStr);
        if (!envConfig.containsKey(env)) {
            throw new UnifiedBusinessException("目标环境不合法");
        }
        return envConfig;
    }


    /**
     * 异步保存同步的主键关系
     * @param syncList
     */
    private void saveSyncLog(List<TBurypointsEdaSyncLog> syncList) {
        if (CollectionToolkit.isEmpty(syncList)) {
            return;
        }
        commonThreadPool.execute(() -> {
            try {
                SpringContextToolkit.getBean(TBurypointsEdaSyncLogMapper.class).saveSyncLog(syncList);
            } catch (Exception e) {
                AlarmToolkit.logAlarm("保存T_BURYPOINTS_EDA_SYNC_LOG出现异常", e, "保存T_BURYPOINTS_EDA_SYNC_LOG出现异常");
            }
        });
    }

    /**
     * 从宜搭同步数据到测试环境
     * @param transEdaMasterBean
     */
    public void syncEdaMasterFromYiDa(TransEdaMasterBean transEdaMasterBean) throws Exception {
        if (StringUtils.isBlank(transEdaMasterBean.getEdaNo())) {
            throw new UnifiedBusinessException("EDA编号不可为空");
        }
        //需要查询EDA编号是否存在
        List<TBurypointsEdaMaster> edaMasterList = tBurypointsEdaMasterMapper.queryByEdaNoList(Arrays.asList(transEdaMasterBean.getEdaNo()), null);
        //根据操作类型处理业务
        switch(transEdaMasterBean.getOptType()) {
            case "3":
                if (CollectionToolkit.isNotEmpty(edaMasterList)) {
                    tBurypointsEdaMasterMapper.removeByEdaNoList(Arrays.asList(transEdaMasterBean.getEdaNo()));
                    //等事务提交之后同步到其他环境
                    TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                        @Override
                        public void afterCommit() {
                            try {
                                Req5545018IBean envSync = new Req5545018IBean();
                                envSync.setEdaNoList(Arrays.asList(transEdaMasterBean.getEdaNo()));
                                envSync.setOptTeam("宜搭");
                                envSync.setOptUserName("宜搭数据同步");
                                envSync.setEnv("UAT");
                                syncMasterEdaDel(envSync);
                                envSync.setEnv("PRO");
                                syncMasterEdaDel(envSync);
                            } catch (Exception e) {
                                AlarmToolkit.logAlarm("宜搭同步数据，从测试环境同步到其他环境出现异常", e, "宜搭同步数据，从测试环境同步到其他环境出现异常");
                            }
                        }
                    });
                }
                break;
            case "1":
            case "2":
                //处理EDA名称缩写   功能点缩写后额外追加01
                transEdaMasterBean.setEdaAbbreveName(PinyinToolkit.converterToFirstSpellUpperCase(transEdaMasterBean.getEdaName()) + (1 == transEdaMasterBean.getEdaType() ? "01" : ""));
                //如果是新增和修改，说明是未删除的数据
                transEdaMasterBean.setIsDel(0);
                if (CollectionToolkit.isNotEmpty(edaMasterList)) {
                    //存在则更新
                    transEdaMasterBean.setBurypointsEdaMasterId(edaMasterList.get(0).getBurypointsEdaMasterId());
                    transEdaMasterBean.setLastModifiedTime(LocalDateTime.now());
                    tBurypointsEdaMasterMapper.updateByPrimaryKeySelective(transEdaMasterBean);
                } else {
                    //不存在则新增
                    transEdaMasterBean.setBurypointsEdaMasterId(UidToolkit.generateUidDefault());
                    //浮动比例默认5%
                    transEdaMasterBean.setFloatingRatio(new BigDecimal("5"));
                    tBurypointsEdaMasterMapper.insertSelective(transEdaMasterBean);
                }
                //等事务提交之后同步到其他环境
                //这块改成新的同步后没有测试，暂时不开放
/*                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                    @Override
                    public void afterCommit() {
                        try {
                            Req5545014IParam envSync = new Req5545014IParam();
                            envSync.setEdaNoList(Arrays.asList(transEdaMasterBean.getEdaNo()));
                            envSync.setOptTeam("宜搭");
                            envSync.setOptUserName("宜搭数据同步");
                            envSync.setEnv("UAT");
                            transMasterEda(envSync);
                            envSync.setEnv("PRO");
                            transMasterEda(envSync);
                        } catch (Exception e) {
                            AlarmToolkit.logAlarm("宜搭同步数据，从测试环境同步到其他环境出现异常", e, "宜搭同步数据，从测试环境同步到其他环境出现异常");
                        }
                    }
                });*/
                break;
            default:
                throw new UnifiedBusinessException("操作类型不合法");

        }
    }

    /**
     * 同步EDA模块(操作链路)
     * @param data
     */
    public void transMasterEda(Req5545014IParam data) throws Exception {
        //入参校验
        JSONObject envConfig = check5545014Param(data);
        //查询需要同步的数据（is_del=1的也会被查出来）
        List<TBurypointsEdaMaster> edaMasterList = tBurypointsEdaMasterMapper.queryByEdaNoList(data.getEdaNoList(), null);
        if (CollectionToolkit.isEmpty(edaMasterList)) {
            throw new UnifiedBusinessException("未查询到数据");
        }
        JSONObject param = new JSONObject();
        JSONObject dataParam = new JSONObject();
        //清空创建时间数据
        edaMasterList.stream().forEach(item->item.setCreatedTime(null));
        dataParam.putOpt("edaMasterList", edaMasterList);
        //根据选项，决定是否同步分支(埋点元素关系)和覆盖率配置
        if (StringUtils.equals("1", data.getSyncEdaBranch())) {
            //查询edaNo关联到的所有分支数据
            List<TBurypointsEda> edaBranchList = tBurypointsEdaMapper.queryByEdaParentNo(data.getEdaNoList().get(0), null);
            List<List<TBurypointsEda>> edaBranchSplitList = SqlParamToolkit.splitList(edaBranchList, 999);
            //查询EDA埋点元素关系数据
            List<TBurypointsEdaRel> edaRelList = new ArrayList<>();
            for (List<TBurypointsEda> tBurypointsEdas : edaBranchSplitList) {
                edaRelList.addAll(tBurypointsEdaRelMapper.queryByEdaAndBranchList(data.getEdaNoList().get(0), tBurypointsEdas, null));
            }
            if (CollectionToolkit.isNotEmpty(edaBranchList)) {
                edaBranchList.stream().forEach(item->item.setCreatedTime(null));
                dataParam.putOpt("edaBranchList", edaBranchList);
            }
            if (CollectionToolkit.isNotEmpty(edaRelList)) {
                edaRelList.stream().forEach(item->item.setCreatedTime(null));
                dataParam.putOpt("edaRelList", edaRelList);
            }
        }
        if (StringUtils.equals("1", data.getSyncEdaBkConfig())) {
            //查询EDA后端埋点的元数据
            List<TBurypointsEdaBkConfig> edaBkConfigList = tBurypointsEdaBkConfigMapper.queryByEdaNo(data.getEdaNoList().get(0), null);
            if (CollectionToolkit.isNotEmpty(edaBkConfigList)) {
                edaBkConfigList.stream().forEach(item->item.setCreatedTime(null));
                dataParam.putOpt("edaBkConfigList", edaBkConfigList);
            }
        }

        param.putOpt("data", dataParam);
        //调用对应环境接口推送数据
        String resultStr = HttpClientToolkit.doPost(String.format("%s/sync/transMasterEdaServer", envConfig.getStr(data.getEnv())), param.toString());
        JSONObject rsJson = JSONUtil.parseObj(resultStr);
        if (!StringUtils.equals("0", rsJson.getStr("reCode"))) {
            throw new UnifiedBusinessException("同步EDA模块异常：%s", rsJson.getStr("reInfo"));
        }
        //记录下同步的日志
        BuryPointToolkit.saveEdaSyncLog(BuryPointToolkit.transEdaMasterSyncLog(data, "1", data.getEnv(), edaMasterList));
    }


    /**
     * 同步EDA模块数据-服务端接口
     * @param data
     */
    public void transMasterEdaServer(Req5545014IBean data) throws InvocationTargetException, IllegalAccessException {
        if (CollectionToolkit.isEmpty(data.getEdaMasterList())) {
            throw new UnifiedBusinessException("数据列表不能为空");
        }
        //#################处理eda模块数据###################
        //查询已存在的数据
        List<TBurypointsEdaMaster> localList = tBurypointsEdaMasterMapper.queryByEdaNoList(data.getEdaMasterList().stream().map(TBurypointsEdaMaster::getEdaNo).collect(Collectors.toList()), null);
        //组装更新数据与插入数据
        for (TBurypointsEdaMaster tBurypointsEdaMaster : data.getEdaMasterList()) {
            tBurypointsEdaMaster.setBurypointsEdaMasterId(null);
            for (TBurypointsEdaMaster burypointsEdaMaster : localList) {
                //如果本地有数据，则只需要更新，主键不用重新生成
                if (StringUtils.equals(tBurypointsEdaMaster.getEdaNo(), burypointsEdaMaster.getEdaNo())) {
                    //使用已存在的主键
                    tBurypointsEdaMaster.setBurypointsEdaMasterId(burypointsEdaMaster.getBurypointsEdaMasterId());
                    JavaBeanTookit.copyTargetNullProperties(burypointsEdaMaster, tBurypointsEdaMaster);
                    //需要更新最后修改时间
                    tBurypointsEdaMaster.setLastModifiedTime(LocalDateTime.now());
                    tBurypointsEdaMasterMapper.updateByPrimaryKeySelective(tBurypointsEdaMaster);
                    break;
                }
            }
            //如果没有主见，则是新增数据，需要补充必要字段
            if (null == tBurypointsEdaMaster.getBurypointsEdaMasterId()) {
                tBurypointsEdaMaster.setBurypointsEdaMasterId(UidToolkit.generateUidDefault());
                tBurypointsEdaMasterMapper.deleteByEdaNo(tBurypointsEdaMaster.getEdaNo());
                tBurypointsEdaMasterMapper.insertSelective(tBurypointsEdaMaster);
            }
        }
        //#################处理eda分支与埋点关系###################
        if (CollectionToolkit.isNotEmpty(data.getEdaRelList())) {
            Req5545015IBean req5545015IBean = new Req5545015IBean();
            req5545015IBean.setEdaRelList(data.getEdaRelList());
            req5545015IBean.setEdaBranchList(data.getEdaBranchList());
            transEdaBranchServer(req5545015IBean);
        }
        //#################处理eda覆盖率配置数据###################
        if (CollectionToolkit.isNotEmpty(data.getEdaBkConfigList())) {
            Req5545016IBean req5545016IBean = new Req5545016IBean();
            req5545016IBean.setEdaBkConfigList(data.getEdaBkConfigList());
            transEdaBkConfigServer(req5545016IBean);
        }

    }

    /**
     * 同步EDA分支与埋点元素关系数据
     * @param data
     */
    public void transEdaBranch(Req5545012IBean data) throws Exception {
        //入参校验
        JSONObject envConfig = check5545012Param(data);
        //查询EDA埋点元素关系数据
        List<TBurypointsEdaRel> edaRelList = tBurypointsEdaRelMapper.queryByIdList(data.getBurypointsEdaRelIdList(), null);
        if (CollectionToolkit.isEmpty(edaRelList)) {
            throw new UnifiedBusinessException("数据异常,请检查EDA埋点元素关系数据");
        }
        //记录下同步的日志
        List<Map<String, String>> mainTableRows = BuryPointToolkit.transEdaFeatureSyncLog(data, "2", data.getEnv(), edaRelList);
        //获取到EDA分支数据
        List<String> edaBranchNoList = edaRelList.stream().filter(e->StringUtils.isNotBlank(e.getEdaBranch())).map(TBurypointsEdaRel::getEdaBranch).distinct().collect(Collectors.toList());
        //查询EDA分支数据
        List<TBurypointsEda> edaBranchList = CollectionToolkit.isEmpty(edaBranchNoList) ? new ArrayList<>() : tBurypointsEdaMapper.queryByEdaNoList(edaBranchNoList, null);
        //开始同步数据
        JSONObject param = new JSONObject();
        JSONObject dataParam = new JSONObject();
        edaBranchList.stream().forEach(item->item.setCreatedTime(null));
        dataParam.putOpt("edaBranchList", edaBranchList);
        edaRelList.stream().forEach(item->item.setCreatedTime(null));
        dataParam.putOpt("edaRelList", edaRelList);
        param.putOpt("data", dataParam);
        //调用对应环境接口推送数据
        String resultStr = HttpClientToolkit.doPost(String.format("%s/sync/transEdaBranchServer", envConfig.getStr(data.getEnv())), param.toString());
        JSONObject rsJson = JSONUtil.parseObj(resultStr);
        if (!StringUtils.equals("0", rsJson.getStr("reCode"))) {
            throw new UnifiedBusinessException("同步EDA分支与埋点元素关系数据异常：%s", rsJson.getStr("reInfo"));
        }
        //记录下同步的日志
        BuryPointToolkit.saveEdaSyncLog(mainTableRows);
        BuryPointToolkit.saveEdaSyncLog(BuryPointToolkit.transEdaBranchSyncLog(data, "3", data.getEnv(), edaBranchList));
    }


    /**
     * 同步EDA分支与埋点元素关系数据-服务端接口
     * @param data
     */
    public void transEdaBranchServer(Req5545015IBean data) {
        if (CollectionToolkit.isEmpty(data.getEdaRelList())) {
            throw new UnifiedBusinessException("关系数据列表不能为空");
        }
        //获取到EDA模块数据
        List<String> edaNoList = data.getEdaRelList().stream().map(TBurypointsEdaRel::getEleEdaNo).distinct().collect(Collectors.toList());
        //获取到EDA分支数据
        List<String> edaBranchNoList = data.getEdaRelList().stream().filter(e->StringUtils.isNotBlank(e.getEdaBranch())).map(TBurypointsEdaRel::getEdaBranch).distinct().collect(Collectors.toList());
        //验证EDA数据是否都存在
        List<TBurypointsEdaMaster> edaList = tBurypointsEdaMasterMapper.queryByEdaNoList(edaNoList, "0");
        //定义一个未同步的EDA模块集合
        List<String> noSyncList = new ArrayList<>();
        out : for (String s : edaNoList) {
            for (TBurypointsEdaMaster tBurypointsEdaMaster : edaList) {
                if (StringUtils.equals(s, tBurypointsEdaMaster.getEdaNo())) {
                    continue out;
                }
            }
            noSyncList.add(s);
        }
        if (CollectionToolkit.isNotEmpty(noSyncList)) {
            throw new UnifiedBusinessException(String.format("EDA模块不存在,请先同步[%s]", noSyncList.toString()));
        }
        //####################这里处理eda_rel表数据##############################
        if (CollectionToolkit.isNotEmpty(data.getEdaRelList())) {
            //edaRelList查询关系数据，存在关系的更新，不存在关系的新增
            //查询主键是否存在，存在的则把创建时间赋值，不要修改创建时间
            List<Long> edaRelIdList = data.getEdaRelList().stream().map(TBurypointsEdaRel::getBurypointsEdaRelId).distinct().collect(Collectors.toList());
            //查询缓存同步的主键关系
            List<TBurypointsEdaSyncLog> syncLog = tBurypointsEdaSyncLogMapper.queryByList("T_BURYPOINTS_EDA_REL", "FAT", edaRelIdList);
            //需要新增的同步关系数据列表
            List<TBurypointsEdaSyncLog> newSyncList = new ArrayList<>();
            //已有主键的列表
            List<Long> localEdaRelIdList = new ArrayList<>();
            //根据主键关系，把主键替换成之前同步过的，如果没找到，则生成一条新的数据
            out : for (TBurypointsEdaRel tBurypointsEdaRel : data.getEdaRelList()) {
                for (TBurypointsEdaSyncLog tBurypointsEdaSyncLog : syncLog) {
                    if (tBurypointsEdaRel.getBurypointsEdaRelId().compareTo(tBurypointsEdaSyncLog.getSourceId()) == 0) {
                        tBurypointsEdaRel.setBurypointsEdaRelId(tBurypointsEdaSyncLog.getTargetId());
                        localEdaRelIdList.add(tBurypointsEdaSyncLog.getTargetId());
                        continue out;
                    }
                }
                //生成新的关系数据
                TBurypointsEdaSyncLog newSync = new TBurypointsEdaSyncLog();
                newSync.setBurypointsEdaSyncLogId(UidToolkit.generateUidDefault());
                newSync.setSyncTableName("T_BURYPOINTS_EDA_REL");
                newSync.setEnv("FAT");//把对应关系保存在各个环境之后，这个参数就没什么意义了，所以写死FAT
                newSync.setSourceId(tBurypointsEdaRel.getBurypointsEdaRelId());
                newSync.setTargetId(UidToolkit.generateUidDefault());
                newSyncList.add(newSync);
                //设置新的主键
                tBurypointsEdaRel.setBurypointsEdaRelId(newSync.getTargetId());
            }
            //比对本地数据，如果出现关系存在，但主键不存在，去补上数据就行了(防止有人修改了记录的主键,或者物理删除的数据)
            List<TBurypointsEdaRel> edaRelList = CollectionToolkit.isEmpty(localEdaRelIdList) ? new ArrayList<>() : tBurypointsEdaRelMapper.queryByIdList(localEdaRelIdList, null);
            boolean isExists;
            for (TBurypointsEdaRel burypointsEdaRel : data.getEdaRelList()) {
                //默认本地库不存在
                isExists = false;
                for (TBurypointsEdaRel tBurypointsEdaRel : edaRelList) {
                    if (tBurypointsEdaRel.getBurypointsEdaRelId().compareTo(burypointsEdaRel.getBurypointsEdaRelId()) == 0) {
                        //本地库找到了，则设置为true
                        isExists = true;
                        break;
                    }
                }
                if (isExists) {
                    tBurypointsEdaRelMapper.updateByPrimaryKey(burypointsEdaRel);
                } else {
                    tBurypointsEdaRelMapper.insert(burypointsEdaRel);
                }
            }
            //异步先把关系数据保存到数据库，就算后面调用接口失败，也不影响这个数据存下来
            saveSyncLog(newSyncList);
        }
        //#########################下面处理eda表数据#############################
        //查询已存在的数据
        List<TBurypointsEda> localList = tBurypointsEdaMapper.queryByEdaNoList(edaBranchNoList, null);
        //组装更新数据与插入数据
        for (TBurypointsEda tBurypointsEda : data.getEdaBranchList()) {
            tBurypointsEda.setBurypointsEdaId(null);
            for (TBurypointsEda burypointsEda : localList) {
                //如果本地有数据，则只需要更新，主键不用重新生成
                if (StringUtils.equals(tBurypointsEda.getEdaNo(), burypointsEda.getEdaNo())) {
                    //使用本地数据主键，不新增
                    tBurypointsEda.setBurypointsEdaId(burypointsEda.getBurypointsEdaId());
                    tBurypointsEda.setLastModifiedTime(LocalDateTime.now());
                    tBurypointsEda.setSyncTime(LocalDateTime.now());
                    tBurypointsEdaMapper.updateByPrimaryKeySelective(tBurypointsEda);
                    break;
                }
            }
            if (null == tBurypointsEda.getBurypointsEdaId()) {
                tBurypointsEdaMapper.deleteByEdaNo(tBurypointsEda.getEdaNo());
                //没有主键，则是需要新增的数据
                tBurypointsEda.setBurypointsEdaId(UidToolkit.generateUidDefault());
                tBurypointsEda.setSyncTime(LocalDateTime.now());
                tBurypointsEdaMapper.insertSelective(tBurypointsEda);
            }
        }
    }

    public void transEdaBkConfig(Req5545013IBean data) throws Exception {
        //入参校验
        JSONObject envConfig = check5545013Param(data);
        //查询EDA后端埋点的元数据
        List<TBurypointsEdaBkConfig> edaBkConfigList = tBurypointsEdaBkConfigMapper.queryByIdList(data.getBurypointsEdaBkConfigIdList(), null);
        if (CollectionToolkit.isEmpty(edaBkConfigList)) {
            throw new UnifiedBusinessException("数据异常,请检查选择同步的EDA覆盖率数据是否存在");
        }
        //记录下同步的日志
        List<Map<String, String>> mainTableRows = BuryPointToolkit.transEdaBkConfigSyncLog(data, "4", data.getEnv(), edaBkConfigList);
        //开始同步数据
        JSONObject param = new JSONObject();
        JSONObject dataParam = new JSONObject();
        edaBkConfigList.stream().forEach(item->item.setCreatedTime(null));
        dataParam.putOpt("edaBkConfigList", edaBkConfigList);
        param.putOpt("data", dataParam);
        //调用对应环境接口推送数据
        String resultStr = HttpClientToolkit.doPost(String.format("%s/sync/transEdaBkConfigServer", envConfig.getStr(data.getEnv())), param.toString());
        JSONObject rsJson = JSONUtil.parseObj(resultStr);
        if (!StringUtils.equals("0", rsJson.getStr("reCode"))) {
            throw new UnifiedBusinessException("同步EDA后端埋点的元数据异常：%s", rsJson.getStr("reInfo"));
        }
        //记录下同步的日志
        BuryPointToolkit.saveEdaSyncLog(mainTableRows);
    }

    /**
     * 同步EDA后端埋点的元数据-服务端接口
     * @param data
     */
    public void transEdaBkConfigServer(Req5545016IBean data) {
        if (CollectionToolkit.isEmpty(data.getEdaBkConfigList())) {
            throw new UnifiedBusinessException("关系数据列表不能为空");
        }
        //按前后端覆盖率进行分组 分别处理
        List<TBurypointsEdaBkConfig> bkList = new ArrayList<>();
        List<TBurypointsEdaBkConfig> ftList = new ArrayList<>();
        data.getEdaBkConfigList().forEach(item->{
            if (StringUtils.equals("1", item.getConfigType())) {
                bkList.add(item);
            } else if (StringUtils.equals("2", item.getConfigType())) {
                ftList.add(item);
            }
        });
        if (CollectionToolkit.isNotEmpty(bkList)) {
            transBkConfigData(bkList);
        }
        if (CollectionToolkit.isNotEmpty(ftList)) {
            transFtConfigData(ftList);
        }
    }

    /**
     * 服务端覆盖率数据同步
     * @param configList
     */
    private void transBkConfigData(List<TBurypointsEdaBkConfig> configList) {
        //获取主键列表
        List<Long> edaBkConfigIdList = configList.stream().map(TBurypointsEdaBkConfig::getBurypointsEdaBkConfigId).distinct().collect(Collectors.toList());
        //查询缓存同步的主键关系
        List<TBurypointsEdaSyncLog> syncLog = tBurypointsEdaSyncLogMapper.queryByList("T_BURYPOINTS_EDA_BK_CONFIG", "FAT", edaBkConfigIdList);
        //需要新增的同步关系数据列表
        List<TBurypointsEdaSyncLog> newSyncList = new ArrayList<>();
        //已有主键的列表
        List<Long> localEdaRelIdList = new ArrayList<>();
        //根据主键关系，把主键替换成之前同步过的，如果没找到，则生成一条新的数据
        out : for (TBurypointsEdaBkConfig tBurypointsEdaBkConfig : configList) {
            for (TBurypointsEdaSyncLog tBurypointsEdaSyncLog : syncLog) {
                if (tBurypointsEdaBkConfig.getBurypointsEdaBkConfigId().compareTo(tBurypointsEdaSyncLog.getSourceId()) == 0) {
                    tBurypointsEdaBkConfig.setBurypointsEdaBkConfigId(tBurypointsEdaSyncLog.getTargetId());
                    localEdaRelIdList.add(tBurypointsEdaSyncLog.getTargetId());
                    continue out;
                }
            }
            //生成新的关系数据
            TBurypointsEdaSyncLog newSync = new TBurypointsEdaSyncLog();
            newSync.setBurypointsEdaSyncLogId(UidToolkit.generateUidDefault());
            newSync.setSyncTableName("T_BURYPOINTS_EDA_BK_CONFIG");
            newSync.setEnv("FAT");
            newSync.setSourceId(tBurypointsEdaBkConfig.getBurypointsEdaBkConfigId());
            newSync.setTargetId(UidToolkit.generateUidDefault());
            newSyncList.add(newSync);
            //设置新的主键
            tBurypointsEdaBkConfig.setBurypointsEdaBkConfigId(newSync.getTargetId());
        }
        //异步先把关系数据保存到数据库，就算后面调用接口失败，也不影响这个数据存下来
        saveSyncLog(newSyncList);
        //查询到数据则更新，查不到就新增
        List<TBurypointsEdaBkConfig> edaBkConfigList = CollectionToolkit.isEmpty(localEdaRelIdList) ? new ArrayList<>() : tBurypointsEdaBkConfigMapper.queryByIdList(localEdaRelIdList, null);
        boolean isExists;
        for (TBurypointsEdaBkConfig burypointsEdaBkConfig : configList) {
            //默认本地库不存在
            isExists = false;
            for (TBurypointsEdaBkConfig tBurypointsEdaBkConfig : edaBkConfigList) {
                if (tBurypointsEdaBkConfig.getBurypointsEdaBkConfigId().compareTo(burypointsEdaBkConfig.getBurypointsEdaBkConfigId()) == 0) {
                    //本地库找到了，则设置为true
                    isExists = true;
                    break;
                }
            }
            if (isExists) {
                tBurypointsEdaBkConfigMapper.updateByPrimaryKeySelective(burypointsEdaBkConfig);
            } else {
                tBurypointsEdaBkConfigMapper.insert(burypointsEdaBkConfig);
            }
        }
    }


    /**
     *前端覆盖率数据同步
     * @param configList
     */
    private void transFtConfigData(List<TBurypointsEdaBkConfig> configList) {
        //获取主键列表
        List<Long> edaBkConfigIdList = configList.stream().map(TBurypointsEdaBkConfig::getBurypointsEdaBkConfigId).distinct().collect(Collectors.toList());
        //查询缓存同步的主键关系
        List<TBurypointsEdaSyncLog> syncLog = tBurypointsEdaSyncLogMapper.queryByList("T_BURYPOINTS_EDA_FT_CONFIG", "FAT", edaBkConfigIdList);
        //需要新增的同步关系数据列表
        List<TBurypointsEdaSyncLog> newSyncList = new ArrayList<>();
        //已有主键的列表
        List<Long> localEdaRelIdList = new ArrayList<>();
        //根据主键关系，把主键替换成之前同步过的，如果没找到，则生成一条新的数据
        out : for (TBurypointsEdaBkConfig tBurypointsEdaBkConfig : configList) {
            for (TBurypointsEdaSyncLog tBurypointsEdaSyncLog : syncLog) {
                if (tBurypointsEdaBkConfig.getBurypointsEdaBkConfigId().compareTo(tBurypointsEdaSyncLog.getSourceId()) == 0) {
                    tBurypointsEdaBkConfig.setBurypointsEdaBkConfigId(tBurypointsEdaSyncLog.getTargetId());
                    localEdaRelIdList.add(tBurypointsEdaSyncLog.getTargetId());
                    continue out;
                }
            }
            //生成新的关系数据
            TBurypointsEdaSyncLog newSync = new TBurypointsEdaSyncLog();
            newSync.setBurypointsEdaSyncLogId(UidToolkit.generateUidDefault());
            newSync.setSyncTableName("T_BURYPOINTS_EDA_FT_CONFIG");
            newSync.setEnv("FAT");
            newSync.setSourceId(tBurypointsEdaBkConfig.getBurypointsEdaBkConfigId());
            newSync.setTargetId(UidToolkit.generateUidDefault());
            newSyncList.add(newSync);
            //设置新的主键
            tBurypointsEdaBkConfig.setBurypointsEdaBkConfigId(newSync.getTargetId());
        }
        //异步先把关系数据保存到数据库，就算后面调用接口失败，也不影响这个数据存下来
        saveSyncLog(newSyncList);
        //查询到数据则更新，查不到就新增
        List<TBurypointsEdaBkConfig> edaBkConfigList = CollectionToolkit.isEmpty(localEdaRelIdList) ? new ArrayList<>() : tBurypointsEdaBkConfigMapper.queryByIdList(localEdaRelIdList, null);
        boolean isExists;
        for (TBurypointsEdaBkConfig burypointsEdaBkConfig : configList) {
            //默认本地库不存在
            isExists = false;
            for (TBurypointsEdaBkConfig tBurypointsEdaBkConfig : edaBkConfigList) {
                if (tBurypointsEdaBkConfig.getBurypointsEdaBkConfigId().compareTo(burypointsEdaBkConfig.getBurypointsEdaBkConfigId()) == 0) {
                    //本地库找到了，则设置为true
                    isExists = true;
                    break;
                }
            }
            if (isExists) {
                tBurypointsEdaBkConfigMapper.updateByPrimaryKeySelectiveFt(burypointsEdaBkConfig);
            } else {
                tBurypointsEdaBkConfigMapper.insertFt(burypointsEdaBkConfig);
            }
        }
    }





    /**
     * 从雄鹰同步操作链路
     * @param bean
     */
    public String syncEagleSystemToEda(Req5545039IBean bean){
        TBurypointsEdaMaster transEdaMasterBean = new TBurypointsEdaMaster();
        transEdaMasterBean.setEdaType(0);
        if(StringUtils.isEmpty(bean.getOptLinkId())){
            throw new UnifiedBusinessException("操作链路id不能为空");
        }
        transEdaMasterBean.setOptLinkId(bean.getOptLinkId());
        if(StringUtils.isEmpty(bean.getOptLinkName())){
            throw new UnifiedBusinessException("操作链路名称不能为空");
        }
        transEdaMasterBean.setEdaName(bean.getOptLinkName());
        if(StringUtils.isEmpty(bean.getMaintenanceTeam())){
            throw new UnifiedBusinessException("负责团队不能为空");
        }
        transEdaMasterBean.setMaintenanceTeam(bean.getMaintenanceTeam());
        transEdaMasterBean.setMaintenanceUi(bean.getMaintenanceUi());
        if(StringUtils.isEmpty(bean.getEdaStatus())){
            throw new UnifiedBusinessException("EDA状态不能为空");
        }
        transEdaMasterBean.setEdaStatus(Integer.parseInt(bean.getEdaStatus()));
        transEdaMasterBean.setEdaNo(bean.getOptLinkId());
        //需要查询EDA编号是否存在
        TBurypointsEdaMaster edaMaster = tBurypointsEdaMasterMapper.selectByOptLinkId(bean.getOptLinkId());
        if(edaMaster == null){
            //eda不存在，新增
            transEdaMasterBean.setBurypointsEdaMasterId(UidToolkit.generateUidDefault());
            //生成拼音简称
            transEdaMasterBean.setEdaAbbreveName(PinyinToolkit.converterToFirstSpellUpperCase(transEdaMasterBean.getEdaName()));
            tBurypointsEdaMasterMapper.insertSelective(transEdaMasterBean);
        }else{
            //eda存在，修改
            transEdaMasterBean.setBurypointsEdaMasterId(edaMaster.getBurypointsEdaMasterId());
            tBurypointsEdaMasterMapper.updateByPrimaryKeySelective(transEdaMasterBean);
        }
        //判断文件是否存在，如果存在则返回url地址，如果不存在，则新增一个eda文件
        TBurypointsEdaFileBean edaBean = new TBurypointsEdaFileBean();
        edaBean.setOptLinkId(bean.getOptLinkId());
        TBurypointsEdaFileBean drawioFileBean = tBurypointsEdaFileMapper.selectDrawioFile(edaBean);
        if(drawioFileBean == null){
            //如果是不呈现，则不创建文件
            if("0".equals(bean.getEdaStatus())){
                return "";
            }
            //不存在，创建一下
            //1.先判断该团队文件夹是否存在
            String folderId = tBurypointsEdaFileMapper.selectDrawioFolderId(bean.getMaintenanceTeam());
            if(StringUtils.isEmpty(folderId)){
                //目录不存在，要先建立文件目录
                TBurypointsEdaFileBean folderBean = new TBurypointsEdaFileBean();
                folderBean.setDrawioFolderId(UidToolkit.generateUidDefault()+"");
                folderBean.setDrawioFolderName(bean.getMaintenanceTeam().toUpperCase());
                folderBean.setNote("雄鹰同步操作链路动作");
                tBurypointsEdaFileMapper.insertEdaFolder(folderBean);
                folderId = folderBean.getDrawioFolderId();
            }
            //2.新增eda文件
            drawioFileBean = new TBurypointsEdaFileBean();
            drawioFileBean.setDrawioFileId(UidToolkit.generateUidDefault()+"");
            drawioFileBean.setDrawioFileName(bean.getOptLinkName().trim());
            drawioFileBean.setOptLinkId(bean.getOptLinkId());
            drawioFileBean.setDrawioFolderId(folderId);
            drawioFileBean.setNote("雄鹰同步操作链路动作");
            tBurypointsEdaFileMapper.insertEdaFile(drawioFileBean);
        }else if(bean.getOptLinkId().equals(drawioFileBean.getOptLinkId()) && !bean.getOptLinkName().trim().equals(drawioFileBean.getDrawioFileName())){
            //更新操作链路名称
            drawioFileBean.setDrawioFileName(bean.getOptLinkName().trim());
            drawioFileBean.setNote("雄鹰同步操作链路动作");
            tBurypointsEdaFileMapper.updateEdaFileInfo(drawioFileBean);
        }
        edaBean.setDrawioFileId(drawioFileBean.getDrawioFileId());
        return ConfigService.getAppConfig().getProperty("bp.eda.share.url","").replace("[TIMES]",System.currentTimeMillis()+"").replace("[FILE_ID]",edaBean.getDrawioFileId());
    }
}
