package com.wtyt.lgfesentryedamanagement.eda.service;

import com.wtyt.generator.toolkit.UidToolkit;
import com.wtyt.lg.commons.exception.BaseTipException;
import com.wtyt.lg.commons.toolkits.ListToolkit;
import com.wtyt.lgfesentryedamanagement.bkc.bean.response.Res5545025OBean;
import com.wtyt.lgfesentryedamanagement.dao.bean.*;
import com.wtyt.lgfesentryedamanagement.dao.mapper.*;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545025IBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545026IBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545027IBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545029IBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.response.Res5545027OBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.response.Res5545029OBean;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.ValidateFilter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.groups.Default;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：2024/5/10 9:09
 */
@Service
@Slf4j
public class BurypointsEdaBranchService {

    @Autowired
    private TBurypointsEdaMasterMapper tBurypointsEdaMasterMapper;

    @Autowired
    private TBurypointsEdaMapper tBurypointsEdaMapper;

    @Autowired
    private TBurypointsEdaRelMapper tBurypointsEdaRelMapper;

    @Autowired
    private TBurypointsBizTypeCfgMapper tBurypointsBizTypeCfgMapper;

    @Autowired
    private TBurypointsEdaLogMapper tBurypointsEdaLogMapper;
    
    @Autowired
    private TBurypointsEdaErrorMapper tBurypointsEdaErrorMapper;

    public Res5545025OBean add(Req5545025IBean data) throws Exception{
        data.setLogType(1);
        return InsertOrUpdateEdaBranch(data);

    }

    public Res5545025OBean update(Req5545025IBean data) throws Exception{
        if(StringUtils.isBlank(data.getBurypointsEdaRelId())){
            throw new BaseTipException("eda分支元素表id不能为空");
        }
        data.setLogType(2);
        return InsertOrUpdateEdaBranch(data);
    }

    private Res5545025OBean InsertOrUpdateEdaBranch(Req5545025IBean data) throws BaseTipException {
        Res5545025OBean oBean = new Res5545025OBean();
        //参数校验
       //  ValidateFilter.getFilterMessage(data, Default.class);

        //校验eda编码是否存在，不存在提示EDA编码有误，不允许新增EDA分支
        TBurypointsEdaMaster tBurypointsEdaMaster = tBurypointsEdaMasterMapper.selectByEdaNo(data.getEleEdaNo());
        if(tBurypointsEdaMaster == null || tBurypointsEdaMaster.getIsDel() != 0){
            throw new BaseTipException("eda编码不存在，请重新输入");
        }

        //校验eda编码分支名称是否存在，如果存在，不允许新增
        int otherCount = tBurypointsEdaMapper.existOther(data.getEdaBranch(), data.getEdaBranchName());
        if(otherCount > 0){
            throw new BaseTipException("当前输入的eda分支名称已存在，请重新输入");
        }

        //校验edaRel表中是否已经存在数据
        List<TBurypointsEdaRel> edaRels = tBurypointsEdaRelMapper.selectByParams(data);
        if(ListToolkit.isNotEmpty(edaRels)){
            throw new BaseTipException("eda数据已存在，请重新输入");
        }

        Res5545027OBean originData = null;
        if(data.getLogType() == 2){
            originData = tBurypointsEdaRelMapper.getEdaRelInfo(data.getBurypointsEdaRelId());
        }

        //业务处理
        TBurypointsEda tBurypointsEda = new TBurypointsEda();
        tBurypointsEda.setEdaParentNo(data.getEleEdaNo());
        tBurypointsEda.setEdaName(data.getEdaBranchName());
        tBurypointsEda.setEdaDesc(data.getEdaBranchDesc());
        //分支的edaType正好是master表edaType+1
        tBurypointsEda.setEdaType(tBurypointsEdaMaster.getEdaType() + 1);
        tBurypointsEda.setIsDel(0);

        //查看eda分支编号是否已存在
        TBurypointsEda existtBurypointsEda = tBurypointsEdaMapper.selectByEdaNo(data.getEdaBranch());
        if(existtBurypointsEda != null){
            tBurypointsEda.setBurypointsEdaId(existtBurypointsEda.getBurypointsEdaId());
            tBurypointsEdaMapper.updateByPrimaryKeySelective(tBurypointsEda);
        }else {
            tBurypointsEda.setEdaNo(data.getEdaBranch());
            tBurypointsEda.setBurypointsEdaId(UidToolkit.generateUidDefault());
            tBurypointsEda.setSyncTime(LocalDateTime.now());
            tBurypointsEdaMapper.insertSelective(tBurypointsEda);
        }

        //插入edaRel表
        TBurypointsEdaRel tBurypointsEdaRel = new TBurypointsEdaRel();
        tBurypointsEdaRel.setEleResourceId(data.getEleResourceId());
        tBurypointsEdaRel.setEleEdaNo(data.getEleEdaNo());
        tBurypointsEdaRel.setEdaBranch(data.getEdaBranch());
        tBurypointsEdaRel.setIsStart(Integer.parseInt(data.getIsStart()));
        tBurypointsEdaRel.setIsEnd(Integer.parseInt(data.getIsEnd()));
        tBurypointsEdaRel.setIsMBranch(Integer.parseInt(data.getIsMBranch()));
        tBurypointsEdaRel.setEleExtField(data.getEleExtField());
        tBurypointsEdaRel.setEleExtFieldVal(data.getEleExtFieldVal());
        tBurypointsEdaRel.setEnv(Integer.parseInt(data.getEnv()));
        if(StringUtils.isNotBlank(data.getBurDomainRelId())){
            tBurypointsEdaRel.setBurDomainRelId(Long.parseLong(data.getBurDomainRelId()));
        }
        if(data.getLogType() == 1){
            tBurypointsEdaRel.setBurypointsEdaRelId(UidToolkit.generateUidDefault());
            tBurypointsEdaRelMapper.insert(tBurypointsEdaRel);
        }
        if(data.getLogType() == 2){
            tBurypointsEdaRel.setBurypointsEdaRelId(Long.parseLong(data.getBurypointsEdaRelId()));
            tBurypointsEdaRelMapper.updateByPrimaryKey(tBurypointsEdaRel);
        }


        //记录异常类型
        List<Integer> errorTypes = new ArrayList<>();

        //更新bizType
        Integer branchType = "1".equals(data.getIsStart())? 1 : 2;
        updateBizType(data.getEdaBranch(),oBean,errorTypes,branchType,data.getLogType());

        //记录操作日志
        optLog(data, data.getLogType(),originData);

        //记录异常日志
        errorLog(data, tBurypointsEdaRel, errorTypes);

        return oBean;
    }

    private void errorLog(Req5545025IBean data, TBurypointsEdaRel tBurypointsEdaRel, List<Integer> errorTypes) {
        List<TBurypointsEdaError> tBurypointsEdaErrorList = new ArrayList<>();
        if(ListToolkit.isNotEmpty(errorTypes)){
            for (Integer errorType : errorTypes){
                TBurypointsEdaError curError = new TBurypointsEdaError();
                curError.setBurypointsEdaErrorId(UidToolkit.generateUidDefault());
                curError.setErrorType(errorType);
                curError.setBurypointsEdaRelId(tBurypointsEdaRel.getBurypointsEdaRelId());
                curError.setEnv(tBurypointsEdaRel.getEnv());
                curError.setLogType(data.getLogType());
                curError.setBurDomainRelId(tBurypointsEdaRel.getBurDomainRelId());
                curError.setOptTeam(data.getOptTeam());
                curError.setOptUserName(data.getOptUserName());
                tBurypointsEdaErrorList.add(curError);
            }
        }

        if(ListToolkit.isNotEmpty(tBurypointsEdaErrorList)){
            //插入异常记录表
            tBurypointsEdaErrorMapper.batchInsert(tBurypointsEdaErrorList);
        }
    }

    private void optLog(Req5545025IBean data,Integer logType,Res5545027OBean originData) {
        TBurypointsEdaLog tBurypointsEdaLog = new TBurypointsEdaLog();
        tBurypointsEdaLog.setBurypointsEdaLogId(UidToolkit.generateUidDefault());
        tBurypointsEdaLog.setOptTeam(data.getOptTeam());
        tBurypointsEdaLog.setOptUserName(data.getOptUserName());
        tBurypointsEdaLog.setLogType(logType);
        tBurypointsEdaLog.setEdaBranchNo(data.getEdaBranch());
        tBurypointsEdaLog.setOptType(1);
        String optNote = "";
        if(logType == 1){
            optNote = "新增"+ data.getEdaBranch()+"分支编码";
        }
        if(logType == 2 && originData != null){
            if(!data.getEdaName().equals(originData.getEdaName())){
                optNote += "eda模块编码名称发生变化；";
            }
            if(!data.getEdaBranchName().equals(originData.getEdaBranchName())){
                optNote += "eda分支编码名称发生变化；";
            }
            if(!data.getEleResourceId().equals(originData.getEleResourceId())){
                optNote += "元素编码发生变化；";
            }
            if(!desc(data.getEleExtField()).equals(desc(originData.getEleExtField()))){
                optNote += "扩展字段key名称发生变化；";
            }
            if(!desc(data.getEleExtFieldVal()).equals(desc(originData.getEleExtFieldVal()))){
                optNote += "扩展字段value值发生变化；";
            }
            if(!data.getIsStart().equals(originData.getIsStart())){
                optNote += "是否入口发生变化；";
            }
            if(!data.getIsEnd().equals(originData.getIsEnd())){
                optNote += "是否出口发生变化；";
            }
            if(!data.getIsMBranch().equals(originData.getIsMBranch())){
                optNote += "是否主流程发生变化；";
            }
            if(!data.getEnv().equals(originData.getEnv())){
                optNote += "环境端发生变化；";
            }
            if(!desc(data.getBurDomainRelId()).equals(desc(originData.getBurDomainRelId()))){
                optNote += "实体业务表发生变化；";
            }

        }
        if(logType == 3){
            optNote = "删除"+ data.getEdaBranch()+"分支编码";
        }

        if(StringUtils.isNotBlank(optNote)){
            tBurypointsEdaLog.setOptNote(optNote);
            this.insertSelective(tBurypointsEdaLog);
        }
    }

    private String desc(String s){
        return s == null ? "" : s;
    }
    public int insertSelective(TBurypointsEdaLog record) {
        return tBurypointsEdaLogMapper.insertSelective(record);
    }


    private void updateBizType(String edaBranch,Res5545025OBean oBean,List<Integer> errorTypes,Integer branchType,Integer logType) {
        TBurypointsEda tBurypointsEda = tBurypointsEdaMapper.selectByEdaNo(edaBranch);
        if(tBurypointsEda == null){
            return;
        }
        Integer oldBizType = tBurypointsEda.getBizType();
        //找出eda分支的所有录入信息
        Req5545025IBean param = new Req5545025IBean();
        param.setEdaBranch(edaBranch);
        List<TBurypointsEdaRel> allEdaRels = tBurypointsEdaRelMapper.selectByParams(param);
        if(ListToolkit.isEmpty(allEdaRels)){
            return;
        }
        //所有入口，出口
        List<TBurypointsEdaRel> allEdaEnters = new ArrayList<>();
        List<TBurypointsEdaRel> allEdaExists = new ArrayList<>();
        //最近入口，出口
        TBurypointsEdaRel lastEnter = null;
        TBurypointsEdaRel lastExist = null;

        allEdaEnters = allEdaRels.stream().filter(e->1==e.getIsStart()).sorted(Comparator.comparing(TBurypointsEdaRel::getLastModifiedTime).reversed()).collect(Collectors.toList());
        allEdaExists = allEdaRels.stream().filter(e->1==e.getIsEnd()).sorted(Comparator.comparing(TBurypointsEdaRel::getLastModifiedTime).reversed()).collect(Collectors.toList());
        if(ListToolkit.isNotEmpty(allEdaEnters)){
            lastEnter = allEdaEnters.get(0);
        }
        if(ListToolkit.isNotEmpty(allEdaExists)){
            lastExist = allEdaExists.get(0);
        }


        //根据入口，出口判断bizType
        TBurypointsBizTypeCfg bizType = getBizType(lastEnter,lastExist);

        if(oBean != null){
            oBean.setTips(bizType == null ? "未配置此场景，请联系阿金": bizType.getTips());
        }

        int newBizType = bizType == null ? -1 : bizType.getMixBizType();

        int judgeType = bizType == null ? 1 : bizType.getJudgeType();

        TBurypointsEda updateTBurypointsEda = new TBurypointsEda();
        updateTBurypointsEda.setBizType(newBizType);
        updateTBurypointsEda.setJudgeType(judgeType);
        updateTBurypointsEda.setBizTypeModifiedTime(LocalDateTime.now());
        updateTBurypointsEda.setBurypointsEdaId(tBurypointsEda.getBurypointsEdaId());

        //只有出口或者入口，直接更新
        if(lastEnter == null || lastExist == null){
            //更新
            tBurypointsEdaMapper.updateByPrimaryKeySelective(updateTBurypointsEda);
        }else {
            //判断之前的bizType是不是组合判断的，1：单个判断，2组合判断
            if(tBurypointsEda.getJudgeType() == 1){
                //更新
                tBurypointsEdaMapper.updateByPrimaryKeySelective(updateTBurypointsEda);
            }
            if(tBurypointsEda.getJudgeType() == 2 && compareBizType(newBizType,oldBizType) > 0){
                //更新
                tBurypointsEdaMapper.updateByPrimaryKeySelective(updateTBurypointsEda);
            }
            //修改场景补充
            if(logType == 2){
                if(allEdaEnters.size() == 1 && allEdaExists.size() > 0 && (branchType == 1 || branchType == 2)){
                    //更新
                    tBurypointsEdaMapper.updateByPrimaryKeySelective(updateTBurypointsEda);
                }

/*                //之前有入口没出口，录了一个出口
                if(allEdaEnters.size() > 0 && allEdaExists.size() == 1 && branchType == 2){
                    //更新
                    tBurypointsEdaMapper.updateByPrimaryKeySelective(updateTBurypointsEda);
                }*/
            }
            //批量删除场景补充，需要重新组合计算bizType
            if(logType == 3){
                TBurypointsBizTypeCfg deleteBizType = null;
                int oldDeleteBizType = -1 ;
                for (TBurypointsEdaRel curRenter : allEdaEnters){
                    for (TBurypointsEdaRel curExist : allEdaExists){
                        TBurypointsBizTypeCfg curBizTypeConfig = getBizType(curRenter,curExist);
                        if(curBizTypeConfig != null){
                            if(compareBizType(curBizTypeConfig.getMixBizType(),oldDeleteBizType) > 0){
                                deleteBizType = curBizTypeConfig;
                                oldDeleteBizType = curBizTypeConfig.getMixBizType();
                            }
                        }
                    }
                }
                if(oBean != null){
                    oBean.setTips(deleteBizType == null ? "未配置此场景，请联系阿金": deleteBizType.getTips());
                }
                int deleteNewBizType = deleteBizType == null ? -1 : deleteBizType.getMixBizType();

                int deleteJudgeType = deleteBizType == null ? 1 : deleteBizType.getJudgeType();

                updateTBurypointsEda.setBizType(deleteNewBizType);
                updateTBurypointsEda.setJudgeType(deleteJudgeType);
                //更新
                tBurypointsEdaMapper.updateByPrimaryKeySelective(updateTBurypointsEda);
            }
        }


        //判断当前录入信息触发了几种异常类型
        if(errorTypes != null){
            getErrorList(errorTypes, allEdaEnters, allEdaExists, lastEnter, lastExist);
        }

        /*if((allEdaEnters.size() > 1 && allEdaExists.size() > 1) && compareBizType(newBizType,oldBizType) > 0){
            //更新
            tBurypointsEdaMapper.updateByPrimaryKeySelective(updateTBurypointsEda);
        }*/

        /*if(allEdaEnters.size() == 1 && allEdaExists.size() == 1){
            //更新
            tBurypointsEdaMapper.updateByPrimaryKeySelective(updateTBurypointsEda);
        }*/

        //之前有出口没入口，录了一个入口
        /*if(allEdaEnters.size() == 1 && allEdaExists.size() > 0 && branchType == 1){
            //更新
            tBurypointsEdaMapper.updateByPrimaryKeySelective(updateTBurypointsEda);
        }

        //之前有入口没出口，录了一个出口
        if(allEdaEnters.size() > 0 && allEdaExists.size() == 1 && branchType == 2){
            //更新
            tBurypointsEdaMapper.updateByPrimaryKeySelective(updateTBurypointsEda);
        }

        //只有出口 || 只有入口
        if(lastEnter == null || lastExist == null){
            //更新
            tBurypointsEdaMapper.updateByPrimaryKeySelective(updateTBurypointsEda);
        }*/
    }

    private static void getErrorList(List<Integer> errorTypes, List<TBurypointsEdaRel> allEdaEnters, List<TBurypointsEdaRel> allEdaExists, TBurypointsEdaRel lastEnter, TBurypointsEdaRel lastExist) {
        //1.多出口bizId关联的业务表不一致
        if(allEdaExists.size() > 1){
            Long firstBurDomainRelId = allEdaExists.get(0).getBurDomainRelId();
            long firstBurDomainRelIdValue = firstBurDomainRelId == null ? -1 : firstBurDomainRelId;
            for (TBurypointsEdaRel curRel : allEdaExists){
                long curRelValue = curRel.getBurDomainRelId() == null ? -1 : curRel.getBurDomainRelId();
                if(firstBurDomainRelIdValue != curRelValue){
                    errorTypes.add(1);
                    break;
                }
            }
        }

        if(lastEnter != null && lastExist != null){
            //2.入口出口环境端不一致
            if(lastEnter.getEnv().compareTo(lastExist.getEnv()) != 0){
                errorTypes.add(2);
            }
            //3.入口有bizId出口没有bizId
            if(lastEnter.getBurDomainRelId() != null && lastExist.getBurDomainRelId() == null){
                errorTypes.add(3);
            }
            //5.入口出口bizId关联的业务表不一致（都有表的情况下）
            if(lastEnter.getBurDomainRelId() != null && lastExist.getBurDomainRelId() != null){
                if(lastEnter.getBurDomainRelId().compareTo(lastExist.getBurDomainRelId()) != 0){
                    errorTypes.add(5);
                }
            }
        }

        //4.录入多个入口
        if(allEdaEnters.size() > 1){
            errorTypes.add(4);
        }
    }

    /**
     * bizType优先级比较 0>3=4>1=2>5>-1
     * 返回正数 newBizType 优先级高
     * 返回负数 newBizType 优先级高
     * 返回0 优先级相同
     * */
    private int compareBizType(int newBizType, int oldBizType) {
        Map<Integer,Integer> priorityMap = new HashMap<>();
        priorityMap.put(0,0);
        priorityMap.put(3,1);
        priorityMap.put(4,1);
        priorityMap.put(1,2);
        priorityMap.put(2,2);
        priorityMap.put(5,3);
        priorityMap.put(-1,4);
        return priorityMap.get(oldBizType) - priorityMap.get(newBizType);
    }

    private TBurypointsBizTypeCfg getBizType(TBurypointsEdaRel lastEnter, TBurypointsEdaRel lastExist) {

        TBurypointsBizTypeCfg bizTypeCfg = null;

        //如果只有一端,这两个不可能同时为空
        if(lastEnter == null || lastExist == null){

            int env = lastEnter == null ? lastExist.getEnv() : lastEnter.getEnv();

            Long burDomainRelId = lastEnter == null ? lastExist.getBurDomainRelId() : lastEnter.getBurDomainRelId();

            int hasTable = burDomainRelId == null ? 0 : 1;

            bizTypeCfg = tBurypointsBizTypeCfgMapper.getBizTypeBySingle(env,hasTable);
            if(bizTypeCfg != null){
                bizTypeCfg.setJudgeType(1);
                bizTypeCfg.setMixBizType(bizTypeCfg.getEnterBizType());
            }
            return bizTypeCfg;

        }

        //出口入手都有值
        int startEnv = lastEnter.getEnv();
        int startHasTable = lastEnter.getBurDomainRelId() == null ? 0 : 1;
        Long startBurDomainRelId = lastEnter.getBurDomainRelId();

        int endEnv = lastExist.getEnv();
        int endHasTable = lastExist.getBurDomainRelId() == null ? 0 : 1;
        Long endBurDomainRelId = lastExist.getBurDomainRelId();

        bizTypeCfg = tBurypointsBizTypeCfgMapper.getBizType(startEnv,startHasTable,endEnv,endHasTable);
        //当bizType = 0 时候，判断前后的表是否一致，不一致降级
        if(bizTypeCfg != null && 0 == bizTypeCfg.getMixBizType()){
            if(startBurDomainRelId != null && endBurDomainRelId != null && startBurDomainRelId.compareTo(endBurDomainRelId) != 0){
                //环境端为PC降级为4
                if(bizTypeCfg.getEnterEnv() == 1){
                    bizTypeCfg.setMixBizType(4);
                }
                //环境端为app/小程序降级为3
                if(bizTypeCfg.getEnterEnv() == 2 || bizTypeCfg.getEnterEnv() == 3 ){
                    bizTypeCfg.setMixBizType(3);
                }
            }
        }
        if(bizTypeCfg != null){
            bizTypeCfg.setJudgeType(2);
        }
        return bizTypeCfg;
    }

    public Res5545027OBean info(Req5545027IBean data) {
        //参数校验
        ValidateFilter.getFilterMessage(data, Default.class);

        Res5545027OBean oBean = tBurypointsEdaRelMapper.getEdaRelInfo(data.getBurypointsEdaRelId());

        return oBean;
    }

    public Res5545029OBean delete(Req5545029IBean data) throws Exception{

        if(ListToolkit.isEmpty(data.getBurypointsEdaRelIds())){
            throw new BaseTipException("eda分支元素ID列表不能为空");
        }
        Res5545029OBean oBean = new Res5545029OBean();
        //查询删除元素
        List<TBurypointsEdaRel> edaRels = tBurypointsEdaRelMapper.selectByIds(data.getBurypointsEdaRelIds());
        if(ListToolkit.isEmpty(edaRels)){
            return oBean;
        }
        List<Long> relIds = edaRels.stream().map(TBurypointsEdaRel::getBurypointsEdaRelId).collect(Collectors.toList());
        //删除元素
        tBurypointsEdaRelMapper.deleteByIds(relIds);
        //记录日志
        for (TBurypointsEdaRel curBean : edaRels){
            Req5545025IBean iBean = new Req5545025IBean();
            iBean.setOptTeam(data.getOptTeam());
            iBean.setOptUserName(data.getOptUserName());
            iBean.setEdaBranch(curBean.getEdaBranch());
            optLog(iBean,3,null);
        }
        oBean.setSuccessList(relIds);

        List<String> edaBranches = edaRels.stream().map(TBurypointsEdaRel::getEdaBranch).distinct().collect(Collectors.toList());

        List<TBurypointsEdaRel> existedaRels = tBurypointsEdaRelMapper.selectByEdaBranch(edaBranches);

        List<String> needDeleteEdaBranches = new ArrayList<>();
        List<String> needUpdateEdaBizType = new ArrayList<>();


        if(ListToolkit.isNotEmpty(existedaRels)){
            List<String> notDeleteEdaBranches = existedaRels.stream().map(TBurypointsEdaRel::getEdaBranch).distinct().collect(Collectors.toList());
            for (String edaBranch : edaBranches){
                if(!notDeleteEdaBranches.contains(edaBranch)){
                    needDeleteEdaBranches.add(edaBranch);
                }else {
                    needUpdateEdaBizType.add(edaBranch);
                }
            }
        }else {
            needDeleteEdaBranches = edaBranches;
        }

        //删除EDA分支
        if(ListToolkit.isNotEmpty(needDeleteEdaBranches)){
            tBurypointsEdaMapper.deleteByEdaNos(needDeleteEdaBranches);
        }
        
        //更新EDA分支bizType
        for (String edaBranch : needUpdateEdaBizType){
            updateBizType(edaBranch,null,null,null,3);
        }
        return oBean;
    }
}
