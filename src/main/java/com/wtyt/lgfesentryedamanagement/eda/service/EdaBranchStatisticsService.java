package com.wtyt.lgfesentryedamanagement.eda.service;

import com.aliyun.odps.Column;
import com.aliyun.odps.OdpsType;
import com.aliyun.odps.data.ArrayRecord;
import com.wtyt.lgfesentryedamanagement.dao.mapper.*;
import com.wtyt.lgfesentryedamanagement.eda.bean.response.Res5545041OBean;
import com.wtyt.lgfesentryedamanagement.eda.utils.DataphinSqlTools;
import com.wtyt.lgfesentryedamanagement.eda.utils.TableDataUtils;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545041IBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545043IBean;
import com.wtyt.lgfesentryedamanagement.dao.bean.TBuryPointsSearchTimelinessClosedloop;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wtyt.lg.commons.exception.UnifiedBusinessException;

import java.util.*;
import javax.annotation.Resource;

import com.aliyun.odps.Instance;
import com.aliyun.odps.Odps;
import com.aliyun.odps.data.Record;
import com.aliyun.odps.task.SQLTask;
import com.aliyun.odps.tunnel.InstanceTunnel;
import com.aliyun.odps.tunnel.io.TunnelRecordReader;
import com.wtyt.generator.toolkit.UidToolkit;
import java.util.concurrent.ThreadPoolExecutor;
import org.json.JSONObject;
import org.apache.commons.lang3.StringUtils;

@Service
@Slf4j
public class EdaBranchStatisticsService {
    @Resource
    private TBurypointsSearchTimelinessClosedloopMapper tBurypointsSearchTimelinessClosedloopMapper;
    @Autowired
    private Odps odps;
    @Resource(name = "commonThreadPool")
    private ThreadPoolExecutor commonThreadPool;

    /**
     * 查询结果列表5545043
     * 
     * @param bean
     * @return
     * @throws Exception
     */
    public List<TBuryPointsSearchTimelinessClosedloop> getSearchList(Req5545043IBean bean) throws Exception {
        return tBurypointsSearchTimelinessClosedloopMapper.getSearchListByOptLinkId(bean);
    }

    /**
     * 新增查询时效闭环任务5545042
     * 
     * @param bean
     * @return
     * @throws Exception
     */
    public void addSearch(Req5545041IBean data) throws Exception {
        List<TBuryPointsSearchTimelinessClosedloop> addSearchList = new ArrayList<>();

        TBuryPointsSearchTimelinessClosedloop addSearch = new TBuryPointsSearchTimelinessClosedloop();
        Long seTimelinessClosedloopId = UidToolkit.generateUidDefault();
        addSearch.setSeTimelinessClosedloopId(seTimelinessClosedloopId);
        addSearch.setOptLinkId(data.getOptLinkId());
        addSearch.setEdaBranchNos(data.getEdaBranchNos());
        addSearch.setRealUserIds(data.getRealUserIds());
        addSearch.setOrgIds(data.getOrgIds());
        addSearch.setSearchDs(data.getSearchDs());
        addSearch.setStartDateId(data.getStartDateId());
        addSearch.setEndDateId(data.getEndDateId());
        addSearch.setSearchState("0");// 查询中
        if (StringUtils.isNotBlank(data.getType())) {
            addSearch.setType(data.getType());
        }
        if (StringUtils.isNotBlank(data.getEnv())) {
            addSearch.setEnv(data.getEnv());
        }

        addSearchList.add(addSearch);

        tBurypointsSearchTimelinessClosedloopMapper.batchInsert(addSearchList);

        // 异步发起dataphin查询
        commonThreadPool.execute(() -> {
            searchJob(data, seTimelinessClosedloopId);
            // // 探测查询
            // if(StringUtils.isNotBlank(data.getType()) && "1".equals(data.getType())){

            // } else {
            // // 台账查询

            // }

        });

    }

    /**
     * 从dataphin查询分支闭环和时效数据
     * 
     * @param bean
     * @return
     * @throws Exception
     */
    private List<Res5545041OBean> searchBranchCloseRateAndCostTimeInDataphin(Req5545041IBean bean) throws Exception {
        
        String executeSql = "";

        String type = bean.getType(); // 1：探测查询 0：台账查询
        if ("1".equals(type)) { // 探测查询
            executeSql = DataphinSqlTools.getEdabranchtimelessclosedrateDetectSql(bean);
        } else {
            executeSql = DataphinSqlTools.getEdabranchtimelessclosedrateSql(bean);
        }

        log.info("查询sql：" + executeSql);
        try {
            Instance instance = SQLTask.run(odps, executeSql);
            instance.waitForSuccess();
            // 创建 InstanceTunnel
            InstanceTunnel tunnel = new InstanceTunnel(odps);
            // 根据 instance id，创建 DownloadSession
            InstanceTunnel.DownloadSession session = tunnel.createDownloadSession(odps.getDefaultProject(),
                    instance.getId());
            long count = session.getRecordCount();
            List<Res5545041OBean> list = new ArrayList<>();
            if (count > 0) {
                // 获取数据的写法与 TableTunnel 一样
                TunnelRecordReader reader = session.openRecordReader(0, count);
                Record record;
                while ((record = reader.read()) != null) {
                    Map<String, String> result = TableDataUtils.getLine(record);
                    // BkcMakerToolkits.buildQueryResourceRs(list, record, resultType);
                    String edaBranchNo = result.get("eda_branch_no");
                    String startNum = result.get("start_busid_cnt");
                    String closeNum = result.get("oper_close_num");
                    String closeRate = result.get("oper_closed_rate");
                    if(StringUtils.isNotBlank(closeRate)){
                        closeRate = DataphinSqlTools.formatWithStringFormat(closeRate);
                    }
                    String avgCostTime = result.get("oper_avg_time");
                    if(StringUtils.isNotBlank(avgCostTime)){
                        avgCostTime = DataphinSqlTools.formatWithStringFormat(avgCostTime); 
                    }
                    String per50CostTime = result.get("oper_50percent_time");
                    if(StringUtils.isNotBlank(per50CostTime)){
                        per50CostTime = DataphinSqlTools.formatWithStringFormat(per50CostTime);
                    }
                    String per75CostTime = result.get("oper_75percent_time");
                    if(StringUtils.isNotBlank(per75CostTime)){
                        per75CostTime = DataphinSqlTools.formatWithStringFormat(per75CostTime);
                    }
                    String per80CostTime = result.get("oper_80percent_time");
                    if(StringUtils.isNotBlank(per80CostTime)){
                        per80CostTime = DataphinSqlTools.formatWithStringFormat(per80CostTime);
                    }
                    String per85CostTime = result.get("oper_85percent_time");
                    if(StringUtils.isNotBlank(per85CostTime)){
                        per85CostTime = DataphinSqlTools.formatWithStringFormat(per85CostTime); 
                    }
                    String per90CostTime = result.get("oper_90percent_time");
                    if(StringUtils.isNotBlank(per90CostTime)){
                        per90CostTime = DataphinSqlTools.formatWithStringFormat(per90CostTime); 
                    }
                    
                    Res5545041OBean rsp = new Res5545041OBean();

                    rsp.setEdaBranchNo(edaBranchNo);
                    rsp.setStartNum(startNum);
                    rsp.setCloseNum(closeNum);
                    rsp.setCloseRate(closeRate);
                    rsp.setAvgCostTime(avgCostTime);
                    rsp.setPer50CostTime(per50CostTime);
                    rsp.setPer75CostTime(per75CostTime);
                    rsp.setPer80CostTime(per80CostTime);
                    rsp.setPer85CostTime(per85CostTime);
                    rsp.setPer90CostTime(per90CostTime);

                    list.add(rsp);

                }
                reader.close();
            }
            return list;
        } catch (Exception e) {
            log.error("从dataphin查询结果异常！！" + e.getMessage() + "(" + executeSql + ")", e);
            throw new UnifiedBusinessException("从dataphin查询结果异常！！" + e.getMessage(), e);
        }
    }

    /** 发起查询job并记录返回结果 */
    private void searchJob(Req5545041IBean bean, Long seTimelinessClosedloopId) {
        try {
            List<Res5545041OBean> searchResult = searchBranchCloseRateAndCostTimeInDataphin(bean);
            // 记录查询结果
            if (searchResult == null || searchResult.size() == 0) {
                tBurypointsSearchTimelinessClosedloopMapper.updateOneSearchResultById(seTimelinessClosedloopId, 2, "",
                        "查询结果为空！！");
                return;
            }
            org.json.JSONArray resultArray = new org.json.JSONArray();
            searchResult.forEach(res -> {
                JSONObject eachJson = new JSONObject();
                eachJson.put("edaBranchNo", res.getEdaBranchNo());
                eachJson.put("startNum", res.getStartNum());
                eachJson.put("closeNum", res.getCloseNum());
                eachJson.put("closeRate", res.getCloseRate());
                eachJson.put("avgCostTime", res.getAvgCostTime());
                eachJson.put("per50CostTime", res.getPer50CostTime());
                eachJson.put("per75CostTime", res.getPer75CostTime());
                eachJson.put("per80CostTime", res.getPer80CostTime());
                eachJson.put("per85CostTime", res.getPer85CostTime());
                eachJson.put("per90CostTime", res.getPer90CostTime());
                // 将每个 JSONObject 添加到 JSONArray 中
                resultArray.put(eachJson);
            });
            // 使用 toString() 方法将 JSONArray 转换为 JSON 字符串
            String searchResultJson = resultArray.toString();
            tBurypointsSearchTimelinessClosedloopMapper.updateOneSearchResultById(seTimelinessClosedloopId, 1,
                    searchResultJson, "");
        } catch (Exception e) {
            log.error("查询结果异常！！" + e.getMessage(), e);
            tBurypointsSearchTimelinessClosedloopMapper.updateOneSearchResultById(seTimelinessClosedloopId, 2, "",
                    "查询结果异常！！" + e.getMessage());
        }

    }

    /**
     * 根据操作链路ID+分支编号多个从dataphin查询时效闭环数据 5545041
     * 
     * @param optLinkId
     * @param edaBranchNos
     * @param startData    开始日期 例：20250601，默认昨日
     * @param endData      结束日期 例：20250601，默认昨日
     * @return
     */
    public List<Res5545041OBean> getBranchCloseRateAndCostTime(Req5545041IBean bean) throws Exception {
        return searchBranchCloseRateAndCostTimeInDataphin(bean);
    }
}
