package com.wtyt.lgfesentryedamanagement.eda.utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang3.time.DateFormatUtils;
import java.util.*;

import com.aliyun.odps.Column;
import com.aliyun.odps.OdpsType;
import com.aliyun.odps.data.Record;


public class TableDataUtils {

    private static final Logger log = LoggerFactory.getLogger(TableDataUtils.class);
    /**
     * 从数据库sql执行结果record中读取一行数据并将类型转换成Java可操作的对象
     * 目前支持的类型：String、Bigint、Decimal、Double、Float、Int、Smallint、Tinyint、Date、Datetime
     * @param record
     * @return
     * */
    public static Map<String, String> getLine(Record record) {
        Map<String, String> line = new HashMap<>();
        for (Column column : record.getColumns()) {
            Object value = record.get(column.getName());
            if (null == value) {
                // do nothing
            } else {
                if (column.getTypeInfo().getOdpsType() == OdpsType.STRING) {
                    line.put(column.getName(), record.getString(column.getName()));
                } else if (column.getTypeInfo().getOdpsType() == OdpsType.BIGINT
                        || column.getTypeInfo().getOdpsType() == OdpsType.DECIMAL
                        || column.getTypeInfo().getOdpsType() == OdpsType.DOUBLE
                        || column.getTypeInfo().getOdpsType() == OdpsType.FLOAT
                        || column.getTypeInfo().getOdpsType() == OdpsType.INT
                        || column.getTypeInfo().getOdpsType() == OdpsType.SMALLINT
                        || column.getTypeInfo().getOdpsType() == OdpsType.TINYINT) {
                    line.put(column.getName(), value.toString());
                } else if (column.getTypeInfo().getOdpsType() == OdpsType.DATE
                        || column.getTypeInfo().getOdpsType() == OdpsType.DATETIME) {
                    line.put(column.getName(), DateFormatUtils.format((Date) value, "yyyy-MM-dd HH:mm:ss"));
                } else {
                    log.error("不支持的类型");
                }
            }
        }
        return line;
    }


}
