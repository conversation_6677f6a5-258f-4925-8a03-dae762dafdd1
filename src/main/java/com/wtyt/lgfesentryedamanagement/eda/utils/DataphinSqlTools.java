package com.wtyt.lgfesentryedamanagement.eda.utils;
import com.ctrip.framework.apollo.ConfigService;
import org.apache.commons.lang3.StringUtils;
import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545041IBean;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class DataphinSqlTools {
  // 获取探测时效闭环sql  
  public static String getEdabranchtimelessclosedrateDetectSql(Req5545041IBean reqBean) {
       String apolloExecuteSql = ConfigService.getConfig("execute.dataphin.sql").getProperty("execute.front.detected.edabranchtimelessclosedrate.dataphin.sql","");
       String cicdEnv = ConfigService.getConfig("application").getProperty("cicd.env","");
       // 解析要执行的sql
        if(StringUtils.isEmpty(apolloExecuteSql)){
            throw new UnifiedBusinessException("要执行查询的dataphin sql不存在");
        }
         //解析出字符串中${xxx}的字段出来
         Pattern pattern = Pattern.compile("\\$\\{(.*?)\\}");
         Matcher matcher = pattern.matcher(apolloExecuteSql);
         StringBuffer sb = new StringBuffer();
         Map<String,Object> fieldMap = new HashMap<>();
         fieldMap.put("opt_link_id",reqBean.getOptLinkId());
         fieldMap.put("start_bizdate",reqBean.getStartDateId());
         fieldMap.put("end_bizdate",reqBean.getEndDateId());
         fieldMap.put("config_log56_ods","PRO".equals(cicdEnv) ? "LOG56_ODS" : "LOG56_ODS_dev"); // 分支配置环境

         String realUserOrgIdsSql = "";
         if(StringUtils.isNotEmpty(reqBean.getOrgIds())){
            realUserOrgIdsSql+= "where t.real_org_id in ("+reqBean.getOrgIds()+")";
         }
         if(StringUtils.isNotEmpty(reqBean.getRealUserIds())){
            realUserOrgIdsSql+=StringUtils.isNotEmpty(realUserOrgIdsSql) ? "and t.real_user_id in ("+reqBean.getRealUserIds()+")" : "where t.real_user_id in ("+reqBean.getRealUserIds()+")";
         }
         fieldMap.put("condition_real_user_org_ids",realUserOrgIdsSql);

         String log56Ods = "FAT".equals(reqBean.getEnv()) ? "LOG56_ODS_dev" : "LOG56_ODS"; // 埋点数据环境
         fieldMap.put("log56_ods",log56Ods);

         String condition_eda_branch_no = "";
         // 根据逗号拆字符串并用单引号包裹后再用逗号拼接
         String newEdaBranchNoString = DataphinSqlTools.formatEdaBranchNos(reqBean.getEdaBranchNos());
         if(StringUtils.isNotBlank(newEdaBranchNoString)){
          condition_eda_branch_no = "where eda_branch_no in ("+newEdaBranchNoString+")";
         } 
        
         fieldMap.put("condition_eda_branch_no",condition_eda_branch_no);

         while(matcher.find()) {
             // 获取匹配到的变量名
             String variable = matcher.group(1);
             // 查找变量名对应的值
             Object replacement = fieldMap.get(variable);
             if (replacement == null) {
                 // 如果没有找到对应的值，使用默认值
                 throw new UnifiedBusinessException("解析sql中字段"+variable+"失败！！字段对于的值是null");
             }
             // 替换找到的匹配项
             matcher.appendReplacement(sb, String.valueOf(replacement));
         }
         matcher.appendTail(sb);
         return sb.toString().replaceAll("\\\n"," ").replaceAll("\\\t"," ");
    }

    // 获取台账时效闭环sql  
  public static String getEdabranchtimelessclosedrateSql(Req5545041IBean reqBean) {
    String apolloExecuteSql = ConfigService.getConfig("execute.dataphin.sql").getProperty("execute.front.edabranchtimelessclosedrate.dataphin.sql","");
     // 解析要执行的sql
     if(StringUtils.isEmpty(apolloExecuteSql)){
         throw new UnifiedBusinessException("要执行查询的dataphin sql不存在");
     }
      //解析出字符串中${xxx}的字段出来
      Pattern pattern = Pattern.compile("\\$\\{(.*?)\\}");
      Matcher matcher = pattern.matcher(apolloExecuteSql);
      StringBuffer sb = new StringBuffer();
      Map<String,Object> fieldMap = new HashMap<>();
      fieldMap.put("opt_link_id",reqBean.getOptLinkId());
      fieldMap.put("searchDs",reqBean.getSearchDs());
      fieldMap.put("start_bizdate",reqBean.getStartDateId());
      fieldMap.put("end_bizdate",reqBean.getEndDateId());

      String realUserOrgIdsSql = "";
      if(StringUtils.isNotEmpty(reqBean.getOrgIds())){
         realUserOrgIdsSql+= "and a.org_id in ("+reqBean.getOrgIds()+")";
      }
      if(StringUtils.isNotEmpty(reqBean.getRealUserIds())){
         realUserOrgIdsSql+=StringUtils.isNotEmpty(realUserOrgIdsSql) ? " and a.real_user_id in ("+reqBean.getRealUserIds()+")" : " and a.real_user_id in ("+reqBean.getRealUserIds()+")";
      }
      fieldMap.put("condition_real_user_org_ids",realUserOrgIdsSql);

      String condition_eda_branch_no = "";
      // 根据逗号拆字符串并用单引号包裹后再用逗号拼接
      String newEdaBranchNoString = DataphinSqlTools.formatEdaBranchNos(reqBean.getEdaBranchNos());
      if(StringUtils.isNotBlank(newEdaBranchNoString)){
       condition_eda_branch_no = "and a.eda_branch_no in ("+newEdaBranchNoString+")";
      } 
     
      fieldMap.put("condition_eda_branch_no",condition_eda_branch_no);

      while(matcher.find()) {
          // 获取匹配到的变量名
          String variable = matcher.group(1);
          // 查找变量名对应的值
          Object replacement = fieldMap.get(variable);
          if (replacement == null) {
              // 如果没有找到对应的值，使用默认值
              throw new UnifiedBusinessException("解析sql中字段"+variable+"失败！！字段对于的值是null");
          }
          // 替换找到的匹配项
          matcher.appendReplacement(sb, String.valueOf(replacement));
      }
      matcher.appendTail(sb);
      return sb.toString().replaceAll("\\\n"," ").replaceAll("\\\t"," ");
 }
/**
     * 将 edaBranchNos 字符串按逗号拆分，并用单引号包裹后拼接
     * @param edaBranchNos 分支编号字符串，多个编号用逗号分隔
     * @return 格式化后的分支编号字符串
     */
    public static String formatEdaBranchNos(String edaBranchNos) {
      if (StringUtils.isBlank(edaBranchNos)) {
          return "";
      }
      String[] edaBranchNoArr = edaBranchNos.split(",");
      return Arrays.stream(edaBranchNoArr)
              .filter(StringUtils::isNotBlank) // 过滤空值
              .map(edaBranchNo -> "'" + edaBranchNo + "'")
              .collect(Collectors.joining(","));
  }
  public static String formatWithStringFormat(String numberStr) {
    try {
        double number = Double.parseDouble(numberStr);
        return String.format("%.2f", number);
    } catch (NumberFormatException e) {
        return numberStr;
    }
}
}