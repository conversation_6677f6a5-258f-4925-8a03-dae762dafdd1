package com.wtyt.lgfesentryedamanagement.bkc.controller;


import com.wtyt.lg.commons.bean.BaseBean;
import com.wtyt.lg.commons.bean.ResDataBean;
import com.wtyt.lgfesentryedamanagement.bkc.bean.BkcRecalReqBean;
import com.wtyt.lgfesentryedamanagement.bkc.bean.FrtDetectRspBean;
import com.wtyt.lgfesentryedamanagement.bkc.bean.param.Req5545009IBean;
import com.wtyt.lgfesentryedamanagement.bkc.bean.param.Req5545031IBean;
import com.wtyt.lgfesentryedamanagement.bkc.bean.response.Req5545020OBean;
import com.wtyt.lgfesentryedamanagement.bkc.bean.response.Req5545032OBean;
import com.wtyt.lgfesentryedamanagement.bkc.impl.BkcCalServiceImpl;
import com.wtyt.lgfesentryedamanagement.bkc.utils.BkcMakerToolkits;
import com.wtyt.lgfesentryedamanagement.pub.annotation.Attribute;
import com.wtyt.lgfesentryedamanagement.pub.bean.BaseTokenBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.Calendar;

/**
 * eda相关计算的接口
 */
@RestController
@RequestMapping("/eda/cal/")
public class BkcCalController {



    @Autowired
    private BkcCalServiceImpl service;//


    /**
     * 探测获取eda数据的最后结果
     * @return
     */
    @RequestMapping(value="get/v1/colverrate")
    @Attribute(sid = "5545009", name = "EDA后端获取覆盖率接口")
    public ResDataBean<?> getDetectedCoverRate(@RequestBody BaseBean<Req5545009IBean> bean){
        service.getDetectedCoverRate(bean.getData());
        return new ResDataBean<>().success();
    }

    /**
     * 探测获取eda数据的最后结果
     * @return
     */
    @RequestMapping(value="get/v1/detected")
    @Attribute(sid = "5545031", name = "EDA探测接口")
    public ResDataBean<?> toDetected(@RequestBody BaseBean<Req5545031IBean> bean){
        service.toDetected(bean.getData());
        return new ResDataBean<>().success();
    }

    @RequestMapping(value="get/v1/getDetectedList")
    @Attribute(sid = "5545032", name = "探测结果列表接口")
    public ResDataBean<Req5545032OBean> getDetectedList(@RequestBody BaseBean<Req5545031IBean> bean) throws IOException {
        return new ResDataBean<Req5545032OBean>().success(service.getDetectedList(bean.getData()));
    }

    @RequestMapping(value="get/v1/getDetectedDetail")
    @Attribute(sid = "5545033", name = "探测详情接口")
    public ResDataBean<Req5545032OBean> getDetectedDetail(@RequestBody BaseBean<Req5545031IBean> bean) throws IOException {
        return new ResDataBean<Req5545032OBean>().success(service.getDetectedDetail(bean.getData()));
    }


    @RequestMapping(value="get/v1/getColverRateList")
    @Attribute(sid = "5545020", name = "EDA后端覆盖率查询记录列表")
    public ResDataBean<Req5545020OBean> getColverRateList(@RequestBody BaseBean<BaseTokenBean> bean) throws IOException {
        return new ResDataBean<Req5545020OBean>().success(service.getColverRateList(bean.getData()));
    }


    /**
     * 重新计算某个eda的结果信息
     * 废弃了--如果要重新用，需要重新改
     * @return
     */
    @RequestMapping(value="put/v1/coverrecal")
    public  String recalCoverRate(@RequestBody String reqJson){
        BkcRecalReqBean reqBean =  BkcMakerToolkits.analysisRecalReqBean(reqJson);
        String linkLocation = BkcMakerToolkits.analysisLinkLocation(reqJson);
        return service.recalCoverRate(reqBean, linkLocation);
    }

    /**
     * 计算eda的数据，job每日跑，调用接口即可
     * @return
     */
    @RequestMapping(value="put/v1/coverjob")
    @Attribute(sid = "5545024", name = "JOB计算eda的每日数据")
    public String  calEveryCoverJob(@RequestBody(required = false) String reqJson){
        Calendar calDate = BkcMakerToolkits.analysisEveryDayTime(reqJson);
        String linkLocation = BkcMakerToolkits.analysisLinkLocation(reqJson);
        return service.calEveryCoverJob(calDate, linkLocation);
    }

    @RequestMapping(value="put/v1/newCoverjob")
    @Attribute(sid = "5545036", name = "JOB计算新eda的每日数据")
    public String  calNewEveryCoverJob(@RequestBody(required = false) String reqJson){
        Calendar calDate = BkcMakerToolkits.analysisEveryDayTime(reqJson);
        return service.calNewEveryCoverJob(calDate);
    }

    /**
     * 5545040-完善每日后端覆盖率数据--暂时没用到
     * @param reqJson
     * @return
     */
    @RequestMapping(value="put/v1/coverjobImprove")
    @Attribute(sid = "5545040", name = "完善每日后端覆盖率数据")
    public String  coverjobImprove(@RequestBody(required = false) String reqJson){
        Calendar calDate = BkcMakerToolkits.analysisEveryDayTime(reqJson);
        return service.coverjobImprove(calDate);
    }

    /**
     * 修复数据，将配置非fvalue非jsonArray的改成jsonArray
     * @return
     */
    @RequestMapping(value="put/v1/fvalue2array")
    public String changeEsIntererfaceConfigToArray(){
        return service.changeEsIntererfaceConfigToArray("1");
    }

}
