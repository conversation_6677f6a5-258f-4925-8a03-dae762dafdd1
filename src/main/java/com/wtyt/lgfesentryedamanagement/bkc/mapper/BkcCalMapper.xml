<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.lgfesentryedamanagement.bkc.mapper.BkcCalMapper">

    <sql id="Bk_Base_Column_List">
        BURYPOINTS_EDA_BK_CONFIG_ID AS burypointsEdaBkConfigId,
        EDA_NO AS edaNo,
        ES_INDEX_NAME AS esIndexName,
        INTERFACE_INDEX_NAME interfaceIndexName,
        INTERFACE_EXCLUDE_FILTERS interfaceExcludeFilters,
        INTERFACE_PROJECT_NAME AS interfaceProjectName,
        INTERFACE_TAG AS interfaceTag,
        INTERFACE_TYPE AS interfaceType,
        CAL_TYPE AS calType,
        FILTER_TYPE AS filterType,
        LOG_FILTERS_KEYWORDS AS logFiltersKeywords,
        LOG_FILTERS_EXCLUDE_KEYWORDS AS logFiltersExcludeKeywords,
        INTERFACE_FILTERS AS interfaceFilters,
        IS_DEL AS isDel,
        DATE_FORMAT(CREATED_TIME, '%Y-%m-%d %H:%i:%s') AS createdTime,
        DATE_FORMAT(LAST_MODIFIED_TIME, '%Y-%m-%d %H:%i:%s') AS lastModifiedTime,
        NOTE AS note,
        LINK_LOCATION,
        EDA_BRANCH,
        NULL AS ELE_RESOURCE_ID,
        NULL AS ELE_EXT_FIELD,
        NULL AS ELE_EXT_FIELD_VAL,
        '1' as CONFIG_TYPE
    </sql>

    <sql id="Ft_Base_Column_List">
        BURYPOINTS_EDA_FT_CONFIG_ID AS burypointsEdaBkConfigId,
        EDA_NO AS edaNo,
        NULL AS esIndexName,
        NULL interfaceIndexName,
        NULL interfaceExcludeFilters,
        NULL AS interfaceProjectName,
        NULL AS interfaceTag,
        NULL AS interfaceType,
        NULL AS calType,
        NULL AS filterType,
        NULL AS logFiltersKeywords,
        NULL AS logFiltersExcludeKeywords,
        NULL AS interfaceFilters,
        IS_DEL AS isDel,
        DATE_FORMAT(CREATED_TIME, '%Y-%m-%d %H:%i:%s') AS createdTime,
        DATE_FORMAT(LAST_MODIFIED_TIME, '%Y-%m-%d %H:%i:%s') AS lastModifiedTime,
        NOTE AS note,
        LINK_LOCATION AS linkLocation,
        EDA_BRANCH,
        ELE_RESOURCE_ID,
        ELE_EXT_FIELD,
        ELE_EXT_FIELD_VAL,
        '2' as CONFIG_TYPE
    </sql>
    <!--查询edaNo对应的后端结果的配置-->
    <select id="selectEdaNoConfList" resultType="com.wtyt.lgfesentryedamanagement.bkc.bean.BkcEdaConfBean">
        SELECT
        <include refid="Bk_Base_Column_List" />
        FROM  t_burypoints_eda_bk_config
        WHERE IS_DEL = 0 AND EDA_NO = #{edaNo}

        UNION ALL

        SELECT
        <include refid="Ft_Base_Column_List" />
        FROM  t_burypoints_eda_ft_config
        WHERE IS_DEL = 0 AND EDA_NO = #{edaNo}

    </select>

    <!--查询edaNo对应的后端结果的配置-->
    <select id="selectEdaNoConfListByLinkLocation" resultType="com.wtyt.lgfesentryedamanagement.bkc.bean.BkcEdaConfBean">
        SELECT
        <include refid="Bk_Base_Column_List" />
        FROM  t_burypoints_eda_bk_config
        WHERE IS_DEL = 0 AND EDA_NO = #{edaNo} and LINK_LOCATION = #{linkLocation}

        UNION ALL

        SELECT
        <include refid="Ft_Base_Column_List" />
        FROM  t_burypoints_eda_ft_config
        WHERE IS_DEL = 0 AND EDA_NO = #{edaNo} and LINK_LOCATION = #{linkLocation}

    </select>


    <!--查询时间点对应的最新的batchId-->
    <select id="queryRecalNewestBatchId" resultType="String">
        select max(BATCH_ID) from T_BURYPOINTS_EDA_BK_RS where IS_DEL = 0 and QUERY_DATE=#{queryDate} and LINK_LOCATION = #{linkLocation}
    </select>

    <!--将老的对应批次的计算结果删除掉-->
    <update id="updateOldBatchEsQueryDeled">
        update T_BURYPOINTS_EDA_BK_DETAIL set IS_DEL = 1,LAST_MODIFIED_TIME=now() where  BATCH_ID = #{bacthId} and CAL_DATE=#{queryDate} and EDA_BK_CONFIG_ID=#{burypointsEdaBkConfigId} and is_del = 0 and LINK_LOCATION = #{linkLocation}
    </update>

    <!--将老的对应的批次合并的结果删除掉-->
    <update id="updateOldBatchMergedRsDeled">
        update T_BURYPOINTS_EDA_BK_RS set IS_DEL = 1,LAST_MODIFIED_TIME=now() where  BATCH_ID = #{bacthId} and QUERY_DATE=#{calDate} and EDA_NO=#{edaNo} and is_del = 0 and LINK_LOCATION = #{linkLocation}
    </update>

    <!--将新的记录插入进去-->
    <insert id="insertBatchMergedNewRs">
        INSERT INTO T_BURYPOINTS_EDA_BK_RS
        (EDA_NO, BATCH_ID, CAL_SUM_VALUE, QUERY_DATE, LINK_LOCATION)
        select
        RS.edaNo,
        #{bacthId},
        sum(RS.calValue) sumCalValue,
        #{calDate},
        RS.linkLocation
        from
        (
        select
        cf.EDA_NO edaNo,
        cf.LINK_LOCATION linkLo
        cation,
        if(cf.CAL_TYPE = 0,
        ifnull(td.CAL_RS_VALUE, 0) ,
        0-ifnull(td.CAL_RS_VALUE, 0)) calValue
        from
        t_burypoints_eda_bk_config cf
        left join t_burypoints_eda_bk_detail td on
        td.EDA_BK_CONFIG_ID = cf.BURYPOINTS_EDA_BK_CONFIG_ID and td.LINK_LOCATION = #{linkLocation}
        and td.IS_DEL = 0
        where
        td.CAL_DATE = #{calDate}
        and td.BATCH_ID =  #{bacthId}
        and cf.eda_no=#{edaNo}
        and cf.LINK_LOCATION = #{linkLocation}
        ) RS  group by edaNo, linkLocation
    </insert>


    <!--查询eda入口前后端计算的配置信息-->
    <select id="selectEdaBkCoverConfigList" resultType="com.wtyt.lgfesentryedamanagement.bkc.bean.BkcEdaConfBean">
        SELECT
        <include refid="Bk_Base_Column_List" />
        FROM  t_burypoints_eda_bk_config
        WHERE IS_DEL = 0 and LINK_LOCATION = #{linkLocation}

    </select>

    <!--查询eda入口前后端计算的配置信息-->
    <select id="selectEdaCoverConfigList" resultType="com.wtyt.lgfesentryedamanagement.bkc.bean.BkcEdaConfBean">
        SELECT
        <include refid="Bk_Base_Column_List" />
        FROM  t_burypoints_eda_bk_config
        WHERE IS_DEL = 0 and LINK_LOCATION = #{linkLocation}

        UNION ALL

        SELECT
        <include refid="Ft_Base_Column_List" />
        FROM  t_burypoints_eda_ft_config
        WHERE IS_DEL = 0 AND LINK_LOCATION = #{linkLocation}

    </select>

    <!--插入最终的结果信息-->
    <insert id="insertEachEsQueryRs">
        INSERT INTO T_BURYPOINTS_EDA_BK_DETAIL (
        EDA_BK_CONFIG_ID,
        CAL_DATE,
        CAL_INDEX_NAME,
        CAL_RS_INFO,
        CAL_RS_VALUE,
        BATCH_ID,
        CAL_LANG,
        LINK_LOCATION
        )
        VALUES (
        #{rs.burypointsEdaBkConfigId},
        #{queryDate},
        #{rs.queryRealIndexName},
        #{rs.calRsInfo},
        #{rs.calRsValue},
        #{bacthId},
        #{rs.queryLang},
        #{linkLocation}
        )
    </insert>


    <!--插入最终的结果信息-->
    <insert id="insertEachEsQueryRsNew">
        INSERT INTO T_BURYPOINTS_EDA_COVER_RSD (
        BURYPOINTS_EDA_COVER_RSD_ID,
        BURYPOINTS_EDA_COVER_BK_ID,
        CAL_DATE,
        CAL_INDEX_NAME,
        CAL_RS_INFO,
        CAL_RS_VALUE,
        BATCH_ID,
        CAL_LANG)
        VALUES (
        #{burypointsEdaCoverRsdId},
        #{rs.configId},
        #{queryDate},
        #{rs.queryRealIndexName},
        #{rs.calRsInfo},
        #{rs.calRsValue},
        #{bacthId},
        #{rs.queryLang}
        )
    </insert>

    <!--插入最终的结果信息-->
    <insert id="insertEachEsQueryRsFt">
        INSERT INTO T_BURYPOINTS_EDA_FT_DETAIL (
        EDA_BK_CONFIG_ID,
        CAL_DATE,
        CAL_RS_INFO,
        CAL_RS_VALUE,
        BATCH_ID,
        LINK_LOCATION
        )
        VALUES (
        #{rs.burypointsEdaBkConfigId},
        #{queryDate},
        #{rs.calRsInfo},
        #{rs.calRsValue},
        #{bacthId},
        #{linkLocation}
        )
    </insert>

    <!--插入最终的结果信息-->
    <insert id="insertFtEachEsQueryRs">
        INSERT INTO T_BURYPOINTS_EDA_FT_DETAIL (
        BURYPOINTS_EDA_FT_CONFIG_ID,
        CAL_DATE,
        CAL_RS_INFO,
        CAL_RS_VALUE,
        BATCH_ID,
        LINK_LOCATION
        )
        VALUES (
        #{rs.burypointsEdaBkConfigId},
        #{queryDate},
        #{rs.calRsInfo},
        #{rs.calRsValue},
        #{bacthId},
        #{linkLocation}
        )
    </insert>



    <!--合并最终的计算结果信息-->
    <insert id="insertSelectMergeCalDateRs">
        INSERT INTO T_BURYPOINTS_EDA_BK_RS(EDA_NO, BATCH_ID, CAL_SUM_VALUE, QUERY_DATE, LINK_LOCATION)
        select
            RS.edaNo,
            #{bacthId},
            sum(RS.calValue) sumCalValue,
            #{queryDate},
            RS.linkLocation
        from
        (
            select
                cf.EDA_NO edaNo,
                cf.LINK_LOCATION linkLocation,
                if(cf.CAL_TYPE = 0,
                ifnull(td.CAL_RS_VALUE, 0) ,
                0-ifnull(td.CAL_RS_VALUE, 0)) calValue
            from t_burypoints_eda_bk_config cf
            left join t_burypoints_eda_bk_detail td on td.EDA_BK_CONFIG_ID = cf.BURYPOINTS_EDA_BK_CONFIG_ID and td.IS_DEL = 0 and td.LINK_LOCATION = #{linkLocation}
            where td.CAL_DATE = #{queryDate}
            and td.BATCH_ID =  #{bacthId}
            and cf.LINK_LOCATION = #{linkLocation}

            union all

            select
                cf.EDA_NO edaNo,
                cf.LINK_LOCATION linkLocation,
                td.CAL_RS_VALUE calValue
            from t_burypoints_eda_ft_config cf
            left join t_burypoints_eda_ft_detail td on td.EDA_BK_CONFIG_ID = cf.BURYPOINTS_EDA_FT_CONFIG_ID and td.IS_DEL = 0 and td.LINK_LOCATION = #{linkLocation}
            where td.CAL_DATE = #{queryDate}
            and td.BATCH_ID =  #{bacthId}
            and cf.LINK_LOCATION = #{linkLocation}
        ) RS
        group by RS.edaNo, RS.linkLocation
    </insert>

    <!--合并最终的计算结果信息-->
    <insert id="insertSelectMergeCalDateRsNew">
        INSERT INTO T_BURYPOINTS_EDA_COVER_RS(BURYPOINTS_EDA_COVER_RS_ID, EDA_MXCELL_ID, OPT_LINK_ID, BATCH_ID, CAL_SUM_VALUE, QUERY_DATE)
        VALUES
        <foreach collection="list" item="bean" separator="," >
            (#{bean.burypointsEdaCoverRsId}, #{bean.edaMxcellId},#{bean.optLinkId},#{bean.batchId},#{bean.calSumValue}, #{bean.queryDate})
        </foreach>
    </insert>

    <select id="queryMergeCalDateRsNew" resultType="com.wtyt.lgfesentryedamanagement.bkc.bean.BkcEdaCoverRsBean">
        select
            RS.EDA_MXCELL_ID,
            RS.OPT_LINK_ID,
            #{bacthId} batchId,
            sum(RS.calValue) calSumValue,
            #{queryDate} queryDate
        from
        (
        select
        cf.OPT_LINK_ID,
        cf.EDA_MXCELL_ID,
        NVL(CAL_RS_VALUE, 0) calValue
        from T_BURYPOINTS_EDA_COVER_BK cf
        left join T_BURYPOINTS_EDA_COVER_RSD td on td.BURYPOINTS_EDA_COVER_BK_ID = cf.BURYPOINTS_EDA_COVER_BK_ID and td.IS_DEL = 0
        where td.CAL_DATE = #{queryDate}
        and td.BATCH_ID =  #{bacthId}
        ) RS
        group by RS.OPT_LINK_ID, RS.EDA_MXCELL_ID
    </select>

    <select id="queryLastBatchIdToday" resultType="java.lang.Long">
        select batch_id from T_BURYPOINTS_EDA_COVER_RSD where is_del = 0 and CAL_DATE=#{queryDate} limit 1
    </select>

    <!--跑的老数据删除掉-->
    <update id="updateOldEsQueryDeledNew">
        update T_BURYPOINTS_EDA_COVER_RSD set IS_DEL = 1,LAST_MODIFIED_TIME=now() where  BATCH_ID != #{bacthId} and CAL_DATE=#{queryDate} and is_del = 0
    </update>

    <!--跑的老数据删除掉-->
    <update id="updateOldEsQueryDeled">
        update T_BURYPOINTS_EDA_BK_DETAIL set IS_DEL = 1,LAST_MODIFIED_TIME=now() where  BATCH_ID != #{bacthId} and CAL_DATE=#{queryDate} and EDA_BK_CONFIG_ID=#{burypointsEdaBkConfigId} and is_del = 0 and LINK_LOCATION = #{linkLocation}
    </update>
    <!--跑的老数据删除掉-->
    <update id="updateOldEsQueryDeledFt">
        update T_BURYPOINTS_EDA_FT_DETAIL set IS_DEL = 1,LAST_MODIFIED_TIME=now() where  BATCH_ID != #{bacthId} and CAL_DATE=#{queryDate} and EDA_BK_CONFIG_ID=#{burypointsEdaBkConfigId} and is_del = 0 and LINK_LOCATION = #{linkLocation}
    </update>

    <!--跑的老数据删除掉-->
    <update id="updateMergeCalDateRsOldDeled">
        update T_BURYPOINTS_EDA_BK_RS set IS_DEL = 1,LAST_MODIFIED_TIME=now() where  BATCH_ID != #{bacthId} and QUERY_DATE=#{queryDate} and is_del = 0 and LINK_LOCATION = #{linkLocation}
    </update>
    <update id="updateMergeCalDateRsOldDeledNew">
        update T_BURYPOINTS_EDA_COVER_RS set IS_DEL = 1,LAST_MODIFIED_TIME=now() where  BATCH_ID != #{bacthId} and QUERY_DATE=#{queryDate} and is_del = 0
    </update>

    <!--更细eda后端元数据接口配置的信息-->
    <update id="updateEdaBkConfInterfaceFvalues">
        update t_burypoints_eda_bk_config set INTERFACE_FILTERS =#{interfaceFilterValue},last_modified_time=now()  where BURYPOINTS_EDA_BK_CONFIG_ID = #{edaBkConfId}
    </update>
    <update id="removeOldEsQueryRs">
        update T_BURYPOINTS_EDA_COVER_RSD set IS_DEL = 1,LAST_MODIFIED_TIME=now() WHERE  BURYPOINTS_EDA_COVER_RSD_ID != #{burypointsEdaCoverRsdId} and is_del = 0
    </update>
</mapper>