package com.wtyt.lgfesentryedamanagement.bkc.mapper;


import com.wtyt.lgfesentryedamanagement.bkc.bean.BkcEdaConfBean;
import com.wtyt.lgfesentryedamanagement.bkc.bean.BkcEdaCoverRsBean;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BkcCalMapper {
    List<BkcEdaConfBean> selectEdaNoConfList(@Param(value = "edaNo") String edaNo);

    List<BkcEdaConfBean> selectEdaNoConfListByLinkLocation(@Param(value = "edaNo") String edaNo, @Param("linkLocation") String linkLocation);

    /**
     * 查询时间点最新的处理的batchId
     * @param calDate
     * @param edaNo
     * @return
     */
    String queryRecalNewestBatchId(@Param(value="queryDate") String calDate,@Param(value="edaNo") String edaNo, @Param("linkLocation") String linkLocation);

    /**
     * 将老的对应的批次计算结果更新为删除
     * @param queryDate
     * @param batchId
     * @param burypointsEdaBkConfigId
     */
    void updateOldBatchEsQueryDeled(@Param(value="queryDate")  String queryDate,@Param(value = "bacthId") String batchId,@Param(value="burypointsEdaBkConfigId") long burypointsEdaBkConfigId, @Param("linkLocation") String linkLocation);

    /**
     * 将老的merge的结果删除掉
     * @param edaNo
     * @param calDate
     * @param batchId
     */
    void updateOldBatchMergedRsDeled(@Param(value = "edaNo") String edaNo,@Param(value="calDate") String calDate,@Param(value = "bacthId") String batchId, @Param("linkLocation") String linkLocation);

    /**
     *
     * @param edaNo
     * @param calDate
     * @param batchId
     */
    void insertBatchMergedNewRs(@Param(value = "edaNo") String edaNo,@Param(value="calDate") String calDate,@Param(value = "bacthId") String batchId, @Param("linkLocation") String linkLocation);

    /**
     * 查询eda 后端覆盖率配置
     * @return
     */
    List<BkcEdaConfBean> selectEdaBkCoverConfigList(String linkLocation);
    /**
     * 查询eda 前后端覆盖率配置
     * @return
     */
    List<BkcEdaConfBean> selectEdaCoverConfigList(String linkLocation);

    /**
     * 插入最终的es的执行结果信息
     *
     * @param currentBatchId
     * @param queryDate
     * @param task
     */
    void insertEachEsQueryRs(@Param(value = "bacthId") long currentBatchId, @Param(value="queryDate") String queryDate, @Param(value = "rs") BkcEdaConfBean task, @Param("linkLocation") String linkLocation);
    void insertEachEsQueryRsFt(@Param(value = "bacthId") long currentBatchId, @Param(value="queryDate") String queryDate, @Param(value = "rs") BkcEdaConfBean task, @Param("linkLocation") String linkLocation);


    void insertEachEsQueryRsNew(@Param("burypointsEdaCoverRsdId") String burypointsEdaCoverRsdId, @Param(value = "bacthId") long currentBatchId, @Param(value="queryDate") String queryDate, @Param(value = "rs") BkcEdaConfBean task);


    /**
     * 插入前端覆盖率查询结果
     * @param currentBatchId
     * @param queryDate
     * @param task
     * @param linkLocation
     */
    void insertFtEachEsQueryRs(@Param(value = "bacthId") long currentBatchId, @Param(value="queryDate") String queryDate, @Param(value = "rs") BkcEdaConfBean task, @Param("linkLocation") String linkLocation);
    /**
     * 汇总es跑的结果信息
     * @param currentBatchId
     * @param queryDate
     */
    void insertSelectMergeCalDateRs(@Param(value = "bacthId")  long currentBatchId,@Param(value="queryDate") String queryDate, @Param("linkLocation") String linkLocation);
    void insertSelectMergeCalDateRsNew(List<BkcEdaCoverRsBean> list);

    /**
     * 查询明细生成的汇总数据
     * @param currentBatchId
     * @param queryDate
     * @return
     */
    List<BkcEdaCoverRsBean> queryMergeCalDateRsNew(@Param(value = "bacthId")  long currentBatchId,@Param(value="queryDate") String queryDate);

    /**
     * 将当天跑的老的数据逻辑删除掉
     * @param currentBatchId
     * @param queryDate
     */
    void updateOldEsQueryDeledNew(@Param(value = "bacthId") long currentBatchId,@Param(value="queryDate")  String queryDate);
    void updateOldEsQueryDeled(@Param(value = "bacthId") long currentBatchId,@Param(value="queryDate")  String queryDate,@Param(value="burypointsEdaBkConfigId") String burypointsEdaBkConfigId, @Param("linkLocation") String linkLocation);
    void updateOldEsQueryDeledFt(@Param(value = "bacthId") long currentBatchId,@Param(value="queryDate")  String queryDate,@Param(value="burypointsEdaBkConfigId") String burypointsEdaBkConfigId, @Param("linkLocation") String linkLocation);


    /**
     * 老版本的数据逻辑删除掉
     * @param currentBatchId
     * @param queryDate
     */
    void updateMergeCalDateRsOldDeled(@Param(value = "bacthId") long currentBatchId,@Param(value="queryDate") String queryDate, @Param("linkLocation") String linkLocation);
    void updateMergeCalDateRsOldDeledNew(@Param(value = "bacthId") long currentBatchId,@Param(value="queryDate") String queryDate);

    /**
     * 更新edafilter的接口过滤配置
     * @param burypointsEdaBkConfigId
     * @param newInterfaceFilterValues
     */
    void updateEdaBkConfInterfaceFvalues(@Param(value="edaBkConfId") long burypointsEdaBkConfigId,@Param(value="interfaceFilterValue") String newInterfaceFilterValues);

    /**
     * 获取最新数据的batchId
     * @param queryDate
     * @return
     */
    long queryLastBatchIdToday(String queryDate);

    /**
     * 逻辑删除
     * @param burypointsEdaCoverRsdId
     */
    void removeOldEsQueryRs(String burypointsEdaCoverRsdId);
}
