package com.wtyt.lgfesentryedamanagement.bkc.bean;

import lombok.Data;

import java.util.List;

/**
 * dataphin前端的埋点探测
 */
@Data
public class FrtDetectRspBean {

    private String edaNo;
    private String edaBranch;//分支编号
    private String configId;//覆盖率配置ID
    private long ftPv;//
    private List<FrtDetectRspBean> resourceIdList;
    private long pv;
    private long uv;
    private String burypointsEdaPointsId;
    private String resourceId;
    private String edaMxcellId;
    private String eleExtField;//
    private String eleExtFieldVal;//
    private String optLinkId;
    private String appTag;
    private String eleExtJson;
    private String eleColumnJson;

}
