package com.wtyt.lgfesentryedamanagement.bkc.bean.response;

import com.wtyt.lgfesentryedamanagement.bkc.bean.BkDetectRspBean;
import com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsDetect;
import lombok.Data;

import java.util.List;

@Data
public class Req5545032OBean {

    private String searchId;
    private String edaName;
    private String optLinkId;
    private String startTime;
    private String endTime;
    private String searchTime;
    private String env;
    private String searchItemStr;
    private List<String> searchItem;
    private String searchState;
    private String searchData;
    private String coverData;
    private String edaMxcellId;
    private String resourceId;
    private String appTag;
    private String eleExtField;//
    private String eleExtFieldVal;//
    private String searchFailInfo;

    private String eleExtJson;
    private String eleColumnJson;

    private String pv;
    private String uv;
    private List<Req5545032OBean> edaList;//节点列表

    private long fenzi;//分子pv
    private long fenmu;//分母pv
    private String coverRate;//覆盖率
    private String configType;//覆盖率类型 1-后端覆盖率 2-前端覆盖率

    private List<Req5545032OBean> coverList;//节点内的覆盖率数据
    private List<Req5545032OBean> resourceIdList;//节点内pvuv数据




}
