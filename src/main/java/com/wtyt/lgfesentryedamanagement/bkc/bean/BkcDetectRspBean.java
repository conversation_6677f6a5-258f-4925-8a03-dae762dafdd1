package com.wtyt.lgfesentryedamanagement.bkc.bean;

public class BkcDetectRspBean {

    private long executeRsValue;

    private String executeLang;//

    private String executeIndexNames;//

    private BkcEdaConfBean  confBean;//

    public BkcEdaConfBean getConfBean() {
        return confBean;
    }

    public void setConfBean(BkcEdaConfBean confBean) {
        this.confBean = confBean;
    }

    public String getExecuteLang() {
        return executeLang;
    }

    public void setExecuteLang(String executeLang) {
        this.executeLang = executeLang;
    }

    public String getExecuteIndexNames() {
        return executeIndexNames;
    }

    public void setExecuteIndexNames(String executeIndexNames) {
        this.executeIndexNames = executeIndexNames;
    }

    public long getExecuteRsValue() {
        return executeRsValue;
    }

    public void setExecuteRsValue(long executeRsValue) {
        this.executeRsValue = executeRsValue;
    }
}
