package com.wtyt.lgfesentryedamanagement.bkc.bean;

import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import org.apache.commons.lang3.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class BkcRecalReqBean {

    private String edaNo;//需要更新的edaNo编号

    private String calDate;//需要计算的日期  yyyyMMdd格式

    private String  batchId;//执行的批次id

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public String getEdaNo() {
        return edaNo;
    }

    public void setEdaNo(String edaNo) {
        this.edaNo = edaNo;
    }

    public Date getCalDateTime(){
        try {
            return new SimpleDateFormat("yyyyMMdd").parse(this.getCalDate());
        } catch (ParseException e) {
            throw new UnifiedBusinessException(this.getCalDate()+"为不合法的计算时间格式，仅支持yyyyMMdd格式的时间");
        }
    }

    /**
     * 如果不传时间，则返回昨天
     * @return
     */
    public String getCalDate() {
        if(StringUtils.isEmpty(this.calDate)){
             Calendar  calendar = Calendar.getInstance();
             calendar.add(Calendar.DAY_OF_MONTH,-1);
             this.calDate = new SimpleDateFormat("yyyyMMdd").format(calendar.getTime());
        }
        return calDate;
    }

    public void setCalDate(String calDate) {
        this.calDate = calDate;
    }


    public BkcRecalReqBean() {
    }

    public BkcRecalReqBean(String edaNo, String calDate) {
        this.edaNo = edaNo;
        this.calDate = calDate;
    }
}
