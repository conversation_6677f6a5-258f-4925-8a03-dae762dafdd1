package com.wtyt.lgfesentryedamanagement.bkc.bean.param;

import com.wtyt.lgfesentryedamanagement.pub.bean.BaseTokenBean;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=false)
public class Req5545009IBean extends BaseTokenBean {

    private static final long serialVersionUID = -1L;

    private String startTime;
    private String endTime;//

    private String edaNo;//配置的id
    private String linkLocation;//链路位置

}
