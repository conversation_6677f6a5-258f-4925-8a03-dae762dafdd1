package com.wtyt.lgfesentryedamanagement.bkc.bean;

import lombok.Data;

import java.util.List;

/**
 * dataphin前端的埋点探测
 */
@Data
public class BkDetectRspBean {
    //前端覆盖率列表
    private List<FrtDetectRspBean> ftCoverList;
    //后端覆盖率列表
    private List<BkcDetectRspBean> bkCoverList;

    private long ftpv;//前端覆盖率pv
    private long bkpv;//后端覆盖率pv

    private long fenzi;//分子pv
    private long fenmu;//分母pv
    private String coverRate;//覆盖率

    private String edaMxcellId;
    private String optLinkId;
    private String configType;//覆盖率类型 1-后端覆盖率 2-前端覆盖率

}
