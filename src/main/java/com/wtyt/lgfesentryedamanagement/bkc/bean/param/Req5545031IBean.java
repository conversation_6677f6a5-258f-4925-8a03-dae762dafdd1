package com.wtyt.lgfesentryedamanagement.bkc.bean.param;

import com.wtyt.lgfesentryedamanagement.pub.bean.BaseTokenBean;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper=false)
public class Req5545031IBean implements Serializable {

    private static final long serialVersionUID = -1L;

    private String startTime;
    private String endTime;

    private String env;//配置的id
    private String optLinkId;//链路id
    private String searchId;
    private List<String> searchItem;//链路id

}
