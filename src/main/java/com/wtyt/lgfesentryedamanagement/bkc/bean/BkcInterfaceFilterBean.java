package com.wtyt.lgfesentryedamanagement.bkc.bean;

import java.util.List;

public class BkcInterfaceFilterBean {



    private String filterKey;
    private List<String> filterValue;

    /**
     * 0 is
     * 1 is not
     * 2 is one of
     * 3 is not one of
     * 4 exists
     * 5 not exists
     * 6 like 模糊匹配
     * 7 not like 模糊匹配
     */
    private String relType="0";//关系类型


    public String getFilterKey() {
        return filterKey;
    }

    public void setFilterKey(String filterKey) {
        this.filterKey = filterKey;
    }

    public List<String> getFilterValue() {
        return filterValue;
    }

    public void setFilterValue(List<String> filterValue) {
        this.filterValue = filterValue;
    }


    public String getRelType() {
        return relType;
    }

    public void setRelType(String relType) {
        this.relType = relType;
    }

}
