package com.wtyt.lgfesentryedamanagement.bkc.bean.response;

import lombok.Data;

import java.util.List;

@Data
public class Req5545020OBean {

    private List<Req5545020OBean> edaList;

    private String edaNo;//eda编号
    /**
     * EDA中文名称
     */
    private String edaName;

    /**
     * EDA缩写名称
     */
    private String edaAbbreveName;
    private String searchState;//查询状态
    private String startTime;//查询的开始时间
    private String endTime;//查询的结束时间
    private String executeTime;//查询时间
    private String bkValue;//后端的pv值
    private String ftValue;//前端的pv值
    private String coverRate;//覆盖率结果
    private String ftExecuteSql;//前端执行的sql语句

    private String linkLocation;

    private String searchFailInfo;

    private List<Req5545020OBean> backList;
    private List<Req5545020OBean> frontList;
    private String configId;//对应的后端配置id
    private String eachValue;//对应的每条执行结果------前端覆盖率也用
    private String eachInfo;//对应的每条执行结果描述
    private String eachDsl;//执行的每条的dsl语句
    private List<String> interfaceFilters;//接口类型的过滤条件
    private String eachEdaNo;//对应的每条的eda编号

    private List<Req5545020OBean> branchList;
    private String edaBranch;
    private String edaBranchName;
    private String configType;//覆盖率类型
    private String eleResourceId;//埋点元素id
    private String eleExtField;//元素扩展字段里的翻译字段
    private String eleExtFieldVal;//元素扩展字段里的翻译字段值


}
