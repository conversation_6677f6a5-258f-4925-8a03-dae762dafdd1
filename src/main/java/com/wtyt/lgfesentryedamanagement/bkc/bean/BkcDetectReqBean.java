package com.wtyt.lgfesentryedamanagement.bkc.bean;

public class BkcDetectReqBean {

    private String startTime;
    private String endTime;//

    private String edaNo;//eda编号

    private String startDs;//开始的分区

    private String endDs;//结束的分区

    private String configId;//覆盖率ID

    private String edaBranch;//eda分支编号

    private String linkLocation;//

    private String fillSql = "";
    private String bizDate;
    private String optLinkId;
    private String env;

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getEdaNo() {
        return edaNo;
    }

    public void setEdaNo(String edaNo) {
        this.edaNo = edaNo;
    }


    public String getStartDs() {
        return startDs;
    }

    public void setStartDs(String startDs) {
        this.startDs = startDs;
    }

    public String getEndDs() {
        return endDs;
    }

    public void setEndDs(String endDs) {
        this.endDs = endDs;
    }

    public String getConfigId() {
        return configId;
    }

    public void setConfigId(String configId) {
        this.configId = configId;
    }

    public String getEdaBranch() {
        return edaBranch;
    }

    public void setEdaBranch(String edaBranch) {
        this.edaBranch = edaBranch;
    }

    public String getLinkLocation() {
        return linkLocation;
    }

    public void setLinkLocation(String linkLocation) {
        this.linkLocation = linkLocation;
    }

    public String getFillSql() {
        return fillSql;
    }

    public void setFillSql(String fillSql) {
        this.fillSql = fillSql;
    }

    public BkcDetectReqBean(String startTime, String endTime, String edaNo, String configId, String edaBranch, String linkLocation, String fillSql) {
        this.startTime = startTime;
        this.endTime = endTime;
        this.edaNo = edaNo;
        this.configId = configId;
        this.edaBranch = edaBranch;
        this.linkLocation = linkLocation;
        this.fillSql = fillSql;
    }

    public BkcDetectReqBean() {
    }

    public String getBizDate() {
        return bizDate;
    }

    public void setBizDate(String bizDate) {
        this.bizDate = bizDate;
    }

    public String getOptLinkId() {
        return optLinkId;
    }

    public void setOptLinkId(String optLinkId) {
        this.optLinkId = optLinkId;
    }

    public String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        this.env = env;
    }
}
