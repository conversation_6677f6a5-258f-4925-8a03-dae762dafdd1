package com.wtyt.lgfesentryedamanagement.bkc.bean;

import lombok.Data;

@Data
public class BkcEdaConfBean {

    // 主键
    private long burypointsEdaBkConfigId;

    // eda的编号
    private String edaNo;

    // es索引前缀名称
    private String esIndexName;

    //接口全埋时的索引名称
    private String interfaceIndexName;//

    // 接口所在的项目名
    private String interfaceProjectName;

    // 接口标识（主要是接口id）
    private String interfaceTag;

    // 接口类型 ,structs:3,springmvc:2,卡友地带网关:1,金融网关:0
    private String interfaceType;

    // 统计的数值计算类型 ,减法计算类型:1,加法计算类型:0
    private String calType;

    // 当前的过滤类型 ,从接口全埋中过滤:1,已日志方式过滤:0
    private String filterType;

    // 日志类型的过滤条件-多个以逗号相隔
    private String logFiltersKeywords;

    // 日志类型的过滤条件(排除类型的)-多个以逗号相隔
    private String logFiltersExcludeKeywords;//

    private long calRsValue=0;//计算的最终结果

    private String interfaceFilters;//

    private String interfaceExcludeFilters;//

    private String note;//

    private String linkLocation;
    /**
     * EDA分支编号
     */
    private String edaBranch;

    private String configType;//覆盖率类型

    private String eleResourceId;//埋点元素id

    private String eleExtField;//元素扩展字段里的翻译字段

    private String eleExtFieldVal;//元素扩展字段里的翻译字段值


    public String calRsInfo="查询成功！！";

    public String  queryLang;//查询的语句

    public String queryRealIndexName;//执行到的索引名称

    private String configId;//通用的记录主键ID
    private String edaMxcellId;//节点ID
    private String optLinkId;//操作链路ID


    //其他业务用到的参数
    private String burypointsEdaCoverRsdId;

}
