package com.wtyt.lgfesentryedamanagement.bkc.utils;

import cn.hutool.core.date.DateUtil;
import com.aliyun.odps.data.Record;
import com.ctrip.framework.apollo.ConfigService;
import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import com.wtyt.lgfesentryedamanagement.bkc.bean.*;
import com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsCoverRate;
import com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEda;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class BkcMakerToolkits {

    private static final Logger log = LoggerFactory.getLogger(BkcMakerToolkits.class);

    /**
     * 获取探测的请求bean
     * @param reqJson
     * @return
     */
    public static BkcDetectReqBean analysisDetectReqBean(String reqJson) {
        BkcDetectReqBean reqBean  =  new BkcDetectReqBean();
        if(StringUtils.isEmpty(reqJson)){
            throw new UnifiedBusinessException("请求参数全为空！！");
        }
        JSONObject datajson = new JSONObject(reqJson).getJSONObject("data");//
        String edaNo = datajson.getString("edaNo");
        String startTime = datajson.getString("startTime");
        String endTime = datajson.getString("endTime");
        if(startTime.length()!=19||endTime.length()!=19){
            throw new UnifiedBusinessException("传入的开始时间或结束时间格式不合法！！仅支持yyyy-MM-dd HH:mm:ss格式的时间！！");
        }
        reqBean.setEdaNo(edaNo);
        reqBean.setStartTime(startTime);
        reqBean.setEndTime(endTime);
        return reqBean;
    }


    /**
     * 获取重新计算的请求bean
     * @param reqJson
     * @return
     */
    public static BkcRecalReqBean analysisRecalReqBean(String reqJson) {
        BkcRecalReqBean reqBean  =  new BkcRecalReqBean();
        if(StringUtils.isEmpty(reqJson)){
            throw new UnifiedBusinessException("请求参数全为空！！");
        }
        JSONObject datajson = new JSONObject(reqJson).getJSONObject("data");//
        String edaNo = datajson.getString("edaNo");
        String calDate = datajson.optString("calDate","");
        reqBean.setEdaNo(edaNo);
        reqBean.setCalDate(calDate);
        return reqBean;
    }
    /**
     * 获取重新计算的日期字符串
     * @param reqJson
     * @return
     */
    public static String analysisLinkLocation(String reqJson) {
        String param = "1";
        if(StringUtils.isNotEmpty(reqJson)){
            JSONObject dataJson = new JSONObject(reqJson).getJSONObject("data");
            if (dataJson.has("linkLocation")) {
                param = dataJson.optString("linkLocation","1");
            }
        }
        return param;
    }

    /**
     * 获取重新计算的日期字符串
     * @param reqJson
     * @return
     */
    public static Calendar analysisEveryDayTime(String reqJson) {
        String param = "";
        if(StringUtils.isNotEmpty(reqJson)){
            param = new JSONObject(reqJson).getJSONObject("data").optString("calDate","");
        }
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH,-1);
        if(StringUtils.isNotEmpty(param)){
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            try {
                calendar.setTime(sdf.parse(param));
            } catch (ParseException e) {
                log.error("将日期"+param+"转换成格式yyyyMMdd异常！！！"+e.getMessage());
            }
        }
        return calendar;
    }

    /**
     * 构建返回结果
     * @param executeTimeStr
     * @param edaNo
     * @param startTimeZoneStr
     * @param endTimeZoneStr
     * @param list
     * @return
     */
    public static String createRecalBkcRspJson(String executeTimeStr, String edaNo, String startTimeZoneStr, String endTimeZoneStr, List<BkcDetectRspBean> list) {
        JSONObject returnJson = new JSONObject();
        returnJson.put("reInfo","请求成功！！");
        returnJson.put("reCode","0");
        JSONObject dataJson = new JSONObject();
        returnJson.put("data",dataJson);
        dataJson.put("edaNo",edaNo);
        dataJson.put("startTime",startTimeZoneStr);
        dataJson.put("endTime",endTimeZoneStr);
        dataJson.put("executeTime",executeTimeStr);
        JSONArray detailArray = new JSONArray();
        dataJson.put("detailList",detailArray);
        long totalValue = 0;
        for (BkcDetectRspBean each:list) {
            JSONObject eachJson = new JSONObject();
            detailArray.put(eachJson);
            //使用减法策略
            if("1".equals(each.getConfBean().getCalType())){
                totalValue-=each.getExecuteRsValue();
            }else{
                //否则都是减法策略
                totalValue+=each.getExecuteRsValue();
            }
            eachJson.put("executeRsValue",each.getExecuteRsValue());
            eachJson.put("executeIndexNames",each.getExecuteIndexNames());
            eachJson.put("executeLang",new JSONObject(each.getExecuteLang()));
        }
        dataJson.put("totalValue",totalValue);
        return returnJson.toString();
    }

    /**
     * 构建结果的检测bean
     * @param task
     * @return
     */
    public static BkcDetectRspBean getDetectBkcRspBean(BkcEdaConfBean task) {
        BkcDetectRspBean  detectRspBean = new BkcDetectRspBean();
        detectRspBean.setConfBean(task);
        detectRspBean.setExecuteLang(task.getQueryLang());
        detectRspBean.setExecuteRsValue(task.getCalRsValue());
        detectRspBean.setExecuteIndexNames(task.getQueryRealIndexName());
        return detectRspBean;
    }

    /**
     * 构建覆盖率返回结果
     * @param executeTimeStr
     * @param reqBean
     * @param bkList
     * @param ftList
     * @return
     */
    public static TBurypointsCoverRate createDetectedCoverRateRspJson(String executeTimeStr, BkcDetectReqBean reqBean, List<BkcDetectRspBean> bkList, List<FrtDetectRspBean> bkftList
            , List<FrtDetectRspBean> bkftBranchList, List<FrtDetectRspBean> bkftConfigIdList, List<FrtDetectRspBean> ftList
            , List<BkcEdaConfBean> ftConfigList, List<FrtDetectRspBean> ftBranchList, List<TBurypointsEda> edaBranchList, String linkLocation) {
        TBurypointsCoverRate tBurypointsCoverRate = new TBurypointsCoverRate();
        String ftExecuteSql = buildDataphinExecuteSql(reqBean, linkLocation, "1");
        tBurypointsCoverRate.setEdaNo(reqBean.getEdaNo());
        tBurypointsCoverRate.setStartTime(reqBean.getStartTime());
        tBurypointsCoverRate.setEndTime(reqBean.getEndTime());
        tBurypointsCoverRate.setFtExecuteSql(ftExecuteSql.replaceAll("\\\n"," "));
        tBurypointsCoverRate.setExecuteTime(executeTimeStr);
        tBurypointsCoverRate.setBkValue("0");
        tBurypointsCoverRate.setFtValue("0");
        tBurypointsCoverRate.setCoverRate("0%");
        //前端埋点
        if(ftList!=null&&!ftList.isEmpty()){
            JSONArray frontJsonArray = new JSONArray();
            long totalFtPv = 0;
            for (FrtDetectRspBean each:ftList) {
                totalFtPv+=each.getFtPv();
                JSONObject eachFrontJson = new JSONObject();
                frontJsonArray.put(eachFrontJson);
                eachFrontJson.put("eachValue",String.valueOf(each.getFtPv()));
                eachFrontJson.put("eachEdaNo",each.getEdaNo());
            }
            if (frontJsonArray.length() > 0) {
                tBurypointsCoverRate.setFrontList(frontJsonArray.toString());
            }
            tBurypointsCoverRate.setFtValue(String.valueOf(totalFtPv));
        }
        //覆盖率各分支pv
        Map<String, Long> coverRatePv = new HashMap<>();
        //覆盖率
        JSONArray bkJsonArray = new JSONArray();
        if((bkList!=null && !bkList.isEmpty()) || (bkftList!=null && !bkftList.isEmpty())){
            long totalBkPv = 0;
            //后端覆盖率
            for (BkcDetectRspBean each : bkList) {
                //定义当前分支的总pv
                long totalBkBranchPv = 0;
                if (coverRatePv.containsKey(each.getConfBean().getEdaBranch())) {
                    totalBkBranchPv = coverRatePv.get(each.getConfBean().getEdaBranch());
                }
                //使用减法策略
                if("1".equals(each.getConfBean().getCalType())){
                    totalBkPv-=each.getExecuteRsValue();
                    totalBkBranchPv-=each.getExecuteRsValue();
                }else{
                    //否则都是减法策略
                    totalBkPv+=each.getExecuteRsValue();
                    totalBkBranchPv+=each.getExecuteRsValue();
                }
                JSONObject eachBackJson = new JSONObject();
                bkJsonArray.put(eachBackJson);
                eachBackJson.put("configId",String.valueOf(each.getConfBean().getBurypointsEdaBkConfigId()));
                eachBackJson.put("eachValue",String.valueOf(each.getExecuteRsValue()));
                eachBackJson.put("eachInfo",each.getConfBean().getCalRsInfo());
                eachBackJson.put("eachDsl",new JSONObject(each.getExecuteLang()));
                eachBackJson.put("configType", "1");
                eachBackJson.put("edaBranch", StringUtils.trimToEmpty(each.getConfBean().getEdaBranch()));
                //计算完的分支总pv放回map
                coverRatePv.put(each.getConfBean().getEdaBranch(), totalBkBranchPv);
            }
            //前端覆盖率PV汇总
            for (FrtDetectRspBean each : bkftList) {
                totalBkPv+=each.getFtPv();
            }
            //分支PV中前端覆盖率部分的计算
            for (FrtDetectRspBean each : bkftBranchList) {
                //定义当前分支的总pv
                long totalBkBranchPv = 0;
                if (coverRatePv.containsKey(each.getEdaBranch())) {
                    totalBkBranchPv = coverRatePv.get(each.getEdaBranch());
                }
                totalBkBranchPv+=each.getFtPv();
                //计算完的分支总pv放回map
                coverRatePv.put(each.getEdaBranch(), totalBkBranchPv);
            }
            //前端覆盖率明细
            for (BkcEdaConfBean each : ftConfigList) {
                JSONObject eachBackJson = new JSONObject();
                bkJsonArray.put(eachBackJson);
                eachBackJson.put("configId",String.valueOf(each.getBurypointsEdaBkConfigId()));
                eachBackJson.put("eleResourceId", each.getEleResourceId());
                eachBackJson.put("eleExtField", each.getEleExtField());
                eachBackJson.put("eleExtFieldVal", each.getEleExtFieldVal());
                eachBackJson.put("eachValue", "");
                eachBackJson.put("eachInfo","未查询到");
                //查询前端覆盖率执行结果的pv
                for (FrtDetectRspBean frtDetectRspBean : bkftConfigIdList) {
                    if (StringUtils.equals(String.valueOf(each.getBurypointsEdaBkConfigId()), frtDetectRspBean.getConfigId())) {
                        eachBackJson.put("eachValue", String.valueOf(frtDetectRspBean.getFtPv()));
                        eachBackJson.put("eachInfo","查询成功！！");
                        break;
                    }
                }
                //组装对应的sql
                reqBean.setFillSql("and c.burypoints_eda_ft_config_id = " + each.getBurypointsEdaBkConfigId());
                eachBackJson.put("ftExecuteSql", BkcMakerToolkits.buildCoverRateDataphinExecuteSql(reqBean, "3"));
                //组装完清除参数
                reqBean.setFillSql("");
                eachBackJson.put("configType", "2");
                eachBackJson.put("edaBranch", StringUtils.trimToEmpty(each.getEdaBranch()));
            }
            
            if (bkJsonArray.length() > 0) {
                tBurypointsCoverRate.setBackList(bkJsonArray.toString());
            }
            tBurypointsCoverRate.setBkValue(String.valueOf(totalBkPv));
        }
        //分支探测结果
        JSONArray branchListArray = new JSONArray();
        for (TBurypointsEda tBurypointsEda : edaBranchList) {
            JSONObject branchJson = new JSONObject();
            branchListArray.put(branchJson);
            branchJson.put("edaBranch", tBurypointsEda.getEdaNo());
            branchJson.put("edaBranchName", tBurypointsEda.getEdaName());
            branchJson.put("bkValue", coverRatePv.containsKey(tBurypointsEda.getEdaNo()) ? coverRatePv.get(tBurypointsEda.getEdaNo()) : "");
            branchJson.put("ftValue", "0");
            for (FrtDetectRspBean frtDetectRspBean : ftBranchList) {
                if (StringUtils.equals(frtDetectRspBean.getEdaBranch(), tBurypointsEda.getEdaNo())) {
                    branchJson.put("ftValue", String.valueOf(frtDetectRspBean.getFtPv()));
                    break;
                }
            }
            branchJson.put("coverRate", StringUtils.defaultIfBlank(transCoverRate(String.valueOf(branchJson.get("ftValue")),
                    String.valueOf(branchJson.get("bkValue"))), "未配置覆盖率"));
            //组装对应的sql
            reqBean.setFillSql("and c.eda_branch = '" + tBurypointsEda.getEdaNo() + "'");
            branchJson.put("ftExecuteSql", BkcMakerToolkits.buildDataphinExecuteSql(reqBean, linkLocation, "2"));
            //组装完清除参数
            reqBean.setFillSql("");
            //处理当前分支下的覆盖率数据
            JSONArray backListJsonArray = new JSONArray();
            branchJson.put("backList", backListJsonArray);
            for (int i = 0; i < bkJsonArray.length(); i++) {
                JSONObject eachBackJson = bkJsonArray.getJSONObject(i);
                //找到同一个分支的数据
                if (StringUtils.equals(tBurypointsEda.getEdaNo(), eachBackJson.getString("edaBranch"))) {
                    backListJsonArray.put(eachBackJson);
                }
            }
        }
        tBurypointsCoverRate.setBranchList(branchListArray.toString());


        long ftValue = Long.parseLong(tBurypointsCoverRate.getFtValue());
        long bkValue = Long.parseLong(tBurypointsCoverRate.getBkValue());
        if(ftValue!=0&&bkValue!=0){
            //保留1位有效数字的四舍五入的百分比结果
            tBurypointsCoverRate.setCoverRate(transCoverRate(tBurypointsCoverRate.getFtValue(), tBurypointsCoverRate.getBkValue()));
        }
        return tBurypointsCoverRate;
    }

    /**
     * 计算覆盖率
     * @param ftValue
     * @param bkValue
     * @return
     */
    private static String transCoverRate(String ftValue, String bkValue) {
        if (StringUtils.isBlank(ftValue)
                || StringUtils.isBlank(bkValue)) {
            return "";
        }
        if (new BigDecimal("0").compareTo(new BigDecimal(bkValue)) == 0) {
            return "0%";
        }
        return new BigDecimal(ftValue).divide(new BigDecimal(bkValue), 3, BigDecimal.ROUND_HALF_UP)
                .multiply(new BigDecimal(100)).setScale(1, BigDecimal.ROUND_HALF_UP)+"%";
    }

    /**
     * 构建dataphin执行的sql
     * 汇总前端埋点数据
     * @param reqBean
     * @param linkLocation
     * @param type 生成sql的类型(1-按eda分组  2-按eda分支分组)
     * @return
     */
    public static String buildDataphinExecuteSql(BkcDetectReqBean reqBean, String linkLocation, String type) {
        String startDs = reqBean.getStartTime().substring(0,10).replaceAll("-","");
        String endDs = reqBean.getEndTime().substring(0,10).replaceAll("-","");
        reqBean.setStartDs(startDs);
        reqBean.setEndDs(endDs);
        String apolloExecuteSql;
        switch(linkLocation) {
            case "1" :
                apolloExecuteSql = ConfigService.getAppConfig().getProperty(StringUtils.equals("2", type) ? "execute.branch.dataphin.sql" : "execute.dataphin.sql","");
                break;
            case "2" :
                apolloExecuteSql = ConfigService.getAppConfig().getProperty(StringUtils.equals("2", type) ? "execute.branch.dataphin.sql.out" : "execute.dataphin.sql.out","");
                break;
            default:
                throw new UnifiedBusinessException("链路位置不合法");

        }
        return analysisAndPuddedExecuteSql(apolloExecuteSql,reqBean);
    }

    /**
     * 前端覆盖率中前端埋点数据
     * @param reqBean
     * @param type 颗粒度(1-eda级  2-分支级  3-单个覆盖率级)
     * @return
     */
    public static String buildCoverRateDataphinExecuteSql(BkcDetectReqBean reqBean, String type) {
        String startDs = reqBean.getStartTime().substring(0,10).replaceAll("-","");
        String endDs = reqBean.getEndTime().substring(0,10).replaceAll("-","");
        reqBean.setStartDs(startDs);
        reqBean.setEndDs(endDs);
        String apolloExecuteSql;
        switch(type) {
            case "1" :
                apolloExecuteSql = ConfigService.getAppConfig().getProperty("execute.coverRate.dataphin.sql","");
                break;
            case "2" :
                apolloExecuteSql = ConfigService.getAppConfig().getProperty("execute.coverRate.edaBranch.dataphin.sql","");
                break;
            case "3" :
                apolloExecuteSql = ConfigService.getAppConfig().getProperty("execute.coverRate.configId.dataphin.sql","");
                break;
            case "4" :
                apolloExecuteSql = ConfigService.getAppConfig().getProperty("execute.coverRate.configId.dataphin.batch.sql","");
                break;
            default:
                throw new UnifiedBusinessException("颗粒度不合法");

        }
        return analysisAndPuddedExecuteSql(apolloExecuteSql,reqBean);
    }

    public static String buildDetectedDataphinExecuteSql(BkcDetectReqBean reqBean, int type) {
        String startDs = reqBean.getStartTime().substring(0,10).replaceAll("-","");
        String endDs = reqBean.getEndTime().substring(0,10).replaceAll("-","");
        reqBean.setStartDs(startDs);
        reqBean.setEndDs(endDs);
        reqBean.setBizDate(DateUtil.format(new Date(),"yyyyMMdd"));
        String apolloExecuteSql;
        switch(type) {
            //废弃
            //case 1 ://分子埋点查询
            //    apolloExecuteSql = ConfigService.getConfig("execute.dataphin.sql").getProperty("execute.detected.resource.dataphin.sql","");
            //    break;
            case 2 ://分子埋点按节点查询
                apolloExecuteSql = ConfigService.getConfig("execute.dataphin.sql").getProperty("execute.node.detected.resource.dataphin.sql","");
                break;
            case 3 ://分母前端覆盖率查询
                apolloExecuteSql = ConfigService.getConfig("execute.dataphin.sql").getProperty("execute.front.detected.resource.dataphin.sql","");
                break;
            case 4 ://分子埋点按配置记录查询
                apolloExecuteSql = ConfigService.getConfig("execute.dataphin.sql").getProperty("execute.apoints.detected.resource.dataphin.sql","");
                break;
            default:
                throw new UnifiedBusinessException("颗粒度不合法");

        }
        if(StringUtils.equals(reqBean.getEnv(),"PRO")){
            apolloExecuteSql = apolloExecuteSql.replaceAll("log56_ods_dev.b_brd_t_brd_fronts_burypoints", "log56_ods.b_brd_t_brd_fronts_burypoints");
        }
        return analysisAndPuddedExecuteSql(apolloExecuteSql,reqBean);
    }

    /**
     * 解析要执行的sql
     * @param apolloExecuteSql
     * @param reqBean
     * @return
     */
    private static String analysisAndPuddedExecuteSql(String apolloExecuteSql, BkcDetectReqBean reqBean) {
        /**
         * 解析要执行的sql
         */
        if(StringUtils.isEmpty(apolloExecuteSql)){
            throw new UnifiedBusinessException("要执行查询的dataphin sql不存在");
        }
        //解析出字符串中${xxx}的字段出来
        Pattern pattern = Pattern.compile("\\$\\{(.*?)\\}");
        Matcher matcher = pattern.matcher(apolloExecuteSql);
        StringBuffer sb = new StringBuffer();
        Map<String,Object> fieldMap = createFieldMap(reqBean);
        while(matcher.find()) {
            // 获取匹配到的变量名
            String variable = matcher.group(1);
            // 查找变量名对应的值
            Object replacement = fieldMap.get(variable);
            if (replacement == null) {
                // 如果没有找到对应的值，使用默认值
                throw new UnifiedBusinessException("解析sql中字段"+variable+"失败！！字段对于的值是null");
            }
            // 替换找到的匹配项
            matcher.appendReplacement(sb, String.valueOf(replacement));
        }
        matcher.appendTail(sb);
        return sb.toString().replaceAll("\\\n"," ").replaceAll("\\\t"," ");
    }

    /**
     * 将fieldBean的内容取值出来
     * @param reqBean
     * @return
     */
    private static Map<String,Object> createFieldMap(BkcDetectReqBean reqBean)  {
        Map<String,Object> fieldMap = new HashMap<>();
        // 获取BkcDetectReqBean类的所有字段
        Field[] fields = BkcDetectReqBean.class.getDeclaredFields();
        for (Field field : fields) {
            // 设置字段可访问，以便获取私有字段的值
            field.setAccessible(true);
            // 获取字段的值
            Object value = null;
            try {
                value = field.get(reqBean);
            } catch (IllegalAccessException e) {
                throw new UnifiedBusinessException("从BkcDetectReqBean中获取属性"+field.getName()+"异常！！"+e.getMessage(),e);
            }
            // 将字段名和值放入map
            fieldMap.put(field.getName(), value);
        }
        return fieldMap;
    }

    /**
     * 构建查询结果
     * @param list
     * @param record
     */
    public static void buildQueryDataphinRs(List<FrtDetectRspBean> list, Record record) {
        Object oedaNo=record.get("edano");
        Object edaBranch=record.get("edabranch");
        Object configId=record.get("configid");
        Object oftCount=record.get("ftcount");
        FrtDetectRspBean  rsp = new FrtDetectRspBean();
        if(oedaNo!=null){
            if(oedaNo instanceof  byte[]){
                rsp.setEdaNo(new String((byte[])oedaNo));
            }else{
                rsp.setEdaNo(String.valueOf(oedaNo));
            }
        }
        if(edaBranch!=null){
            if(edaBranch instanceof  byte[]){
                rsp.setEdaBranch(new String((byte[])edaBranch));
            }else{
                rsp.setEdaBranch(String.valueOf(edaBranch));
            }
        }
        if(configId!=null){
            if(configId instanceof  byte[]){
                rsp.setConfigId(new String((byte[])configId));
            }else{
                rsp.setConfigId(String.valueOf(configId));
            }
        }
        if(oftCount!=null&&StringUtils.isNotEmpty(String.valueOf(oftCount))){
            rsp.setFtPv(Long.parseLong(String.valueOf(oftCount)));
        }
        list.add(rsp);
    }

    public static void buildQueryDetetcedRs(List<FrtDetectRspBean> list, Record record) {
        Object optLinkId=record.get("opt_link_id");
        Object edaMxcellId=record.get("eda_mxcell_id");
        Object pv=record.get("pv");
        Object uv=record.get("uv");
        FrtDetectRspBean  rsp = new FrtDetectRspBean();
        if(optLinkId!=null){
            if(optLinkId instanceof  byte[]){
                rsp.setOptLinkId(new String((byte[])optLinkId));
            }else{
                rsp.setOptLinkId(String.valueOf(optLinkId));
            }
        }
        if(edaMxcellId!=null){
            if(edaMxcellId instanceof  byte[]){
                rsp.setEdaMxcellId(new String((byte[])edaMxcellId));
            }else{
                rsp.setEdaMxcellId(String.valueOf(edaMxcellId));
            }
        }

        if(pv!=null&&StringUtils.isNotEmpty(String.valueOf(pv))){
            rsp.setPv(Long.parseLong(String.valueOf(pv)));
        }

        if(uv!=null&&StringUtils.isNotEmpty(String.valueOf(uv))){
            rsp.setUv(Long.parseLong(String.valueOf(uv)));
        }
        list.add(rsp);
    }

    public static void buildQueryResourceRs(List<FrtDetectRspBean> list, Record record, int resultType) {
        Object optLinkId=record.get("opt_link_id");
        Object edaMxcellId=record.get("eda_mxcell_id");
        Object pv=record.get("pv");
        FrtDetectRspBean  rsp = new FrtDetectRspBean();
        if(optLinkId!=null){
            if(optLinkId instanceof  byte[]){
                rsp.setOptLinkId(new String((byte[])optLinkId));
            }else{
                rsp.setOptLinkId(String.valueOf(optLinkId));
            }
        }
        if(edaMxcellId!=null){
            if(edaMxcellId instanceof  byte[]){
                rsp.setEdaMxcellId(new String((byte[])edaMxcellId));
            }else{
                rsp.setEdaMxcellId(String.valueOf(edaMxcellId));
            }
        }
        if(pv!=null&&StringUtils.isNotEmpty(String.valueOf(pv))){
            rsp.setPv(Long.parseLong(String.valueOf(pv)));
        }

        if (resultType == 2 || resultType == 4) {
            Object uv=record.get("uv");
            if(uv!=null&&StringUtils.isNotEmpty(String.valueOf(uv))){
                rsp.setUv(Long.parseLong(String.valueOf(uv)));
            }
        }

        //特殊的数据
        if (4 == resultType) {
            Object burypointsEdaPointsId=record.get("burypoints_eda_points_id");

            if(burypointsEdaPointsId!=null){
                if(burypointsEdaPointsId instanceof  byte[]){
                    rsp.setBurypointsEdaPointsId(new String((byte[])burypointsEdaPointsId));
                }else{
                    rsp.setBurypointsEdaPointsId(String.valueOf(burypointsEdaPointsId));
                }
            }

        }

        list.add(rsp);
    }
}
