package com.wtyt.lgfesentryedamanagement.bkc.utils;

import com.ctrip.framework.apollo.ConfigService;
import com.wtyt.lgfesentryedamanagement.bkc.bean.BkcEdaConfBean;
import com.wtyt.lgfesentryedamanagement.bkc.bean.BkcInterfaceFilterBean;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.JsonToolkit;
import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.support.IndicesOptions;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * 关于es查询的操作类
 */
public class BkcEsToolkits {


    private static final Logger log = LoggerFactory.getLogger(BkcEsToolkits.class);


    /**
     * 通过关系类型构建过滤条件
     * @param boolQueryBuilder
     * @param eachFilter
     */
    public static void puddedBoolQueryBuilder(BoolQueryBuilder boolQueryBuilder, BkcInterfaceFilterBean eachFilter) {
        String relType = eachFilter.getRelType();
        String fieldName = eachFilter.getFilterKey();
        //尝试变成数据，因为是多选
        List<String> fieldValues = eachFilter.getFilterValue();
        if((BkcEsConstants.IS_QUERY.equals(relType)|| StringUtils.isEmpty(relType))){
            checkFilterValueEmpty(relType, fieldName, fieldValues);
            //构建es查询is的语法
            buildFieldIsMacthQuery(boolQueryBuilder,fieldName,fieldValues);
        }else if(BkcEsConstants.IS_NOT_QUERY.equals(relType)){
            checkFilterValueEmpty(relType, fieldName, fieldValues);
            //构建es查询is not的语法
            buildFieldIsNotMacthQuery(boolQueryBuilder,fieldName,fieldValues);
        }else if(BkcEsConstants.IS_ONE_OF_QUERY.equals(relType)){
            checkFilterValueEmpty(relType, fieldName, fieldValues);
            //构建es查询is one of的语法
            buildFieldIsOneOfMacthQuery(boolQueryBuilder,fieldName,fieldValues);
        }else if(BkcEsConstants.IS_NOT_ONE_OF_QUERY.equals(relType)){
            checkFilterValueEmpty(relType, fieldName, fieldValues);
            //构建es查询is not one of的语法
            buildFieldIsNotOneOfMacthQuery(boolQueryBuilder,fieldName,fieldValues);
        }else if(BkcEsConstants.EXISTS_QUERY.equals(relType)){
            //构建es查询exists的语法
            buildFieldExistsMacthQuery(boolQueryBuilder,fieldName);
        }else if(BkcEsConstants.NOT_EXISTS_QUERY.equals(relType)){
            //构建es查询 not exists的语法
            buildFieldNotExistsMacthQuery(boolQueryBuilder,fieldName);
        }else if(BkcEsConstants.WILDCARD_QUERY.equals(relType)){
            checkFilterValueEmpty(relType, fieldName, fieldValues);
            //构建es查询 wildcard的语法
            buildFieldWildcardQuery(boolQueryBuilder,fieldName,fieldValues);
        }else if(BkcEsConstants.NOT_WILDCARD_QUERY.equals(relType)){
            checkFilterValueEmpty(relType, fieldName, fieldValues);
            //构建es查询not wildcard的语法
            buildFieldNotWildcardQuery(boolQueryBuilder,fieldName,fieldValues);
        }else{
            throw new UnifiedBusinessException("不支持的关系查询类型！！"+relType);
        }

    }

    /**
     * 校验参数必填
     */
    private static void checkFilterValueEmpty(String relType, String fieldName, List<String> fieldValues) {
        if (CollectionUtils.isEmpty(fieldValues)) {
            throw new UnifiedBusinessException(String.format("关系查询类型%s 条件%s的值不可为空", relType, fieldName));
        }
    }

    /**
     * 构建es not wildcard的查询语法
     * @param boolQueryBuilder
     * @param fieldName
     * @param fieldValues
     */
    private static void buildFieldNotWildcardQuery(BoolQueryBuilder boolQueryBuilder, String fieldName, List<String> fieldValues) {
        BoolQueryBuilder shouldBoolQuery = QueryBuilders.boolQuery();
        for(String keyword:fieldValues){
            shouldBoolQuery.should(QueryBuilders.wildcardQuery(fieldName,keyword));
        }
        shouldBoolQuery.minimumShouldMatch(1);//至少匹配一个
        boolQueryBuilder.mustNot(shouldBoolQuery);
    }


    /**
     * 构建es wildcard的查询语法
     * @param boolQueryBuilder
     * @param fieldName
     * @param fieldValues
     */
    private static void buildFieldWildcardQuery(BoolQueryBuilder boolQueryBuilder, String fieldName, List<String> fieldValues) {
        BoolQueryBuilder shouldBoolQuery = QueryBuilders.boolQuery();
        for(String keyword:fieldValues){
            shouldBoolQuery.should(QueryBuilders.wildcardQuery(fieldName,keyword));
        }
        shouldBoolQuery.minimumShouldMatch(1);//至少匹配一个
        boolQueryBuilder.must(shouldBoolQuery);
    }

    /**
     * 尝试将filterValue转换成数组，多选
     * @param filterValue
     * @return
     */
    public static List<String> tryGetFieldValues(String filterValue) {
        List<String>  list = new ArrayList<>();
        if(JsonToolkit.isJsonArrayObject(filterValue)){
            try{
                JSONArray array = new JSONArray(filterValue);
                for(int i=0;i<array.length();i++){
                    Object eachValue = array.get(i);
                    if(null!=eachValue){
                        String eachArrayValue = String.valueOf(eachValue);
                        if(StringUtils.isNotEmpty(eachArrayValue)){
                            list.add(eachArrayValue);
                        }
                    }
                }
            }catch (Exception e){
                log.error("将"+filterValue+"转换成jsonArray发生异常！！"+e.getMessage());
                list.add(filterValue);
            }
        }else{
            list.add(filterValue);
        }
        return list;
    }

    /**
     * 构建es查询 not exists的语法
     * @param boolQueryBuilder
     */
    private static void buildFieldNotExistsMacthQuery(BoolQueryBuilder boolQueryBuilder, String fieldName ) {
        boolQueryBuilder.mustNot(QueryBuilders.existsQuery(fieldName));
    }

    /**
     * 构建es查询exists的语法
     * @param boolQueryBuilder
     */
    private static void buildFieldExistsMacthQuery(BoolQueryBuilder boolQueryBuilder, String fieldName) {
        boolQueryBuilder.must(QueryBuilders.existsQuery(fieldName));
    }

    /**
     * 构建es查询is not one of 的语法
     * @param boolQueryBuilder
     */
    private static void buildFieldIsNotOneOfMacthQuery(BoolQueryBuilder boolQueryBuilder, String fieldName,List<String> fieldValues) {
        BoolQueryBuilder shouldBoolQuery = QueryBuilders.boolQuery();
        for(String keyword:fieldValues){
            shouldBoolQuery.should(QueryBuilders.matchPhraseQuery(fieldName,keyword));
        }
        shouldBoolQuery.minimumShouldMatch(1);//至少匹配一个
        boolQueryBuilder.mustNot(shouldBoolQuery);
    }

    /**
     * 构建es查询is one of 的语法
     * @param boolQueryBuilder
     */
    private static void buildFieldIsOneOfMacthQuery(BoolQueryBuilder boolQueryBuilder, String fieldName,List<String> fieldValues) {
        BoolQueryBuilder shouldBoolQuery = QueryBuilders.boolQuery();
        for(String keyword:fieldValues){
            shouldBoolQuery.should(QueryBuilders.matchPhraseQuery(fieldName,keyword));
        }
        shouldBoolQuery.minimumShouldMatch(1);//至少匹配一个
        boolQueryBuilder.must(shouldBoolQuery);
    }

    /**
     * 构建es查询is not 的语法
     * @param boolQueryBuilder
     */
    private static void buildFieldIsNotMacthQuery(BoolQueryBuilder boolQueryBuilder, String fieldName,List<String> fieldValues) {
        for(String keyword:fieldValues){
            boolQueryBuilder.mustNot(QueryBuilders.matchPhraseQuery(fieldName,keyword));
        }
    }

    /**
     * 构建es查询is 的语法
     * @param boolQueryBuilder     *
     */
    private static void buildFieldIsMacthQuery(BoolQueryBuilder boolQueryBuilder, String fieldName,List<String> fieldValues) {
        for(String keyword:fieldValues){
            boolQueryBuilder.must(QueryBuilders.matchPhraseQuery(fieldName,keyword));
        }
    }

    /**
     * 从日志全埋里面获取
     * @param esClient
     * @param startZoneDateStr
     * @param endZoneDateStr
     * @param task
     */
    public static void queryBkcEsValueFromLogEs(RestHighLevelClient esClient, String startZoneDateStr, String endZoneDateStr, BkcEdaConfBean task, boolean isPopError) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.rangeQuery("@timestamp").gte(startZoneDateStr).lte(endZoneDateStr).includeUpper(false).timeZone("+08:00"))
                .must(QueryBuilders.termQuery("fields.project_name.keyword", task.getInterfaceProjectName()));
        //包含关键词的条件
        if(task.getLogFiltersKeywords()!=null){
            String keywords[] = task.getLogFiltersKeywords().split(",");
            for(String eachKeyWord:keywords){
                if(StringUtils.isNotEmpty(eachKeyWord)){
                    boolQueryBuilder.must(QueryBuilders.matchPhraseQuery("message",eachKeyWord));
                }
            }
        }
        //排除关键词的条件
        if(task.getLogFiltersExcludeKeywords()!=null){
            String excludeKeywords[] = task.getLogFiltersExcludeKeywords().split(",");
            for(String eachExcludeKeyWord:excludeKeywords){
                if(StringUtils.isNotEmpty(eachExcludeKeyWord)){
                    boolQueryBuilder.mustNot(QueryBuilders.matchPhraseQuery("message",eachExcludeKeyWord));
                }
            }
        }
        CountRequest countRequest = new CountRequest(task.getEsIndexName()).indicesOptions(IndicesOptions.lenientExpandOpen());
        BoolQueryBuilder filterQuery = QueryBuilders.boolQuery().filter(boolQueryBuilder);
        countRequest.query(filterQuery);
        CountResponse countResponse = null;
        try {
            task.setQueryRealIndexName(task.getEsIndexName());
            task.setQueryLang(filterQuery.toString());
            countResponse = esClient.count(countRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            task.setCalRsInfo("error:"+e.getMessage());
            log.error("执行es 查询索引 "+task.getEsIndexName()+","+task.getBurypointsEdaBkConfigId()+"异常！！！",e);
            if (isPopError) {
                throw new UnifiedBusinessException(String.format("执行es 查询索引[%s],覆盖率[%s]异常！！！内部错误[%s]", task.getEsIndexName(), task.getBurypointsEdaBkConfigId(), e.getMessage()),e);
            }
        }
        task.setCalRsValue(countResponse==null?0:countResponse.getCount());
    }


    /**
     * 从接口全埋里面获取
     * @param esClient
     * @param startZoneDateStr
     * @param endZoneDateStr
     * @param task
     * @param isPopError
     */
    public static void queryBkcEsValueFromInterfaceEs(RestHighLevelClient esClient, String startZoneDateStr, String endZoneDateStr, BkcEdaConfBean task, boolean isPopError, int tryCount) {
        //标记执行次数
        tryCount++;
        log.info(String.format("执行es 查询索引[%s],覆盖率[%s],第[%s]次执行", task.getInterfaceIndexName(), task.getConfigId(), tryCount));
        String indexAliseName = task.getInterfaceIndexName();
        if(StringUtils.isEmpty(indexAliseName)){
            task.setCalRsInfo("error:当前接口查询对应的索引名称不存在！！");
            task.setQueryLang("{}");
            return;
        }
        String interfaceFilters = task.getInterfaceFilters();
        if(StringUtils.isEmpty(interfaceFilters)){
            task.setCalRsInfo("error:当前接口查询对应的索引关键词不存在！！");
            task.setQueryLang("{}");
            return;
        }
        //如果返回结果是空，代表是不合法的配置信息
        List<BkcInterfaceFilterBean> filterList = createInterfaceFilterBeanList(task,interfaceFilters);
        if(filterList.isEmpty()){
            return;
        }
        CountResponse countResponse = null;
        try {
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                    .must(QueryBuilders.rangeQuery("timestamp").gte(startZoneDateStr).lte(endZoneDateStr).includeUpper(false).timeZone("+08:00"));
            for(BkcInterfaceFilterBean eachFilter:filterList){
                puddedBoolQueryBuilder(boolQueryBuilder,eachFilter);
            }
            String interfaceIndexNameStr = ConfigService.getAppConfig().getProperty("eda.es.interfaceIndexName", "");
            List<String> indices = new ArrayList<>();
            indices.add(task.getInterfaceIndexName());
            if (StringUtils.isNotBlank(interfaceIndexNameStr)) {
                for (String interfaceIndexName : interfaceIndexNameStr.split(",")) {
                    if (StringUtils.isNotBlank(interfaceIndexName)) {
                        indices.add(interfaceIndexName);
                    }
                }
            }
            BoolQueryBuilder filterQuery = QueryBuilders.boolQuery().filter(boolQueryBuilder);
            CountRequest countRequest = new CountRequest(indices.toArray(new String[indices.size()]), filterQuery).indicesOptions(IndicesOptions.lenientExpandOpen());
            task.setQueryRealIndexName(task.getInterfaceIndexName());
            task.setQueryLang(filterQuery.toString());
            countResponse = esClient.count(countRequest, RequestOptions.DEFAULT);
            task.setCalRsInfo("查询成功！！");
            task.setCalRsValue(countResponse==null?0:countResponse.getCount());
        } catch (Exception e) {
            task.setCalRsInfo("error:"+e.getMessage());
            log.error("执行es 查询索引 "+task.getInterfaceIndexName()+","+task.getBurypointsEdaBkConfigId()+"异常！！！",e);
            if (isPopError) {
                throw new UnifiedBusinessException(String.format("执行es 查询索引[%s],覆盖率[%s]异常！！！内部错误[%s]", task.getInterfaceIndexName(), task.getConfigId(), e.getMessage()),e);
            } else {
                //如果是异常不阻断进程的，则进行尝试3次
                if (tryCount < 3) {
                    queryBkcEsValueFromInterfaceEs(esClient, startZoneDateStr, endZoneDateStr, task, isPopError, tryCount);
                } else {
                    //异常且不再重试时，设置默认值
                    task.setCalRsValue(countResponse==null?0:countResponse.getCount());
                }
            }
        }
    }


    /**
     * 构建filter请求bean
     * @param task
     * @param interfaceFilters
     * @return
     */
    private static List<BkcInterfaceFilterBean> createInterfaceFilterBeanList(BkcEdaConfBean task, String interfaceFilters) {
        List<BkcInterfaceFilterBean>  filterList = new ArrayList<>();        try{
            JSONArray  filterArray = new JSONArray(interfaceFilters);
            for(int i=0;i<filterArray.length();i++){
                JSONObject eachJson = filterArray.getJSONObject(i);
                BkcInterfaceFilterBean  filterBean = new BkcInterfaceFilterBean();
                filterBean.setFilterKey(eachJson.getString("fkey"));
                filterBean.setFilterValue(BkcEsToolkits.tryGetFieldValues(eachJson.optString("fvalue","")));
                if(eachJson.has("frel")){
                    filterBean.setRelType(eachJson.getString("frel"));//获取对应的计算关系
                }
                filterList.add(filterBean);
            }
            if(filterList.size()==0){
                task.setCalRsInfo("error:当前配置的接口过滤信息不存在！！");
                task.setQueryLang("{}");
            }
        }catch (Exception e){
            log.error("当前索引查询任务"+task.getBurypointsEdaBkConfigId()+"对应的关键词转换成json异常！！"+e.getMessage());
            task.setCalRsInfo("error:接口索引关键词转换成jsonarray异常"+e.getMessage());
            task.setQueryLang("{}");
        }
        return filterList;
    }
}
