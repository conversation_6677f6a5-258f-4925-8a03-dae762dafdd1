package com.wtyt.lgfesentryedamanagement.bkc.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.aliyun.odps.Instance;
import com.aliyun.odps.Odps;
import com.aliyun.odps.data.Record;
import com.aliyun.odps.task.SQLTask;
import com.aliyun.odps.tunnel.InstanceTunnel;
import com.aliyun.odps.tunnel.io.TunnelRecordReader;
import com.ctrip.framework.apollo.ConfigService;
import com.google.common.collect.Lists;
import com.wtyt.generator.toolkit.UidToolkit;
import com.wtyt.lg.commons.exception.UnifiedBusinessException;
import com.wtyt.lg.commons.toolkits.AlarmToolkit;
import com.wtyt.lgfesentryedamanagement.bkc.bean.*;
import com.wtyt.lgfesentryedamanagement.bkc.bean.param.Req5545009IBean;
import com.wtyt.lgfesentryedamanagement.bkc.bean.param.Req5545031IBean;
import com.wtyt.lgfesentryedamanagement.bkc.bean.response.Req5545020OBean;
import com.wtyt.lgfesentryedamanagement.bkc.bean.response.Req5545032OBean;
import com.wtyt.lgfesentryedamanagement.bkc.mapper.BkcCalMapper;
import com.wtyt.lgfesentryedamanagement.bkc.utils.BkcEsToolkits;
import com.wtyt.lgfesentryedamanagement.bkc.utils.BkcMakerToolkits;
import com.wtyt.lgfesentryedamanagement.dao.bean.*;
import com.wtyt.lgfesentryedamanagement.dao.mapper.*;
import com.wtyt.lgfesentryedamanagement.ecc.service.BurypointsEdaBkConfigService;
import com.wtyt.lgfesentryedamanagement.pub.bean.BaseTokenBean;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.JsonToolkit;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.ResultToolkit;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.SqlParamToolkit;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.client.RestHighLevelClient;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class BkcCalServiceImpl {

    private static final Logger log = LoggerFactory.getLogger(BkcCalServiceImpl.class);

    @Autowired
    private BkcCalMapper mapper;
    @Autowired
    private TBurypointsEdaMasterMapper tBurypointsEdaMasterMapper;
    @Autowired
    private TBurypointsEdaMapper tBurypointsEdaMapper;
    @Autowired
    private TBurypointsEdaBkConfigMapper tBurypointsEdaBkConfigMapper;
    @Autowired
    private TBurypointsEdaCoverBkMapper tBurypointsEdaCoverBkMapper;
    @Autowired
    private TBurypointsEdaCoverFtMapper tBurypointsEdaCoverFtMapper;
    @Autowired
    private TBurypointsCoverRateMapper tBurypointsCoverRateMapper;
    @Autowired
    private TBurypointsEdaErrorMapper tBurypointsEdaErrorMapper;
    @Autowired
    private TBuryPointsEdaPointsMapper tBuryPointsEdaPointsMapper;

    @Resource(name = "logEsClient")
    private RestHighLevelClient logRestHighLevelClient;

    @Resource(name = "calBkcRateThreadPool")
    private ThreadPoolExecutor threadPool;

    @Resource(name = "commonThreadPool")
    private ThreadPoolExecutor commonThreadPool;

    //这个后续废弃掉，已经不用了
    @Resource(name = "interfaceEsClient")
    private RestHighLevelClient interfaceRestHighLevelClient;
    //测试环境ES
    @Resource(name = "interfaceEsFatClient")
    private RestHighLevelClient interfaceRestHighLevelFatClient;
    //生产环境ES
    @Resource(name = "interfaceEsProClient")
    private RestHighLevelClient interfaceRestHighLevelProClient;

    @Autowired
    private BurypointsEdaBkConfigService burypointsEdaBkConfigService;

    @Autowired
    private Odps  odps;

    /**
     * 获取某天的eda覆盖率信息
     * @param data
     * @return
     */
    public void getDetectedCoverRate(Req5545009IBean data) {
        BkcDetectReqBean reqBean = new BkcDetectReqBean();
        if (StringUtils.isAnyBlank(data.getEdaNo(), data.getEndTime(), data.getStartTime(), data.getOptUserId())) {
            throw new UnifiedBusinessException("EDA编号、起始终止时间、操作用户信息都不可为空");
        }
        if(data.getStartTime().length()!=19 || data.getEndTime().length()!=19){
            throw new UnifiedBusinessException("传入的开始时间或结束时间格式不合法！！仅支持yyyy-MM-dd HH:mm:ss格式的时间！！");
        }
        if (StringUtils.isNotBlank(data.getLinkLocation()) && !StringUtils.equalsAny(data.getLinkLocation(), "0", "1", "2")) {
            throw new UnifiedBusinessException("链路位置不合法");
        }
        TBurypointsEdaMaster tBurypointsEdaMaster = tBurypointsEdaMasterMapper.selectByEdaNo(data.getEdaNo());
        if (null == tBurypointsEdaMaster) {
            throw new UnifiedBusinessException("操作链路/功能点不存在");
        }
        reqBean.setEdaNo(data.getEdaNo());
        reqBean.setStartTime(data.getStartTime());
        reqBean.setEndTime(data.getEndTime());
        List<BkcEdaConfBean> edaNoConfList = mapper.selectEdaNoConfList(reqBean.getEdaNo());
        //入口覆盖率配置
        List<BkcEdaConfBean> innerList = new ArrayList<>();
        //出口覆盖率配置
        List<BkcEdaConfBean> outList = new ArrayList<>();
        edaNoConfList.stream().forEach(item->{
            if (StringUtils.equals("1", item.getLinkLocation())) {
                innerList.add(item);
            } else if (StringUtils.equals("2", item.getLinkLocation())) {
                outList.add(item);
            }
        });
        //定义提示未配置的链路位置
        List<String> errorList = new ArrayList<>();
        if(CollectionUtils.isEmpty(innerList) && StringUtils.equalsAny(StringUtils.defaultIfEmpty(data.getLinkLocation(), "1"), "0", "1")){
            errorList.add("入口");
        }
        if(CollectionUtils.isEmpty(outList) && StringUtils.equalsAny(StringUtils.defaultIfEmpty(data.getLinkLocation(), "1"), "0", "2")){
            errorList.add("出口");
        }
        if (0 == tBurypointsEdaMaster.getEdaType() && CollectionUtils.isNotEmpty(errorList)) {
            throw new UnifiedBusinessException(String.format("【操作链路】%s 的 %s 接口覆盖率未配置", tBurypointsEdaMaster.getEdaName(), String.join("/", errorList)));
        }
        //异步计算数据
        String executeTimeStr = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        if (StringUtils.equalsAny(StringUtils.defaultIfEmpty(data.getLinkLocation(), "1"), "0", "1")) {
            //入口数据
            executeCoverRate(innerList, data, executeTimeStr, reqBean, "1");
        }
        if (StringUtils.equalsAny(StringUtils.defaultIfEmpty(data.getLinkLocation(), "1"), "0", "2")) {
            //出口数据
            executeCoverRate(outList, data, executeTimeStr, reqBean, "2");
        }

    }

    public void toDetected(Req5545031IBean data) {
        if (StringUtils.isAnyBlank(data.getOptLinkId(), data.getEndTime(), data.getStartTime(), data.getEnv()) || CollectionUtils.isEmpty(data.getSearchItem())) {
            throw new UnifiedBusinessException("操作链路ID、起始终止时间、查询环境、查询项都不可为空");
        }
        if(data.getStartTime().length()!=19 || data.getEndTime().length()!=19){
            throw new UnifiedBusinessException("传入的开始时间或结束时间格式不合法！！仅支持yyyy-MM-dd HH:mm:ss格式的时间！！");
        }
        //尝试补充eda名称
        TBurypointsEdaMaster tBurypointsEdaMaster = tBurypointsEdaMasterMapper.selectByOptLinkId(data.getOptLinkId());
        if (null == tBurypointsEdaMaster) {
            throw new UnifiedBusinessException("操作链路/功能点不存在");
        }
        //查询时间
        String executeTimeStr = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        //插入一条探测记录
        TBurypointsDetect tdDetect = new TBurypointsDetect();
        tdDetect.setBurypointsDetectionId(UidToolkit.generateUidString());
        tdDetect.setEdaName(null == tBurypointsEdaMaster ? null : tBurypointsEdaMaster.getEdaName());
        tdDetect.setStartTime(data.getStartTime());
        tdDetect.setEndTime(data.getEndTime());
        tdDetect.setOptLinkId(data.getOptLinkId());
        tdDetect.setExecuteTime(executeTimeStr);
        tdDetect.setEnv(data.getEnv());
        tdDetect.setSearchItemStr(StringUtils.join(data.getSearchItem(),","));
        tdDetect.setSearchState("0");
        tBurypointsCoverRateMapper.insertDetected(tdDetect);
        //异步查询数据，并更新查询结果
        commonThreadPool.execute(() -> {
            //定义一个唯一序列号，用于追踪日志
            String batchId = UidToolkit.generateUidString();
            try {
                //
                BkcDetectReqBean reqBean =  new BkcDetectReqBean();
                BeanUtils.copyProperties(tdDetect, reqBean);
                /*****PVUV&覆盖率异常计算*****/
                List<TBurypointsEdaError> edaExpList =  new ArrayList<TBurypointsEdaError>();
                /*****PVUV处理---开始******/

                /*****按节点查询分子埋点   主要是统计每个节点汇总的pvuv******/
                List<FrtDetectRspBean> fenziCoverList = excuCoverSql(reqBean, batchId, 2);
                /*****按配置查询分子埋点  主要填充具体同一个节点下， 每个埋点配置的pv是多少******/
                List<FrtDetectRspBean> configFenziList = excuCoverSql(reqBean, batchId, 4);
                //补充埋点配置信息
                transApointsInfo(configFenziList);
                //计算漏斗数据
                calculationFunelData(tdDetect,fenziCoverList);
                List<FrtDetectRspBean> mxcellDataList = new ArrayList<>();
                mxcellDataList.addAll(fenziCoverList);
                //组装详情返回接口
                for(FrtDetectRspBean rspData : mxcellDataList){
                    List<FrtDetectRspBean> resourceIdList = new ArrayList<>();
                    for(FrtDetectRspBean rspResource : configFenziList){
                        if(StringUtils.equals(rspData.getEdaMxcellId(),rspResource.getEdaMxcellId())){
                            resourceIdList.add(rspResource);
                        }
                    }
                    rspData.setResourceIdList(resourceIdList);
                }
                tdDetect.setSearchData(JSONUtil.toJsonStr(mxcellDataList));
                /*****PVUV处理---结束******/
                if (data.getSearchItem().contains("覆盖率")) {
                    String dataId = new SimpleDateFormat("yyyyMMdd").format(new Date());
                    /*****分母数据处理---开始******/
                    //前端覆盖率结果列表
                    List<FrtDetectRspBean> ftCoverList = excuCoverSql(reqBean, batchId, 3);
                    //覆盖率分母查询结果列表
                    List<BkDetectRspBean> coverResultList = new ArrayList<>();
                    //查询后端覆盖率的配置
                    List<BkcEdaConfBean> bkConfigList = tBurypointsEdaCoverBkMapper.queryBkConfigList(reqBean.getOptLinkId());
                    String startTimeZoneStr = reqBean.getStartTime().replaceFirst(" ","T")+".000";
                    String endTimeZoneStr = reqBean.getEndTime().replaceFirst(" ","T")+".000";
                    List<BkcDetectRspBean> bkList = CollectionUtils.isEmpty(bkConfigList) ? new ArrayList<>() : getDetectedCoverBackRspByEnv(startTimeZoneStr, endTimeZoneStr, bkConfigList, reqBean.getEnv());
                    //先以前端覆盖率生成一份节点列表
                    for (FrtDetectRspBean frtDetectRspBean : ftCoverList) {
                        BkDetectRspBean temp = new BkDetectRspBean();
                        coverResultList.add(temp);
                        temp.setOptLinkId(reqBean.getOptLinkId());
                        temp.setEdaMxcellId(frtDetectRspBean.getEdaMxcellId());
                        temp.setFtCoverList(frtDetectRspBean.getResourceIdList());
                        temp.setFtpv(frtDetectRspBean.getPv());
                    }
                    //再通过后端覆盖率补充节点列表
                    boolean exists = false;//标记是否已存在
                    for (BkcDetectRspBean bkcDetectRspBean : bkList) {
                        exists = false;
                        for (BkDetectRspBean bkDetectRspBean : coverResultList) {
                            if (StringUtils.equals(bkcDetectRspBean.getConfBean().getEdaMxcellId(), bkDetectRspBean.getEdaMxcellId())) {
                                exists = true;
                                if (CollectionUtils.isEmpty(bkDetectRspBean.getBkCoverList())) {
                                    bkDetectRspBean.setBkCoverList(new ArrayList<>());
                                }
                                bkDetectRspBean.getBkCoverList().add(bkcDetectRspBean);
                                bkDetectRspBean.setBkpv(bkDetectRspBean.getBkpv() + bkcDetectRspBean.getExecuteRsValue());
                            }
                        }
                        //不存在时，补充一条节点数据
                        if (!exists) {
                            BkDetectRspBean temp = new BkDetectRspBean();
                            coverResultList.add(temp);
                            temp.setOptLinkId(reqBean.getOptLinkId());
                            temp.setEdaMxcellId(bkcDetectRspBean.getConfBean().getEdaMxcellId());
                            temp.setBkCoverList(new ArrayList<>());
                            temp.getBkCoverList().add(bkcDetectRspBean);
                            temp.setBkpv(bkcDetectRspBean.getExecuteRsValue());
                        }
                    }
                    TBurypointsEdaError expBean = null;
                    //计算覆盖率
                    for (BkDetectRspBean bkDetectRspBean : coverResultList) {
                        bkDetectRspBean.setFenmu(bkDetectRspBean.getFtpv() + bkDetectRspBean.getBkpv());
                        for (FrtDetectRspBean frtDetectRspBean : fenziCoverList) {
                            if (StringUtils.equals(bkDetectRspBean.getEdaMxcellId(), frtDetectRspBean.getEdaMxcellId())) {
                                bkDetectRspBean.setFenzi(bkDetectRspBean.getFenzi() + frtDetectRspBean.getPv());
                            }
                        }
                        //如果分子分母有一个为0，则覆盖率为0
                        if (bkDetectRspBean.getFenzi() == 0 || bkDetectRspBean.getFenmu() == 0) {
                            bkDetectRspBean.setCoverRate("0");
                        } else {
                            bkDetectRspBean.setCoverRate(new BigDecimal(String.valueOf(bkDetectRspBean.getFenzi())).divide(new BigDecimal(String.valueOf(bkDetectRspBean.getFenmu())), 2, BigDecimal.ROUND_HALF_UP).toPlainString());
                        }
                        //新增覆盖率异常检测
                        int minCoverRagePv = Integer.parseInt(ConfigService.getAppConfig().getProperty("bp.coverrate.minpv","50"));
                        if(bkDetectRspBean.getFenzi()>minCoverRagePv){
                            //1.分子pv小于50，不考核
                            double coverRate = Double.parseDouble(bkDetectRspBean.getCoverRate());
                            if(!(coverRate >= 0.95 && coverRate <= 1.05)){
                                expBean = new TBurypointsEdaError();
                                edaExpList.add(expBean);
                                expBean.setExpType(3);
                                expBean.setOptLinkId(reqBean.getOptLinkId());
                                expBean.setExpText(bkDetectRspBean.getFenmu() == 0?"当前EDA元素节点（"+bkDetectRspBean.getEdaMxcellId()+"）分子大于"+minCoverRagePv+",分母不能为零":"当前EDA元素节点（"+bkDetectRspBean.getEdaMxcellId()+"）分子大于50,埋点覆盖率("+coverRate+")不在[0.95,1.05]范围内");
                                expBean.setDataId(dataId);
                                expBean.setEdaMxcellId(bkDetectRspBean.getEdaMxcellId());
                                expBean.setFromType(1);
                                expBean.setIsAlarm(0);
                                expBean.setDetectionId(tdDetect.getBurypointsDetectionId());
                                expBean.setDrawioFemErrorId(UidToolkit.generateUidString());
                            }
                        }
                    }
                    tdDetect.setCoverData(JSONUtil.toJsonStr(coverResultList));
                    //记录异常
//                    log.info("ToDetect方法,edaExpList="+edaExpList.size());
                    if(edaExpList.size()>0){
                        tBurypointsEdaErrorMapper.batchInsertEdaPointsError(edaExpList);
                    }
                }
                /*****分母数据处理---结束******/
                tdDetect.setSearchState("1");
            } catch (Exception e) {
                log.error("EDA异步探测出现异常", e);
                AlarmToolkit.logAlarm("EDA异步探测出现异常", e, "EDA异步探测出现异常");
                tdDetect.setSearchState("2");
                String searchFailInfo = StringUtils.defaultIfBlank(e.getMessage(), "EDA覆盖率异步探测出现异常");
                tdDetect.setSearchFailInfo(searchFailInfo.substring(0, searchFailInfo.length() > 256 ? 256 : searchFailInfo.length()));

            }finally {
                tBurypointsCoverRateMapper.updateDetected(tdDetect);
            }
        });
    }

    /**
     * 补充埋点配置信息
     * @param configFenziList
     */
    private void transApointsInfo(List<FrtDetectRspBean> configFenziList) {
        //补充埋点配置信息
        if (CollectionUtils.isNotEmpty(configFenziList)) {
            List<String> burypointsEdaPointsIdList = configFenziList.stream().filter(e->StringUtils.isNotBlank(e.getBurypointsEdaPointsId())).map(FrtDetectRspBean::getBurypointsEdaPointsId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(burypointsEdaPointsIdList)) {
                List<List<String>> burypointsEdaPointsIdSplitList = SqlParamToolkit.splitList(burypointsEdaPointsIdList, 200);
                List<TBuryPointsEdaPoints> coverFtList = new ArrayList<>();
                for (List<String> strings : burypointsEdaPointsIdSplitList) {
                    coverFtList.addAll(tBuryPointsEdaPointsMapper.queryList(strings));
                }
                for (FrtDetectRspBean frtDetectRspBean : configFenziList) {
                    for (TBuryPointsEdaPoints tBuryPointsEdaPoints : coverFtList) {
                        if (StringUtils.equals(frtDetectRspBean.getBurypointsEdaPointsId(), String.valueOf(tBuryPointsEdaPoints.getBurypointsEdaPointsId()))) {
                            frtDetectRspBean.setAppTag(tBuryPointsEdaPoints.getAppTag());
                            frtDetectRspBean.setEleExtJson(tBuryPointsEdaPoints.getEleExtJson());
                            frtDetectRspBean.setEleColumnJson(tBuryPointsEdaPoints.getEleColumnJson());
                            frtDetectRspBean.setResourceId(tBuryPointsEdaPoints.getEleResourceId());
                            break;
                        }
                    }
                }
            }
        }
    }


    //计算pvuv漏斗数据
    private void calculationFunelData(TBurypointsDetect tdDetect,List<FrtDetectRspBean> fenziCoverList){
        //开启线程处理
        commonThreadPool.execute(() -> {
            try {
                log.info("开始计算漏斗数据, detectionId={}", tdDetect.getBurypointsDetectionId());
                String dataId = new SimpleDateFormat("yyyyMMdd").format(new Date());
                // TODO: 在这里添加漏斗计算逻辑
                if (CollectionUtils.isEmpty(fenziCoverList)) {
                    log.info("fenziCoverList为空，无需计算漏斗数据");
                    return;
                }

                //查询当前所有EDApoints数据
                List<TBuryPointsEdaPoints> edaPointList = tBuryPointsEdaPointsMapper.selectBpMxcellIdByOptLinkId(tdDetect.getOptLinkId());
                Map<String, FrtDetectRspBean> fenziCoverMap = fenziCoverList.stream().collect(Collectors.toMap(FrtDetectRspBean::getEdaMxcellId,Function.identity(),(o1,o2)->o1));
                // 处理完成后更新数据
                String[] bpIds = null;
                FrtDetectRspBean ftBean = null;
                TBurypointsEdaError  expBean= null;
                List<TBurypointsEdaError> edaExpList =  new ArrayList<TBurypointsEdaError>();
                long totalBpIds = 0;
                long curMxellIdPv = 0;
                 for(TBuryPointsEdaPoints bean :edaPointList){
                     totalBpIds = 0;
                     ftBean = fenziCoverMap.get(bean.getEdaMxcellId());
//                     log.info("ajin====> ftBeancur.getPv()"+ftBean.getPv());
                     if(ftBean!=null && ftBean.getPv()>0 && !StringUtils.isEmpty(bean.getBpParentMxcellIds())){
//                         log.info("ajin====>bean.getEdaMxcellId():"+bean.getEdaMxcellId());
                         curMxellIdPv = ftBean.getPv();
                         bpIds = Arrays.stream(bean.getBpParentMxcellIds().split(",")).distinct().filter(s -> !s.isEmpty()).toArray(String[]::new);
                         //累加求和
                         for(String bpid:bpIds){
                             ftBean = fenziCoverMap.get(bpid);
//                             log.info("ajin====> ftBean.getPv():"+ ftBean.getPv());
                             if(ftBean!=null){
                                 totalBpIds = totalBpIds + ftBean.getPv();
                             }
                         }
//                         log.info("ajin====>totalBpIds:"+totalBpIds);
                        // 当前节点类型为0-按钮，若当前节点pv>0 且上级节点pv = 0 ，视为异常,其他情况则不视为异常（按钮可能被点击0-n次）
                        // 当前节点不是按钮，按照漏斗模型计算异常
                        boolean curMxellIsButton = java.util.Objects.equals(bean.getEdaMxcellType(), "0");
                        //  log.info("ajin====>curMxellIsButton:"+curMxellIsButton+"cellId:"+bean.getEdaMxcellId()+"cellType:"+bean.getEdaMxcellType());
                        boolean isContinue = !curMxellIsButton || (curMxellIsButton && totalBpIds == 0 && curMxellIdPv > 0);
                         //上一级pv>0
                         if(isContinue && totalBpIds < curMxellIdPv){
                             //当前节点大于上一级所有节点pv，视为异常
                             expBean = new TBurypointsEdaError();
                             edaExpList.add(expBean);
                             expBean.setExpType(3);
                             expBean.setOptLinkId(tdDetect.getOptLinkId());
                             expBean.setExpText("当前EDA元素节点（"+bean.getEdaMxcellId()+"）漏斗数据异常,它不能大于上一级EDA元素节点（"+bean.getBpParentMxcellIds()+"）的PV数总和");
                             expBean.setDataId(dataId);
                             expBean.setEdaMxcellId(bean.getEdaMxcellId());
                             expBean.setFromType(1);
                             expBean.setIsAlarm(0);
                             expBean.setDetectionId(tdDetect.getBurypointsDetectionId());
                             expBean.setDrawioFemErrorId(UidToolkit.generateUidString());
                         }
                     }
                 }

                 //记录异常
                 if(edaExpList.size()>0){
                     log.info("ajin====>pvuv异常");
                    tBurypointsEdaErrorMapper.batchInsertEdaPointsError(edaExpList);
                 }
                 log.info("漏斗数据计算完成, detectionId={}", tdDetect.getBurypointsDetectionId());
            } catch (Exception e) {
                log.error("计算漏斗数据异常, detectionId=" + tdDetect.getBurypointsDetectionId(), e);
                AlarmToolkit.logAlarm("计算漏斗数据异常", e, "计算漏斗数据异常");
            }
        });
    }

    /**
     * 前端覆盖率查询
     * @param reqBean
     * @param batchId
     * @return
     */
    private List<FrtDetectRspBean> excuCoverSql(BkcDetectReqBean reqBean, String batchId, int type) {
        //查询节点pv
        String executeSql = BkcMakerToolkits.buildDetectedDataphinExecuteSql(reqBean, type);
        log.info("要执行的sql：{}",executeSql);
        List<FrtDetectRspBean> mxcellList = getDetectedRsp(executeSql, batchId, type);

        Map<String, List<FrtDetectRspBean>> mxcellIdMap = mxcellList.stream().collect(Collectors.groupingBy(FrtDetectRspBean::getEdaMxcellId));
        List<FrtDetectRspBean> mxcellDataList = new ArrayList<>();
        //按节点整理数据
        mxcellIdMap.forEach((k, v) -> {
            FrtDetectRspBean frtDetectRspBean = new FrtDetectRspBean();
            frtDetectRspBean.setEdaMxcellId(k);
            Long pvSum = v.stream().map(FrtDetectRspBean::getPv).reduce((a, b) -> a + b).orElseGet(() -> 0L);
            frtDetectRspBean.setPv(pvSum);
            mxcellDataList.add(frtDetectRspBean);
        });
        //每个节点内，组装节点的每个埋点配置数据
        //组装详情返回接口
        for(FrtDetectRspBean rspData : mxcellDataList){
            List<FrtDetectRspBean> resourceIdList = new ArrayList<>();
            for(FrtDetectRspBean rspResource : mxcellList){
                if(StringUtils.equals(rspData.getEdaMxcellId(),rspResource.getEdaMxcellId())){
                    resourceIdList.add(rspResource);
                }
            }
            rspData.setResourceIdList(resourceIdList);
        }
        return mxcellList;
    }

    /**
     * 覆盖率计算
     * @param edaNoConfList
     * @param data
     * @param executeTimeStr
     * @param reqBean
     * @param linkLocation
     */
    private void executeCoverRate(List<BkcEdaConfBean> edaNoConfList, Req5545009IBean data, String executeTimeStr, BkcDetectReqBean reqBean, String linkLocation) {
        //把ConfList按前后端覆盖率配置进行拆分，需要分别处理
        List<BkcEdaConfBean> bkConfigList = new ArrayList<>();
        List<BkcEdaConfBean> ftConfigList = new ArrayList<>();
        for (BkcEdaConfBean bkcEdaConfBean : edaNoConfList) {
            if (StringUtils.equals("1", bkcEdaConfBean.getConfigType())) {
                bkConfigList.add(bkcEdaConfBean);
            } else if (StringUtils.equals("2", bkcEdaConfBean.getConfigType())) {
                ftConfigList.add(bkcEdaConfBean);
            }
        }
        //先插入一条查询记录
        String burypointsCoverRateId = UidToolkit.generateUidString();
        TBurypointsCoverRate coverRateBean = new TBurypointsCoverRate();
        coverRateBean.setBurypointsCoverRateId(burypointsCoverRateId);
        coverRateBean.setEdaNo(data.getEdaNo());
        coverRateBean.setStartTime(data.getStartTime());
        coverRateBean.setEndTime(data.getEndTime());
        coverRateBean.setExecuteTime(executeTimeStr);
        coverRateBean.setLinkLocation(linkLocation);
        coverRateBean.setSearchState("0");
        coverRateBean.setOptUserId(data.getOptUserId());
        coverRateBean.setOptUserName(data.getOptUserName());
        tBurypointsCoverRateMapper.insertCoverRate(coverRateBean);
        //异步查询数据，并更新查询结果
        commonThreadPool.execute(() -> {
            //定义一个唯一序列号，用于追踪日志
            String batchId = UidToolkit.generateUidString();
            try {
                BkcDetectReqBean localReqBean = new BkcDetectReqBean();
                BeanUtils.copyProperties(reqBean, localReqBean);
                String startTimeZoneStr = localReqBean.getStartTime().replaceFirst(" ","T")+".000";
                String endTimeZoneStr = localReqBean.getEndTime().replaceFirst(" ","T")+".000";
                localReqBean.setLinkLocation(linkLocation);
                //后端覆盖率数据
                List<BkcDetectRspBean> bkList = CollectionUtils.isEmpty(bkConfigList) ? new ArrayList<>() : getDetectedCoverBackRsp(startTimeZoneStr,endTimeZoneStr,bkConfigList);
                //前端覆盖率数据
                List<FrtDetectRspBean> bkftList = getDetectedCoverFrontRsp(BkcMakerToolkits.buildCoverRateDataphinExecuteSql(localReqBean, "1"), batchId);
                //前端覆盖率按分支分组数据
                List<FrtDetectRspBean> bkftBranchList = getDetectedCoverFrontRsp(BkcMakerToolkits.buildCoverRateDataphinExecuteSql(localReqBean, "2"), batchId);
                //前端覆盖率按configId分组数据
                List<FrtDetectRspBean> bkftConfigIdList = getDetectedCoverFrontRsp(BkcMakerToolkits.buildCoverRateDataphinExecuteSql(localReqBean, "3"), batchId);
                //前端埋点按分支分组数据
                List<FrtDetectRspBean> ftBranchList = getDetectedCoverFrontRsp(BkcMakerToolkits.buildDataphinExecuteSql(localReqBean, linkLocation, "2"), batchId);
                //前端埋点数据
                List<FrtDetectRspBean> ftList = getDetectedCoverFrontRsp(BkcMakerToolkits.buildDataphinExecuteSql(localReqBean, linkLocation, "1"), batchId);
                //查询eda的所有分支
                List<TBurypointsEda> edaBranchList = tBurypointsEdaMapper.queryByEdaParentNo(data.getEdaNo(), "0");
                TBurypointsCoverRate tBurypointsCoverRate = BkcMakerToolkits.createDetectedCoverRateRspJson(executeTimeStr,localReqBean,bkList, bkftList, bkftBranchList, bkftConfigIdList,ftList, ftConfigList, ftBranchList, edaBranchList, linkLocation);
                tBurypointsCoverRate.setSearchState("1");
                tBurypointsCoverRate.setBurypointsCoverRateId(burypointsCoverRateId);
                tBurypointsCoverRateMapper.updateCoverRate(tBurypointsCoverRate);
            } catch (Exception e) {
                log.error("EDA覆盖率异步探测出现异常", e);
                AlarmToolkit.logAlarm("EDA覆盖率异步探测出现异常", e, "EDA覆盖率异步探测出现异常");
                TBurypointsCoverRate tBurypointsCoverRate = new TBurypointsCoverRate();
                tBurypointsCoverRate.setSearchState("2");
                String searchFailInfo = StringUtils.defaultIfBlank(e.getMessage(), "EDA覆盖率异步探测出现异常");
                tBurypointsCoverRate.setSearchFailInfo(searchFailInfo.substring(0, searchFailInfo.length() > 128 ? 128 : searchFailInfo.length()));
                tBurypointsCoverRate.setBurypointsCoverRateId(burypointsCoverRateId);
                tBurypointsCoverRateMapper.updateCoverRate(tBurypointsCoverRate);
            }
        });
    }

    /**
     * EDA后端覆盖率查询记录列表
     * @param data
     * @return
     */
    public Req5545020OBean getColverRateList(BaseTokenBean data) throws IOException {
        if (StringUtils.isBlank(data.getOptUserId())) {
            throw new UnifiedBusinessException("操作用户信息都不可为空");
        }
       List<TBurypointsCoverRate> coverRateList = tBurypointsCoverRateMapper.queryCoverRateList(data.getOptUserId());
        Req5545020OBean req5545020OBean = new Req5545020OBean();
        List<Req5545020OBean> edaList = new ArrayList<>();
        req5545020OBean.setEdaList(edaList);
        //组装返回信息
        for (TBurypointsCoverRate tBurypointsCoverRate : coverRateList) {
            Req5545020OBean eda = new Req5545020OBean();
            BeanUtils.copyProperties(tBurypointsCoverRate, eda);
            edaList.add(eda);
            if (StringUtils.isNotBlank(tBurypointsCoverRate.getBackList())) {
                List<Req5545020OBean> backList = JSONUtil.toList(tBurypointsCoverRate.getBackList(), Req5545020OBean.class);
                eda.setBackList(backList);
                //补充interfaceFilters
                transBackList(backList);
            }
            if (StringUtils.isNotBlank(tBurypointsCoverRate.getFrontList())) {
                List<Req5545020OBean> frontList = JSONUtil.toList(tBurypointsCoverRate.getFrontList(), Req5545020OBean.class);
                eda.setFrontList(frontList);
            }
            if (StringUtils.isNotBlank(tBurypointsCoverRate.getBranchList())) {
                List<Req5545020OBean> branchList = JSONUtil.toList(tBurypointsCoverRate.getBranchList(), Req5545020OBean.class);
                for (Req5545020OBean branch : branchList) {
                    if (CollectionUtils.isNotEmpty(branch.getBackList())) {
                        transBackList(branch.getBackList());
                    }
                }
                eda.setBranchList(branchList);
            }
        }
        return req5545020OBean;
    }

    /**
     * 特殊数据的补充
     * @param backList
     */
    private void transBackList(List<Req5545020OBean> backList) {
        if (CollectionUtils.isNotEmpty(backList)) {
            List<String> configIdList = backList.stream().filter(e->StringUtils.isNotBlank(e.getConfigId())).map(Req5545020OBean::getConfigId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(configIdList)) {
                //批量查询数据
                List<TBurypointsEdaBkConfig> edaBkConfigList = tBurypointsEdaBkConfigMapper.queryByIdList(configIdList.stream().map(Long::parseLong).collect(Collectors.toList()), "0");
                //比对数据，补充interfaceFilters
                for (TBurypointsEdaBkConfig tBurypointsEdaBkConfig : edaBkConfigList) {
                    for (Req5545020OBean oBean : backList) {
                        if (StringUtils.equals(String.valueOf(tBurypointsEdaBkConfig.getBurypointsEdaBkConfigId()), oBean.getConfigId())) {
                            if (StringUtils.equals("1", tBurypointsEdaBkConfig.getConfigType())) {
                                oBean.setInterfaceFilters(burypointsEdaBkConfigService.esFilterTagHandle(tBurypointsEdaBkConfig.getInterfaceFilters()));
                            } else if (StringUtils.equals("2", tBurypointsEdaBkConfig.getConfigType())) {
                                oBean.setEleResourceId(tBurypointsEdaBkConfig.getEleResourceId());
                                oBean.setEleExtField(tBurypointsEdaBkConfig.getEleExtField());
                                oBean.setEleExtFieldVal(tBurypointsEdaBkConfig.getEleExtFieldVal());
                            }
                            break;
                        }
                    }
                }
            }
        }
    }

    /**
     * 前端dataphin埋点的记录
     * @param executeSql
     * @return
     */
    private List<FrtDetectRspBean> getDetectedCoverFrontRsp(String executeSql, String batchId) {
        log.info("{}开始执行dataphin查询", batchId);
        log.info("{}执行dataphin语句=========={}", batchId, executeSql);
        List<FrtDetectRspBean>  list = new ArrayList<>();
        try {
            Instance instance = SQLTask.run(odps, executeSql);
            log.info("{}获取instance实例信息=========={}", batchId, JSONUtil.toJsonStr(instance));
            instance.waitForSuccess();
            // 创建 InstanceTunnel
            InstanceTunnel tunnel = new InstanceTunnel(odps);
            // 根据 instance id，创建 DownloadSession
            InstanceTunnel.DownloadSession session = tunnel.createDownloadSession(odps.getDefaultProject(), instance.getId());
            long count = session.getRecordCount();
            if(count>0){
                // 获取数据的写法与 TableTunnel 一样
                TunnelRecordReader reader = session. openRecordReader (0, count);
                Record record;
                while ((record = reader.read()) != null) {
                    BkcMakerToolkits.buildQueryDataphinRs(list,record);
                }
                reader.close();
            }
        } catch (Exception e) {
            log.error("从dataphin查询结果异常！！"+e.getMessage()+"("+executeSql+")",e);
            throw new UnifiedBusinessException("从dataphin查询结果异常！！"+e.getMessage(),e);
        }
        log.info("{}结束执行dataphin查询", batchId);
        return list;
    }



    /**
     * 前端dataphin埋点的记录---指定优先级
     * 优先级 (注：公共云环境此参数无效) 优先级的取值去见为[0, 9]的整型值，数字越大，优先级越低。 默认值为9。
     *
     * @param executeSql
     * @return
     */
    private List<FrtDetectRspBean> getDetectedCoverFrontRsp(String executeSql,Integer priority) {
        List<FrtDetectRspBean>  list = new ArrayList<>();
        try {
            if(null==priority){
                priority = 9;
            }
            Instance instance = SQLTask.run(odps,odps.getDefaultProject(), executeSql,"AnonymousSQLTask",null,null,priority);
            instance.waitForSuccess();
            // 创建 InstanceTunnel
            InstanceTunnel tunnel = new InstanceTunnel(odps);
            // 根据 instance id，创建 DownloadSession
            InstanceTunnel.DownloadSession session = tunnel.createDownloadSession(odps.getDefaultProject(), instance.getId());
            long count = session.getRecordCount();
            if(count>0){
                // 获取数据的写法与 TableTunnel 一样
                TunnelRecordReader reader = session. openRecordReader (0, count);
                Record record;
                while ((record = reader.read()) != null) {
                    BkcMakerToolkits.buildQueryDataphinRs(list,record);
                }
                reader.close();
            }
        } catch (Exception e) {
            log.error("从dataphin查询结果异常！！"+e.getMessage()+"("+executeSql+")",e);
            throw new UnifiedBusinessException("从dataphin查询结果异常！！"+e.getMessage(),e);
        }
        return list;
    }
    /**
     * 后端结果的返回结果
     * @param startTimeZoneStr
     * @param endTimeZoneStr
     * @param edaNoConfList
     * @return
     */
    private List<BkcDetectRspBean> getDetectedCoverBackRsp(String startTimeZoneStr, String endTimeZoneStr,List<BkcEdaConfBean> edaNoConfList) {
        List<BkcDetectRspBean> list = new ArrayList<>();
        for (BkcEdaConfBean task:edaNoConfList){
            if("0".equals(task.getFilterType())){
                //isPopError直接抛出异常
                BkcEsToolkits.queryBkcEsValueFromLogEs(logRestHighLevelClient,startTimeZoneStr,endTimeZoneStr,task, true);
            }else{
                BkcEsToolkits.queryBkcEsValueFromInterfaceEs(interfaceRestHighLevelClient,startTimeZoneStr,endTimeZoneStr,task, true, 0);
            }
            //加入返回结果
            list.add(BkcMakerToolkits.getDetectBkcRspBean(task));
        }
        return list;
    }

    /**
     * 后端结果的返回结果
     * @param startTimeZoneStr
     * @param endTimeZoneStr
     * @param edaNoConfList
     * @return
     */
    private List<BkcDetectRspBean> getDetectedCoverBackRspByEnv(String startTimeZoneStr, String endTimeZoneStr,List<BkcEdaConfBean> edaNoConfList, String env) {
        List<BkcDetectRspBean> list = new ArrayList<>();
        for (BkcEdaConfBean task:edaNoConfList){
            if("0".equals(task.getFilterType())){
                //这个分支废弃了--没有查日志的情况了
                //isPopError直接抛出异常
                BkcEsToolkits.queryBkcEsValueFromLogEs(logRestHighLevelClient,startTimeZoneStr,endTimeZoneStr,task, true);
            }else{
                if (StringUtils.equals("PRO", env)) {
                    BkcEsToolkits.queryBkcEsValueFromInterfaceEs(interfaceRestHighLevelProClient,startTimeZoneStr,endTimeZoneStr,task, true, 0);
                } else {
                    BkcEsToolkits.queryBkcEsValueFromInterfaceEs(interfaceRestHighLevelFatClient,startTimeZoneStr,endTimeZoneStr,task, true, 0);
                }
            }
            //加入返回结果
            list.add(BkcMakerToolkits.getDetectBkcRspBean(task));
        }
        return list;
    }

    /**
     * 查询埋点的记录
     * @param executeSql
     * @param resultType 兼容不同的SQL返回值
     * @return
     */
    private List<FrtDetectRspBean> getDetectedRsp(String executeSql, String batchId, int resultType) {
        log.info("{}开始执行dataphin查询", batchId);
        log.info("{}执行dataphin语句=========={}", batchId, executeSql);
        List<FrtDetectRspBean>  list = new ArrayList<>();
        try {
            Instance instance = SQLTask.run(odps, executeSql);
            log.info("{}获取instance实例信息=========={}", batchId, JSONUtil.toJsonStr(instance));
            instance.waitForSuccess();
            // 创建 InstanceTunnel
            InstanceTunnel tunnel = new InstanceTunnel(odps);
            // 根据 instance id，创建 DownloadSession
            InstanceTunnel.DownloadSession session = tunnel.createDownloadSession(odps.getDefaultProject(), instance.getId());
            long count = session.getRecordCount();
            if(count>0){
                // 获取数据的写法与 TableTunnel 一样
                TunnelRecordReader reader = session. openRecordReader (0, count);
                Record record;
                while ((record = reader.read()) != null) {
                  BkcMakerToolkits.buildQueryResourceRs(list, record, resultType);
                }
                reader.close();
            }
        } catch (Exception e) {
            log.error("从dataphin查询结果异常！！"+e.getMessage()+"("+executeSql+")",e);
            throw new UnifiedBusinessException("从dataphin查询结果异常！！"+e.getMessage(),e);
        }
        log.info("{}结束执行dataphin查询", batchId);
        return list;
    }



    /**
     * 重新计算某天的eda数据
     * @param reqBean
     * @return
     */
    public String recalCoverRate(BkcRecalReqBean reqBean, String linkLocation) {
        List<BkcEdaConfBean> edaNoConfList = mapper.selectEdaNoConfListByLinkLocation(reqBean.getEdaNo(), linkLocation);
        if(null==edaNoConfList||edaNoConfList.isEmpty()){
            throw new UnifiedBusinessException("不支持的edaNo编号"+reqBean.getEdaNo());
        }
        //查询当前的批次id
        String edaNoRelNewestBatchId = mapper.queryRecalNewestBatchId(reqBean.getCalDate(),reqBean.getEdaNo(), linkLocation);
        if(StringUtils.isEmpty(edaNoRelNewestBatchId)){
            throw new UnifiedBusinessException("未查询到日期:"+reqBean.getCalDate()+"支持可重新计算的batchId");
        }
        reqBean.setBatchId(edaNoRelNewestBatchId);
        return executeRecalDateCoverRate(reqBean,edaNoConfList, linkLocation);
    }

    /**
     * 执行重新计算某天的eda后端的数值
     * @param reqBean
     * @param configList
     * @return
     */
    private String executeRecalDateCoverRate(BkcRecalReqBean reqBean, List<BkcEdaConfBean> configList, String linkLocation) {
        String executeTimeStr = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String timeZonePrefixStr = new SimpleDateFormat("yyyy-MM-dd").format(reqBean.getCalDateTime());
        String startTimeZoneStr = timeZonePrefixStr+"T00:00:00.000";
        String endTimeZoneStr = timeZonePrefixStr+"T23:59:59.999";
        List<BkcDetectRspBean> list = new ArrayList<>();
        for (BkcEdaConfBean task:configList){
            if("0".equals(task.getFilterType())){
                BkcEsToolkits.queryBkcEsValueFromLogEs(logRestHighLevelClient,startTimeZoneStr,endTimeZoneStr,task,false);
            }else{
                BkcEsToolkits.queryBkcEsValueFromInterfaceEs(interfaceRestHighLevelClient,startTimeZoneStr,endTimeZoneStr,task,false, 0);
            }
            //将老的对应批次的查询结果更新为删除
            mapper.updateOldBatchEsQueryDeled(reqBean.getCalDate(),reqBean.getBatchId(),task.getBurypointsEdaBkConfigId(), linkLocation);
            //将新的计算结果统计进去
            mapper.insertEachEsQueryRs(Long.parseLong(reqBean.getBatchId()),reqBean.getCalDate(),task, linkLocation);
            //加入返回结果
            list.add(BkcMakerToolkits.getDetectBkcRspBean(task));
        }
        //将老的记录删除
        mapper.updateOldBatchMergedRsDeled(reqBean.getEdaNo(),reqBean.getCalDate(),reqBean.getBatchId(), linkLocation);
        //插入新的记录
        mapper.insertBatchMergedNewRs(reqBean.getEdaNo(),reqBean.getCalDate(),reqBean.getBatchId(), linkLocation);
        //构建返回结果
        return BkcMakerToolkits.createRecalBkcRspJson(executeTimeStr,reqBean.getEdaNo(),startTimeZoneStr,endTimeZoneStr,list);
    }


    public String calNewEveryCoverJob(Calendar calDate) {
        List<BkcEdaConfBean> configList = tBurypointsEdaCoverBkMapper.queryAllBkConfigList();
        long batchId = System.currentTimeMillis();
        //获取要查询的具体日期
        String queryDate = new SimpleDateFormat("yyyyMMdd").format(calDate.getTime());
        //es索引要查传的格式
        String timeZonePrefixStr = new SimpleDateFormat("yyyy-MM-dd").format(calDate.getTime());
        String startZoneDateStr = timeZonePrefixStr + "T00:00:00.000";
        String endZoneDateStr = timeZonePrefixStr + "T23:59:59.999";
        //异步查询数据，并保存查询结果
        commonThreadPool.execute(() -> {
            try{
                List<CompletableFuture<Void>> futures = new ArrayList<>();//
                //获取配置需要计算的配置信息
                if(configList!=null&&!configList.isEmpty()){
                    //将每个都转换成task
                    //后端覆盖率
                    futures.addAll(configList.stream().map(task -> CompletableFuture.runAsync(() -> {
                                //执行查询es的埋点结果信息
                                try {
                                    log.info("进入后端覆盖率查询es({},{})对应的结果信息,对应的批次为{},对应的覆盖率类型为{}", task.getBurypointsEdaBkConfigId(), task.getEdaNo(), batchId, task.getConfigType());
                                    BkcEsToolkits.queryBkcEsValueFromInterfaceEs(interfaceRestHighLevelClient, startZoneDateStr, endZoneDateStr, task, false, 0);
                                    mapper.insertEachEsQueryRsNew(UidToolkit.generateUidString(), batchId, queryDate, task);
                                    log.info("离开后端覆盖率查询es({},{})对应的结果信息,对应的批次为{},结果为{}", task.getBurypointsEdaBkConfigId(), task.getEdaNo(), batchId, task.getCalRsValue());
                                } catch (Exception e) {
                                    log.error("执行从es里面查询数据发生异常！！！" + e.getMessage());
                                }
                            }, threadPool)).collect(Collectors.toList()));
                }
                //最终合并结果信息
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).whenComplete((r,e)->{
                    mapper.updateOldEsQueryDeledNew(batchId, queryDate);
                    //汇总数据放在另一个job去执行--5545040-暂时没用到
                    //先查询出合并结果
                    List<BkcEdaCoverRsBean> converRsList = mapper.queryMergeCalDateRsNew(batchId, queryDate);
                    if (CollectionUtils.isNotEmpty(converRsList)) {
                        for (BkcEdaCoverRsBean bkcEdaCoverRsBean : converRsList) {
                            bkcEdaCoverRsBean.setBurypointsEdaCoverRsId(UidToolkit.generateUidString());
                        }
                        List<List<BkcEdaCoverRsBean>> converRsSplitList = SqlParamToolkit.splitList(converRsList, 200);
                        for (List<BkcEdaCoverRsBean> bkcEdaCoverRsBeans : converRsSplitList) {
                            //然后插入数据库
                            mapper.insertSelectMergeCalDateRsNew(bkcEdaCoverRsBeans);
                        }
                    }
                    //清理旧数据
                    mapper.updateMergeCalDateRsOldDeledNew(batchId,queryDate);
                });
            } catch (Exception e) {
                log.error("calEveryCoverJob异步查询数据出现异常", e);
            }
        });
        JSONObject resultJson = new JSONObject();
        resultJson.put("batchId", batchId);
        resultJson.put("queryDate", queryDate);
        return ResultToolkit.getResult(resultJson.toString());
    }

    /**
     * 完善每日后端覆盖率数据
     * @param calDate
     * @return
     */
    public String coverjobImprove(Calendar calDate) {
        //获取要查询的具体日期
        String queryDate = new SimpleDateFormat("yyyyMMdd").format(calDate.getTime());
        //es索引要查传的格式
        String timeZonePrefixStr = new SimpleDateFormat("yyyy-MM-dd").format(calDate.getTime());
        String startZoneDateStr = timeZonePrefixStr + "T00:00:00.000";
        String endZoneDateStr = timeZonePrefixStr + "T23:59:59.999";
        //获取当前的batchId
        long batchId = mapper.queryLastBatchIdToday(queryDate);
        List<BkcEdaConfBean> configList = tBurypointsEdaCoverBkMapper.queryImproveBkConfigList(queryDate);
        //异步查询数据，并保存查询结果
        commonThreadPool.execute(() -> {
            try{
                List<CompletableFuture<Void>> futures = new ArrayList<>();//
                //获取配置需要计算的配置信息
                if(configList!=null&&!configList.isEmpty()){
                    //将每个都转换成task
                    //后端覆盖率
                    futures.addAll(configList.stream().map(task -> CompletableFuture.runAsync(() -> {
                        //执行查询es的埋点结果信息
                        try {
                            log.info("进入后端覆盖率查询es({},{})对应的结果信息,对应的批次为{},对应的覆盖率类型为{}", task.getBurypointsEdaBkConfigId(), task.getEdaNo(), batchId, task.getConfigType());
                            BkcEsToolkits.queryBkcEsValueFromInterfaceEs(interfaceRestHighLevelClient, startZoneDateStr, endZoneDateStr, task, false, 0);
                            mapper.insertEachEsQueryRsNew(UidToolkit.generateUidString(), batchId, queryDate, task);
                            mapper.removeOldEsQueryRs(task.getBurypointsEdaCoverRsdId());
                            log.info("离开后端覆盖率查询es({},{})对应的结果信息,对应的批次为{},结果为{}", task.getBurypointsEdaBkConfigId(), task.getEdaNo(), batchId, task.getCalRsValue());
                        } catch (Exception e) {
                            log.error("执行从es里面查询数据发生异常！！！" + e.getMessage());
                        }
                    }, threadPool)).collect(Collectors.toList()));
                }
                //最终合并结果信息
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).whenComplete((r,e)->{
                    //先查询出合并结果
                    List<BkcEdaCoverRsBean> converRsList = mapper.queryMergeCalDateRsNew(batchId, queryDate);
                    if (CollectionUtils.isNotEmpty(converRsList)) {
                        for (BkcEdaCoverRsBean bkcEdaCoverRsBean : converRsList) {
                            bkcEdaCoverRsBean.setBurypointsEdaCoverRsId(UidToolkit.generateUidString());
                        }
                        List<List<BkcEdaCoverRsBean>> converRsSplitList = SqlParamToolkit.splitList(converRsList, 200);
                        for (List<BkcEdaCoverRsBean> bkcEdaCoverRsBeans : converRsSplitList) {
                            //然后插入数据库
                            mapper.insertSelectMergeCalDateRsNew(bkcEdaCoverRsBeans);
                        }
                    }
                    //清理旧数据
                    mapper.updateMergeCalDateRsOldDeledNew(batchId,queryDate);
                });
            } catch (Exception e) {
                log.error("calEveryCoverJob异步查询数据出现异常", e);
            }
        });
        JSONObject resultJson = new JSONObject();
        resultJson.put("batchId", batchId);
        resultJson.put("queryDate", queryDate);
        return ResultToolkit.getResult(resultJson.toString());
    }


    /**
     * 计算每日的覆盖率
     * @param calDate
     * @return
     */
    public String calEveryCoverJob(Calendar calDate, String linkLocation) {
        List<BkcEdaConfBean> configList = mapper.selectEdaCoverConfigList(linkLocation);
        long batchId = System.currentTimeMillis();
        //获取要查询的具体日期
        String queryDate = new SimpleDateFormat("yyyyMMdd").format(calDate.getTime());
        //es索引要查传的格式
        String timeZonePrefixStr = new SimpleDateFormat("yyyy-MM-dd").format(calDate.getTime());
        String startZoneDateStr = timeZonePrefixStr + "T00:00:00.000";
        String endZoneDateStr = timeZonePrefixStr + "T23:59:59.999";
        String startDateStr = timeZonePrefixStr + " 00:00:00";
        String endDateStr = timeZonePrefixStr + " 23:59:59";
        //异步查询数据，并保存查询结果
        commonThreadPool.execute(() -> {
            try{
                List<CompletableFuture<Void>> futures = new ArrayList<>();//
                //获取配置需要计算的配置信息
                if(configList!=null&&!configList.isEmpty()){
                    //将每个都转换成task
                    //后端覆盖率
                    futures.addAll(configList.stream().filter(task -> StringUtils.equals("1", task.getConfigType()))
                            .map(task -> CompletableFuture.runAsync(() -> {
                                //执行查询es的埋点结果信息
                                try {
                                    log.info("进入后端覆盖率查询es({},{})对应的结果信息,对应的批次为{},对应的覆盖率类型为{}", task.getBurypointsEdaBkConfigId(), task.getEdaNo(), batchId, task.getConfigType());
                                    if ("0".equals(task.getFilterType())) {
                                        BkcEsToolkits.queryBkcEsValueFromLogEs(logRestHighLevelClient, startZoneDateStr, endZoneDateStr, task, false);
                                    } else {
                                        BkcEsToolkits.queryBkcEsValueFromInterfaceEs(interfaceRestHighLevelClient, startZoneDateStr, endZoneDateStr, task, false, 0);
                                    }
                                    mapper.insertEachEsQueryRs(batchId, queryDate, task, linkLocation);
                                    mapper.updateOldEsQueryDeled(batchId, queryDate, String.valueOf(task.getBurypointsEdaBkConfigId()), linkLocation);
                                    log.info("离开后端覆盖率查询es({},{})对应的结果信息,对应的批次为{},结果为{}", task.getBurypointsEdaBkConfigId(), task.getEdaNo(), batchId, task.getCalRsValue());
                                } catch (Exception e) {
                                    log.error("执行从es里面查询数据发生异常！！！" + e.getMessage());
                                }
                            }, threadPool)).collect(Collectors.toList()));

                    //前端覆盖率
                    List<BkcEdaConfBean> ftCollect = configList.stream().filter(task -> StringUtils.equals("2", task.getConfigType())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(ftCollect)) {
                        String batchSize = ConfigService.getAppConfig().getProperty("execute.coverRate.edaBranch.dataphin.batch.size", "500");
                        String priority = ConfigService.getAppConfig().getProperty("execute.coverRate.edaBranch.dataphin.batch.priority", "9");
                        //list 按指定长度分隔
                        List<List<BkcEdaConfBean>> ftCollectListList = Lists.partition(ftCollect, Integer.parseInt(batchSize));
                        futures.addAll(ftCollectListList.stream()
                                .map(ftCollectList -> CompletableFuture.runAsync(() -> {
                                    log.info("进入前端覆盖率查询,对应的批次：{}，本次查询数量：{}", batchId, ftCollectList.size());
                                    Set<Long> bkConfigIds = ftCollectList.stream().map(BkcEdaConfBean::getBurypointsEdaBkConfigId).collect(Collectors.toSet());
                                    //转英文逗号分隔字符串
                                    String bkConfigIdsStr = String.join(",", bkConfigIds.stream().map(id -> id + "").collect(Collectors.toList()));
                                    log.info("前端覆盖率查询,对应的批次：{},configIds：{}",batchId, bkConfigIdsStr);
                                    //前端覆盖率
                                    BkcDetectReqBean reqBean = new BkcDetectReqBean();
                                    reqBean.setStartTime(startDateStr);
                                    reqBean.setEndTime(endDateStr);
                                    reqBean.setLinkLocation(linkLocation);
                                    //组装对应的sql
                                    reqBean.setFillSql("and c.burypoints_eda_ft_config_id in (" + bkConfigIdsStr + ")");
                                    String executeSql = BkcMakerToolkits.buildCoverRateDataphinExecuteSql(reqBean, "4");
                                    log.info("前端覆盖率查询,对应的批次：{},生成的SQL：{}",batchId, executeSql);
                                    List<FrtDetectRspBean> bkftConfigIdList = getDetectedCoverFrontRsp(executeSql, Integer.parseInt(priority));
                                    log.info("前端覆盖率查询,对应的批次：{},查询结果：{}",batchId, JsonToolkit.objectToJson(bkftConfigIdList));
                                    //将查询结果转换成map
                                    Map<String, FrtDetectRspBean> bkftRespMap = bkftConfigIdList.stream()
                                            .collect(Collectors.toMap(FrtDetectRspBean::getConfigId,
                                                    Function.identity(), (oldValue, newValue) -> oldValue));
                                    ftCollectList.forEach(task -> {
                                        FrtDetectRspBean frtDetectRspBean = bkftRespMap.get(String.valueOf(task.getBurypointsEdaBkConfigId()));
                                        //先设默认值
                                        task.setCalRsInfo("默认值");
                                        task.setCalRsValue(0);
                                        log.info("前端覆盖率查询,对应的批次：{},configId:{},查询结果：{}",batchId
                                                ,task.getBurypointsEdaBkConfigId(),JsonToolkit.objectToJson(frtDetectRspBean));
                                        //查询到数据则替换掉默认值
                                        if (ObjectUtil.isNotNull(frtDetectRspBean)) {
                                            task.setCalRsInfo("查询成功！！");
                                            task.setCalRsValue(frtDetectRspBean.getFtPv());
                                        }
                                        mapper.insertEachEsQueryRsFt(batchId, queryDate, task, linkLocation);
                                        mapper.updateOldEsQueryDeledFt(batchId, queryDate, String.valueOf(task.getBurypointsEdaBkConfigId()), linkLocation);
                                        log.info("前端覆盖率查询({},{})对应的结果信息,对应的批次为{},结果为{}", task.getBurypointsEdaBkConfigId(), task.getEdaNo(), batchId, task.getCalRsValue());
                                    });
                                    log.info("离开前端覆盖率查询,对应的批次：{}，本次查询结果数量：{}", batchId, bkftConfigIdList.size());
                                }, threadPool)).collect(Collectors.toList()));
                    }
                }
                //最终合并结果信息
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).whenComplete((r,e)->{
                    mapper.insertSelectMergeCalDateRs(batchId,queryDate, linkLocation);
                    mapper.updateMergeCalDateRsOldDeled(batchId,queryDate, linkLocation);
                });
            } catch (Exception e) {
                log.error("calEveryCoverJob异步查询数据出现异常", e);
            }
        });
        JSONObject resultJson = new JSONObject();
        resultJson.put("batchId", batchId);
        resultJson.put("queryDate", queryDate);
        return ResultToolkit.getResult(resultJson.toString());
    }

    /**
     * 将配置的interfacefilters 的非jsonarray改成jsonArray
     * @return
     */
    public String changeEsIntererfaceConfigToArray(String linkLocation) {
        List<BkcEdaConfBean>  allConfigList = mapper.selectEdaBkCoverConfigList(linkLocation);
        if(null!=allConfigList){
            allConfigList.forEach(each->{
               String interfaceFilterValues = each.getInterfaceFilters();
               if(StringUtils.isNotEmpty(interfaceFilterValues)){
                   List<Map<String,Object>>  filterMapList = new ArrayList<>();
                   JSONArray filterArray = new JSONArray(interfaceFilterValues);
                   for(int i=0;i<filterArray.length();i++){
                       Map<String,Object> filterMap = new HashMap<>();
                       JSONObject eachJson = filterArray.getJSONObject(i);
                       if(eachJson.has("fkey")){
                           filterMap.put("fkey",eachJson.getString("fkey"));
                           String fvalueStr = eachJson.optString("fvalue","");
                           if(StringUtils.isNotEmpty(fvalueStr)){
                               filterMap.put("fvalue",BkcEsToolkits.tryGetFieldValues(fvalueStr));
                           }
                           if(eachJson.has("frel")){
                               filterMap.put("frel",eachJson.getString("frel"));//获取对应的计算关系
                           }
                           filterMapList.add(filterMap);
                       }
                   }
                   String newInterfaceFilterValues = JsonToolkit.objectToJson(filterMapList);
                   //更新eda接口filter的配置
                   mapper.updateEdaBkConfInterfaceFvalues(each.getBurypointsEdaBkConfigId(),newInterfaceFilterValues);
               }
            });
        }
        return ResultToolkit.getResult();
    }


    public Req5545032OBean getDetectedList(Req5545031IBean data) {
        Req5545032OBean req5545032OBean = new Req5545032OBean();
        List<Req5545032OBean> list = tBurypointsCoverRateMapper.queryDetectedList(data.getOptLinkId());
        if(CollectionUtils.isEmpty(list)){
            return req5545032OBean;
        }
        list.stream().forEach(dataBean->{dataBean.setSearchItem(Arrays.asList(dataBean.getSearchItemStr().split(",")));dataBean.setSearchItemStr(null);});
        req5545032OBean.setEdaList(list);
        return req5545032OBean;
    }

    public Req5545032OBean getDetectedDetail(Req5545031IBean data) {
        Req5545032OBean req5545032OBean = tBurypointsCoverRateMapper.queryDetectedDetail(data.getSearchId());
        if (ObjectUtil.isNull(req5545032OBean)) {
            return req5545032OBean;
        }
        if (StringUtils.isNotBlank(req5545032OBean.getSearchData())) {
            List<Req5545032OBean> list = JSONUtil.toList(req5545032OBean.getSearchData(), Req5545032OBean.class);
            req5545032OBean.setEdaList(list);
        }
        if (CollectionUtils.isEmpty(req5545032OBean.getEdaList())) {
            req5545032OBean.setEdaList(new ArrayList<>());
        }
        if (StringUtils.isNotBlank(req5545032OBean.getCoverData())) {
            List<BkDetectRspBean> coverJsonList = JSONUtil.toList(req5545032OBean.getCoverData(), BkDetectRspBean.class);
            boolean flag = true;
            Req5545032OBean coverBean = null;
            for (BkDetectRspBean bkDetectRspBean : coverJsonList) {
                flag = true;
                coverBean = null;
                //查询edaList里面有没有这个节点，没有则补充
                for (Req5545032OBean oBean : req5545032OBean.getEdaList()) {
                    if (StringUtils.equals(bkDetectRspBean.getEdaMxcellId(), oBean.getEdaMxcellId())) {
                        flag = false;
                        coverBean = oBean;
                        break;
                    }
                }
                if (flag) {
                    Req5545032OBean oBean = new Req5545032OBean();
                    oBean.setPv("0");
                    oBean.setUv("0");
                    oBean.setEdaMxcellId(bkDetectRspBean.getEdaMxcellId());
                    req5545032OBean.getEdaList().add(oBean);
                    coverBean = oBean;
                }
                //设置覆盖率信息
                coverBean.setFenzi(bkDetectRspBean.getFenzi());
                coverBean.setFenmu(bkDetectRspBean.getFenmu());
                coverBean.setCoverRate(bkDetectRspBean.getCoverRate());
                //分母详情--暂不处理
/*                List<Req5545032OBean> coverList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(bkDetectRspBean.getFtCoverList())) {
                    for (FrtDetectRspBean frtDetectRspBean : bkDetectRspBean.getFtCoverList()) {
                        Req5545032OBean temp = new Req5545032OBean();
                        temp.setConfigType("2");
                        temp.setcConfigId();
                    }
                }*/
            }
            //req5545032OBean.setCoverList(coverList);
        }
        //只有前端埋点的  不展示覆盖率
/*        for (Req5545032OBean oBean : req5545032OBean.getEdaList()) {
            if (StringUtils.isBlank(oBean.getCoverRate())) {
                oBean.setCoverRate(null);
            }
        }*/
        req5545032OBean.setSearchData(null);
        req5545032OBean.setCoverData(null);
        return req5545032OBean;
    }
}
