<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
	
    <settings>
        <!--日志打印 STDOUT_LOGGING-->
        <setting name="logImpl" value="LOG4J2" />
        <setting name="jdbcTypeForNull" value="NULL" /> 
        <!--设置启用数据库字段下划线映射到java对象的驼峰式命名属性，默认为false-->  
        <setting name="mapUnderscoreToCamelCase" value="true"/>
    </settings>   
    <typeAliases>
        <package name="com.wtyt.lgfesentryedamanagement.dao.bean"/>
    </typeAliases>
    

</configuration>  