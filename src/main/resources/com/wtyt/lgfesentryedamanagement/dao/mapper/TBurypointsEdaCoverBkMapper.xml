<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.lgfesentryedamanagement.dao.mapper.TBurypointsEdaCoverBkMapper">

    <insert id="batchInsert">
        INSERT INTO T_BURYPOINTS_EDA_COVER_BK(BURYPOINTS_EDA_COVER_BK_ID, EDA_MXCELL_ID, OPT_LINK_ID, INTERFACE_PROJECT_NAME, INTERFACE_TAG,
        INTERFACE_TYPE, INTERFACE_FILTERS, INTERFACE_INDEX_NAME, INTERFACE_EXCLUDE_FILTERS)
        VALUES
        <foreach collection="list" item="bean" separator="," >
            (#{bean.burypointsEdaCoverBkId}, #{bean.edaMxcellId},#{bean.optLinkId},#{bean.interfaceProjectName},#{bean.interfaceTag},
             #{bean.interfaceType},#{bean.interfaceFilters},#{bean.interfaceIndexName},#{bean.interfaceExcludeFilters})
        </foreach>
    </insert>

    <update id="delByOptLinkId">
        UPDATE T_BURYPOINTS_EDA_COVER_BK SET IS_DEL = 1, LAST_MODIFIED_TIME=now(), note='批量新增时删除老数据'
        WHERE OPT_LINK_ID=#{optLinkId} AND IS_DEL = 0
    </update>

    <select id="queryBkConfigList" resultType="com.wtyt.lgfesentryedamanagement.bkc.bean.BkcEdaConfBean">
        SELECT
            BURYPOINTS_EDA_COVER_BK_ID configId,
            EDA_MXCELL_ID,
            OPT_LINK_ID,
            INTERFACE_PROJECT_NAME,
            INTERFACE_TAG,
            INTERFACE_TYPE,
            INTERFACE_FILTERS,
            INTERFACE_INDEX_NAME,
            INTERFACE_EXCLUDE_FILTERS
        FROM T_BURYPOINTS_EDA_COVER_BK WHERE OPT_LINK_ID=#{optLinkId} AND IS_DEL = 0
    </select>

    <select id="queryAllBkConfigList" resultType="com.wtyt.lgfesentryedamanagement.bkc.bean.BkcEdaConfBean">
        SELECT
        BURYPOINTS_EDA_COVER_BK_ID configId,
        EDA_MXCELL_ID,
        OPT_LINK_ID,
        INTERFACE_PROJECT_NAME,
        INTERFACE_TAG,
        INTERFACE_TYPE,
        INTERFACE_FILTERS,
        INTERFACE_INDEX_NAME,
        INTERFACE_EXCLUDE_FILTERS
        FROM T_BURYPOINTS_EDA_COVER_BK T
        WHERE IS_DEL = 0
    </select>

    <select id="queryImproveBkConfigList"
            resultType="com.wtyt.lgfesentryedamanagement.bkc.bean.BkcEdaConfBean">
        SELECT
        BURYPOINTS_EDA_COVER_BK_ID configId,
        EDA_MXCELL_ID,
        OPT_LINK_ID,
        INTERFACE_PROJECT_NAME,
        INTERFACE_TAG,
        INTERFACE_TYPE,
        INTERFACE_FILTERS,
        INTERFACE_INDEX_NAME,
        INTERFACE_EXCLUDE_FILTERS,
        BURYPOINTS_EDA_COVER_RSD_ID
        FROM T_BURYPOINTS_EDA_COVER_BK T
        INNER T_BURYPOINTS_EDA_COVER_RSD R
        WHERE R.IS_DEL = 0 AND R.BURYPOINTS_EDA_COVER_BK_ID = T.BURYPOINTS_EDA_COVER_BK_ID
        AND R.CAL_DATE = #{calDate}
        AND R.CAL_RS_VALUE = 0
    </select>

</mapper>