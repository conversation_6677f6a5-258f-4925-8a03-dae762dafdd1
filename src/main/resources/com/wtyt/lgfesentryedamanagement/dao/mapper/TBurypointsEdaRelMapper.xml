<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.lgfesentryedamanagement.dao.mapper.TBurypointsEdaRelMapper">

  <insert id="insert">
    INSERT INTO T_BURYPOINTS_EDA_REL(
    BURYPOINTS_EDA_REL_ID,
    ELE_RESOURCE_ID, ELE_EDA_NO,
    EDA_BRANCH, IS_START, IS_END, IS_M_BRANCH, ELE_EXT_FIELD, ELE_EXT_FIELD_VAL,ENV,BUR_DOMAIN_REL_ID
    <if test="isDel != null">
      ,IS_DEL
    </if>
    )
    VALUES(#{burypointsEdaRelId}, #{eleResourceId}, #{eleEdaNo}, #{edaBranch}, #{isStart}, #{isEnd}, #{isMBranch}, #{eleExtField}, #{eleExtFieldVal},#{env},#{burDomainRelId}
    <if test="isDel != null">
      ,#{isDel}
    </if>
    )
  </insert>
  <update id="updateByPrimaryKey">
    UPDATE T_BURYPOINTS_EDA_REL SET
    <if test="isDel != null">
      IS_DEL = #{isDel},
    </if>
    BURYPOINTS_EDA_REL_ID = #{burypointsEdaRelId},
    ELE_RESOURCE_ID = #{eleResourceId},
    ELE_EDA_NO = #{eleEdaNo},
    EDA_BRANCH = #{edaBranch},
    IS_START = #{isStart},
    IS_END = #{isEnd},
    IS_M_BRANCH = #{isMBranch},
    ELE_EXT_FIELD = #{eleExtField},
    ELE_EXT_FIELD_VAL = #{eleExtFieldVal},
    ENV = #{env},
    BUR_DOMAIN_REL_ID = #{burDomainRelId},
    LAST_MODIFIED_TIME = now()
    WHERE BURYPOINTS_EDA_REL_ID = #{burypointsEdaRelId}
  </update>

  <select id="queryByIdList" resultType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaRel">
    SELECT BURYPOINTS_EDA_REL_ID, ELE_RESOURCE_ID, ELE_EDA_NO, EDA_BRANCH, IS_START, IS_END, IS_M_BRANCH, ELE_EXT_FIELD, ELE_EXT_FIELD_VAL, CREATED_TIME, IS_DEL, ENV, BUR_DOMAIN_REL_ID
    FROM T_BURYPOINTS_EDA_REL
    WHERE 1 = 1
    <if test="isDel != null">
      AND IS_DEL = #{isDel}
    </if>
    AND BURYPOINTS_EDA_REL_ID IN
    <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="queryByEdaAndBranchList" resultType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaRel">
    SELECT BURYPOINTS_EDA_REL_ID, ELE_RESOURCE_ID, ELE_EDA_NO, EDA_BRANCH, IS_START, IS_END, IS_M_BRANCH, ELE_EXT_FIELD, ELE_EXT_FIELD_VAL, CREATED_TIME, IS_DEL, ENV, BUR_DOMAIN_REL_ID
    FROM T_BURYPOINTS_EDA_REL
    WHERE 1 = 1
    <if test="isDel != null">
      AND IS_DEL = #{isDel}
    </if>
    AND ELE_EDA_NO = #{edaNo}
    AND EDA_BRANCH IN
    <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
      #{item.edaNo}
    </foreach>
  </select>

  <select id="selectByParams" resultType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaRel"
          parameterType="com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545025IBean">
    SELECT BURYPOINTS_EDA_REL_ID, ELE_RESOURCE_ID, ELE_EDA_NO, EDA_BRANCH, IS_START, IS_END, IS_M_BRANCH, ELE_EXT_FIELD, ELE_EXT_FIELD_VAL, ENV, BUR_DOMAIN_REL_ID, CREATED_TIME, LAST_MODIFIED_TIME,IS_DEL
    FROM T_BURYPOINTS_EDA_REL
    WHERE IS_DEL = 0
    <if test="eleResourceId != null">
      AND ELE_RESOURCE_ID = #{eleResourceId}
    </if>
    <if test="eleEdaNo != null">
      AND ELE_EDA_NO = #{eleEdaNo}
    </if>
    <if test="edaBranch != null">
      AND EDA_BRANCH = #{edaBranch}
    </if>
    <if test="isStart != null">
      AND IS_START = #{isStart}
    </if>
    <if test="isEnd != null">
      AND IS_END = #{isEnd}
    </if>
    <if test="isMBranch != null">
      AND IS_M_BRANCH = #{isMBranch}
    </if>
    <if test="eleExtField != null">
      AND ELE_EXT_FIELD = #{eleExtField}
    </if>
    <if test="eleExtFieldVal != null">
      AND ELE_EXT_FIELD_VAL = #{eleExtFieldVal}
    </if>
    <if test="burypointsEdaRelId != null">
      AND BURYPOINTS_EDA_REL_ID != #{burypointsEdaRelId}
    </if>

  </select>

  <select id="getEdaRelInfo" resultType="com.wtyt.lgfesentryedamanagement.eda.bean.response.Res5545027OBean">
    select
      a.BURYPOINTS_EDA_REL_ID burypointsEdaRelId,
      c.EDA_NO eleEdaNo,
      c.EDA_ABBREVE_NAME edaAbBreveName,
      a.ELE_RESOURCE_ID eleResourceId,
      b.EDA_NO edaBranch,
      a.IS_START isStart,
      a.IS_END isEnd,
      a.IS_M_BRANCH isMBranch,
      a.ELE_EXT_FIELD eleExtField,
      a.ELE_EXT_FIELD_VAL eleExtFieldVal,
      c.BURYPOINTS_EDA_MASTER_ID edaId,
      c.EDA_NAME edaName,
      c.EDA_DESC edaDesc,
      b.EDA_NAME edaBranchName,
      b.BURYPOINTS_EDA_ID edaBranchId,
      b.EDA_DESC edaBranchDesc,
      b.BIZ_TYPE bizType,
      a.ENV env,
      a.BUR_DOMAIN_REL_ID burDomainRelId,
      d.BUR_DOMAIN_TABLE_NAME burDomainTableName,
      d.BUR_PAGE_CODE burPageCode,
      d.BUR_PAGE_CODE_NAME burPageCodeName
    from
      t_burypoints_eda_rel a
        inner join T_BURYPOINTS_EDA b on
        a.EDA_BRANCH = b.EDA_NO
        inner join t_burypoints_eda_master c on
        b.EDA_PARENT_NO = c.EDA_NO
        left join t_bur_domain_rel d on a.BUR_DOMAIN_REL_ID = d.BUR_DOMAIN_REL_ID
    where a.BURYPOINTS_EDA_REL_ID = #{burypointsEdaRelId}
  </select>

  <select id="selectByIds" resultType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaRel">
    SELECT BURYPOINTS_EDA_REL_ID, ELE_RESOURCE_ID, ELE_EDA_NO, EDA_BRANCH, IS_START, IS_END, IS_M_BRANCH, ELE_EXT_FIELD, ELE_EXT_FIELD_VAL, ENV, BUR_DOMAIN_REL_ID, CREATED_TIME, LAST_MODIFIED_TIME,IS_DEL
    FROM T_BURYPOINTS_EDA_REL
    WHERE BURYPOINTS_EDA_REL_ID IN
    <foreach collection="burypointsEdaRelIds" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
    AND IS_DEL = 0
  </select>

  <update id="deleteByIds">
    UPDATE T_BURYPOINTS_EDA_REL SET IS_DEL = 1 ,LAST_MODIFIED_TIME = now()
    WHERE BURYPOINTS_EDA_REL_ID IN
    <foreach collection="burypointsEdaRelIds" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </update>

  <select id="selectByEdaBranch" resultType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaRel">
    SELECT BURYPOINTS_EDA_REL_ID, ELE_RESOURCE_ID, ELE_EDA_NO, EDA_BRANCH, IS_START, IS_END, IS_M_BRANCH, ELE_EXT_FIELD, ELE_EXT_FIELD_VAL, ENV, BUR_DOMAIN_REL_ID, CREATED_TIME, LAST_MODIFIED_TIME,IS_DEL
    FROM T_BURYPOINTS_EDA_REL
    WHERE EDA_BRANCH IN
    <foreach collection="edaBranches" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
    AND IS_DEL = 0
  </select>

</mapper>