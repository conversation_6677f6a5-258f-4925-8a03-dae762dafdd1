<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.lgfesentryedamanagement.dao.mapper.TBurypointsEdaSyncLogMapper">

    <insert id="saveSyncLog">
        INSERT INTO T_BURYPOINTS_EDA_SYNC_LOG(BURYPOINTS_EDA_SYNC_LOG_ID, ENV, SYNC_TABLE_NAME, SOURCE_ID, TARGET_ID)
        VALUES
        <foreach collection="list" item="bean" separator="," >
            (#{bean.burypointsEdaSyncLogId}, #{bean.env}, #{bean.syncTableName}, #{bean.sourceId}, #{bean.targetId})
        </foreach>
    </insert>


    <select id="queryByList" resultType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaSyncLog">
    SELECT
    BURYPOINTS_EDA_SYNC_LOG_ID,
    ENV,
    SYNC_TABLE_NAME,
    SOURCE_ID,
    TARGET_ID
    FROM T_BURYPOINTS_EDA_SYNC_LOG
    WHERE IS_DEL = 0
    AND ENV = #{env}
    AND SYNC_TABLE_NAME = #{syncTableName}
    AND SOURCE_ID IN
    <foreach collection="burypointsEdaRelIdList" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>
</mapper>