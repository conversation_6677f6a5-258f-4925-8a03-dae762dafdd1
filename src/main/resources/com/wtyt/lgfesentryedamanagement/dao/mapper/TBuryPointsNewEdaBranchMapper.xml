<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.lgfesentryedamanagement.dao.mapper.TBuryPointsNewEdaBranchMapper">
    <resultMap id="BaseResultMap" type="com.wtyt.lgfesentryedamanagement.dao.bean.TBuryPointsNewEdaBranch">
        <!--@mbg.generated-->
        <!--@Table t_burypoints_eda-->
        <id column="BURYPOINTS_EDA_BRANCH_ID" jdbcType="BIGINT" property="burypointsEdaBranchId" />
        <result column="OPT_LINK_ID" jdbcType="VARCHAR" property="optLinkId" />
        <result column="EDA_BRANCH_NO" jdbcType="VARCHAR" property="edaBranchNo" />
        <result column="EDA_BRANCH_NAME" jdbcType="VARCHAR" property="edaBranchName" />
        <result column="APP_TAG" jdbcType="VARCHAR" property="appTag" />
        <result column="PRODUCT_ENV" jdbcType="VARCHAR" property="productEnv" />
        <result column="DEVICE_ENV" jdbcType="VARCHAR" property="deviceEnv" />
        <result column="BIZ_TYPE" jdbcType="BIGINT" property="bizType" />
        <result column="IS_DEL" jdbcType="TINYINT" property="isDel" />
        <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime" />
        <result column="LAST_MODIFIED_TIME" jdbcType="TIMESTAMP" property="lastModifiedTime" />
        <result column="NOTE" jdbcType="VARCHAR" property="note" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        BURYPOINTS_EDA_BRANCH_ID, OPT_LINK_ID, EDA_BRANCH_NO, APP_TAG, PRODUCT_ENV, DEVICE_ENV, BIZ_TYPE, IS_DEL
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List" />
        from t_burypoints_eda_branch
        where BURYPOINTS_EDA_BRANCH_ID = #{burypointsEdaBranchId,jdbcType=BIGINT}
    </select>

    <select id="selectBranchAndPointsInfo" resultType="com.wtyt.lgfesentryedamanagement.eda.bean.response.BurypointsNewEdaBranchAndPointsInfo">
       select b.EDA_BRANCH_NO,b.EDA_BRANCH_NAME,b.APP_TAG ,b.BIZ_TYPE,bp.EDA_MXCELL_ID ,bp.ELE_RESOURCE_ID,bp.ELE_RESOURCE_NAME,bp.ELE_EXT_JSON,bp.ELE_COLUMN_JSON,bp.EDA_LINK_LOC from t_burypoints_eda_branch b 
        left join t_eda_branch_points bp on bp.EDA_BRANCH_ID = b.BURYPOINTS_EDA_BRANCH_ID 
        where b.IS_DEL =0
        and b.OPT_LINK_ID = #{optLinkId}
    </select>

    <insert id="batchInsert">
        INSERT INTO t_burypoints_eda_branch(BURYPOINTS_EDA_BRANCH_ID, OPT_LINK_ID, EDA_BRANCH_NO,EDA_BRANCH_NAME, APP_TAG, PRODUCT_ENV, DEVICE_ENV, BIZ_TYPE)
        VALUES
        <foreach collection="list" item="bean" separator="," >
            (#{bean.burypointsEdaBranchId}, #{bean.optLinkId},#{bean.edaBranchNo},#{bean.edaBranchName},#{bean.appTag},#{bean.productEnv},
             #{bean.deviceEnv},#{bean.bizType})
        </foreach>
    </insert>

    <update id="delByOptLinkId">
        UPDATE t_burypoints_eda_branch set IS_DEL = 1, LAST_MODIFIED_TIME=now(), note='批量新增时删除老数据'
        where OPT_LINK_ID=#{optLinkId} and is_del=0
    </update>

</mapper>