<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.lgfesentryedamanagement.dao.mapper.TBuryPointsEleColumnMapper">


    <insert id="batchInsert">
        INSERT INTO T_BURYPOINTS_ELE_COLUMN(BURYPOINTS_ELE_COLUMN_ID, RESOURCE_NAME, EVENT_DESC, FROM_PAGE_RESOURCE_ID, FROM_RESOURCE_ID, BELONG_RESOURCE_NAME, BELONG_RESOURCE_ID)
        VALUES
        <foreach collection="list" item="bean" separator="," >
            (#{bean.burypointsEleColumnId}, #{bean.resourceName},#{bean.eventDesc},#{bean.fromPageResourceId},#{bean.fromResourceId}, #{bean.belongResourceName},#{bean.belongResourceId})
        </foreach>
    </insert>

    <update id="delByPointsOptLinkId">
        UPDATE T_BURYPOINTS_ELE_COLUMN c set IS_DEL = 1, LAST_MODIFIED_TIME=now(), note='批量新增时删除老数据'
        where is_del=0 and exists(select 1 from T_BURYPOINTS_EDA_POINTS p where p.OPT_LINK_ID=#{optLinkId} and p.is_del = 0 and p.BURYPOINTS_ELE_COLUMN_ID = c.BURYPOINTS_ELE_COLUMN_ID)
    </update>

    <update id="delByCoverOptLinkId">
        UPDATE T_BURYPOINTS_ELE_COLUMN c set IS_DEL = 1, LAST_MODIFIED_TIME=now(), note='批量新增时删除老数据'
        where is_del=0 and exists(select 1 from T_BURYPOINTS_EDA_COVER_FT p where p.OPT_LINK_ID=#{optLinkId} and p.is_del = 0 and p.BURYPOINTS_ELE_COLUMN_ID = c.BURYPOINTS_ELE_COLUMN_ID)
    </update>

</mapper>