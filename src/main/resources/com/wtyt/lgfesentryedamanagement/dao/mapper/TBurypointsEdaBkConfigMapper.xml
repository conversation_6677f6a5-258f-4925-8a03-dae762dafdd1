<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.lgfesentryedamanagement.dao.mapper.TBurypointsEdaBkConfigMapper">
  <resultMap id="BaseResultMap" type="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaBkConfig">
    <!--@mbg.generated-->
    <!--@Table t_burypoints_eda_bk_config-->
    <id column="BURYPOINTS_EDA_BK_CONFIG_ID" jdbcType="BIGINT" property="burypointsEdaBkConfigId" />
    <result column="EDA_NO" jdbcType="VARCHAR" property="edaNo" />
    <result column="ES_INDEX_NAME" jdbcType="VARCHAR" property="esIndexName" />
    <result column="INTERFACE_PROJECT_NAME" jdbcType="VARCHAR" property="interfaceProjectName" />
    <result column="INTERFACE_TAG" jdbcType="VARCHAR" property="interfaceTag" />
    <result column="INTERFACE_TYPE" jdbcType="TINYINT" property="interfaceType" />
    <result column="CAL_TYPE" jdbcType="TINYINT" property="calType" />
    <result column="FILTER_TYPE" jdbcType="TINYINT" property="filterType" />
    <result column="LOG_FILTERS_KEYWORDS" jdbcType="VARCHAR" property="logFiltersKeywords" />
    <result column="INTERFACE_FILTERS" jdbcType="LONGVARCHAR" property="interfaceFilters" />
    <result column="IS_DEL" jdbcType="TINYINT" property="isDel" />
    <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="LAST_MODIFIED_TIME" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="NOTE" jdbcType="VARCHAR" property="note" />
    <result column="LOG_FILTERS_EXCLUDE_KEYWORDS" jdbcType="VARCHAR" property="logFiltersExcludeKeywords" />
    <result column="INTERFACE_INDEX_NAME" jdbcType="VARCHAR" property="interfaceIndexName" />
    <result column="INTERFACE_EXCLUDE_FILTERS" jdbcType="LONGVARCHAR" property="interfaceExcludeFilters" />
    <result column="LINK_LOCATION" jdbcType="TINYINT" property="linkLocation" />
    <result column="EDA_BRANCH" jdbcType="VARCHAR" property="edaBranch" />
    <result column="ELE_RESOURCE_ID" jdbcType="VARCHAR" property="eleResourceId" />
    <result column="ELE_EXT_FIELD" jdbcType="VARCHAR" property="eleExtField" />
    <result column="ELE_EXT_FIELD_VAL" jdbcType="VARCHAR" property="eleExtFieldVal" />
    <result column="CONFIG_TYPE" jdbcType="VARCHAR" property="configType" />
  </resultMap>
  <sql id="Bk_Base_Column_List">
    BURYPOINTS_EDA_BK_CONFIG_ID, EDA_NO, ES_INDEX_NAME, INTERFACE_PROJECT_NAME, INTERFACE_TAG, 
    INTERFACE_TYPE, CAL_TYPE, FILTER_TYPE, LOG_FILTERS_KEYWORDS, REPLACE(REPLACE(JSON_EXTRACT(INTERFACE_FILTERS, '$'), ' ', ''), CHAR(10), '') as INTERFACE_FILTERS, IS_DEL,
    CREATED_TIME, LAST_MODIFIED_TIME, NOTE, LOG_FILTERS_EXCLUDE_KEYWORDS, INTERFACE_INDEX_NAME, 
    INTERFACE_EXCLUDE_FILTERS, LINK_LOCATION, EDA_BRANCH,
    NULL AS ELE_RESOURCE_ID,
    NULL AS ELE_EXT_FIELD,
    NULL AS ELE_EXT_FIELD_VAL,
    '1' as CONFIG_TYPE
  </sql>

  <sql id="Ft_Base_Column_List">
    BURYPOINTS_EDA_FT_CONFIG_ID AS BURYPOINTS_EDA_BK_CONFIG_ID, EDA_NO,
    NULL AS ES_INDEX_NAME,
    NULL AS INTERFACE_PROJECT_NAME,
    NULL AS INTERFACE_TAG,
    NULL AS INTERFACE_TYPE,
    NULL AS CAL_TYPE,
    NULL AS FILTER_TYPE,
    NULL AS LOG_FILTERS_KEYWORDS,
    NULL AS INTERFACE_FILTERS,
    IS_DEL,
    CREATED_TIME,
    LAST_MODIFIED_TIME,
    NOTE,
    NULL AS LOG_FILTERS_EXCLUDE_KEYWORDS,
    NULL AS INTERFACE_INDEX_NAME,
    NULL AS INTERFACE_EXCLUDE_FILTERS,
    LINK_LOCATION,
    EDA_BRANCH,
    ELE_RESOURCE_ID,
    ELE_EXT_FIELD,
    ELE_EXT_FIELD_VAL,
    '2' as CONFIG_TYPE
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Bk_Base_Column_List" />
    from t_burypoints_eda_bk_config
    where BURYPOINTS_EDA_BK_CONFIG_ID = #{burypointsEdaBkConfigId,jdbcType=BIGINT}

    UNION ALL

    select
    <include refid="Ft_Base_Column_List" />
    from t_burypoints_eda_ft_config
    where BURYPOINTS_EDA_FT_CONFIG_ID = #{burypointsEdaBkConfigId,jdbcType=BIGINT}

  </select>

    <select id="queryByIdList" resultType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaBkConfig">
      SELECT
          BURYPOINTS_EDA_BK_CONFIG_ID, EDA_NO, ES_INDEX_NAME, INTERFACE_PROJECT_NAME, INTERFACE_TAG,
          INTERFACE_TYPE, CAL_TYPE, FILTER_TYPE, LOG_FILTERS_KEYWORDS, INTERFACE_FILTERS,
          LOG_FILTERS_EXCLUDE_KEYWORDS, INTERFACE_INDEX_NAME, INTERFACE_EXCLUDE_FILTERS, CREATED_TIME, IS_DEL, LINK_LOCATION, EDA_BRANCH,
          NULL AS ELE_RESOURCE_ID,
          NULL AS ELE_EXT_FIELD,
          NULL AS ELE_EXT_FIELD_VAL,
          '1' as CONFIG_TYPE
      FROM T_BURYPOINTS_EDA_BK_CONFIG
      WHERE 1=1
      <if test="isDel != null and isDel != ''">
        AND IS_DEL = #{isDel}
      </if>
      AND BURYPOINTS_EDA_BK_CONFIG_ID IN
      <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
        #{item}
      </foreach>


      UNION ALL

      SELECT
      BURYPOINTS_EDA_FT_CONFIG_ID AS BURYPOINTS_EDA_BK_CONFIG_ID, EDA_NO,
      NULL AS ES_INDEX_NAME,
      NULL AS INTERFACE_PROJECT_NAME,
      NULL AS INTERFACE_TAG,
      NULL AS INTERFACE_TYPE,
      NULL AS CAL_TYPE,
      NULL AS FILTER_TYPE,
      NULL AS LOG_FILTERS_KEYWORDS,
      NULL AS INTERFACE_FILTERS,
      NULL AS LOG_FILTERS_EXCLUDE_KEYWORDS,
      NULL AS INTERFACE_INDEX_NAME,
      NULL AS INTERFACE_EXCLUDE_FILTERS,
      CREATED_TIME,
      IS_DEL,
      LINK_LOCATION,
      EDA_BRANCH,
      ELE_RESOURCE_ID,
      ELE_EXT_FIELD,
      ELE_EXT_FIELD_VAL,
      '2' as CONFIG_TYPE
      FROM T_BURYPOINTS_EDA_FT_CONFIG
      WHERE 1=1
      <if test="isDel != null and isDel != ''">
        AND IS_DEL = #{isDel}
      </if>
      AND BURYPOINTS_EDA_FT_CONFIG_ID IN
      <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
        #{item}
      </foreach>

    </select>

    <select id="queryByEdaNo" resultType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaBkConfig">
      SELECT
      BURYPOINTS_EDA_BK_CONFIG_ID, EDA_NO, ES_INDEX_NAME, INTERFACE_PROJECT_NAME, INTERFACE_TAG,
      INTERFACE_TYPE, CAL_TYPE, FILTER_TYPE, LOG_FILTERS_KEYWORDS, INTERFACE_FILTERS,
      LOG_FILTERS_EXCLUDE_KEYWORDS, INTERFACE_INDEX_NAME, INTERFACE_EXCLUDE_FILTERS, CREATED_TIME, IS_DEL, LINK_LOCATION, EDA_BRANCH,
      NULL AS ELE_RESOURCE_ID,
      NULL AS ELE_EXT_FIELD,
      NULL AS ELE_EXT_FIELD_VAL,
      '1' as CONFIG_TYPE
      FROM T_BURYPOINTS_EDA_BK_CONFIG
      WHERE 1=1
      <if test="isDel != null and isDel != ''">
        AND IS_DEL = #{isDel}
      </if>
      AND EDA_NO = #{edaNo}


      UNION ALL

      SELECT
      BURYPOINTS_EDA_FT_CONFIG_ID AS BURYPOINTS_EDA_BK_CONFIG_ID, EDA_NO,
      NULL AS ES_INDEX_NAME,
      NULL AS INTERFACE_PROJECT_NAME,
      NULL AS INTERFACE_TAG,
      NULL AS INTERFACE_TYPE,
      NULL AS CAL_TYPE,
      NULL AS FILTER_TYPE,
      NULL AS LOG_FILTERS_KEYWORDS,
      NULL AS INTERFACE_FILTERS,
      NULL AS LOG_FILTERS_EXCLUDE_KEYWORDS,
      NULL AS INTERFACE_INDEX_NAME,
      NULL AS INTERFACE_EXCLUDE_FILTERS,
      CREATED_TIME,
      IS_DEL,
      LINK_LOCATION,
      EDA_BRANCH,
      ELE_RESOURCE_ID,
      ELE_EXT_FIELD,
      ELE_EXT_FIELD_VAL,
      '2' as CONFIG_TYPE
      FROM T_BURYPOINTS_EDA_FT_CONFIG
      WHERE 1=1
      <if test="isDel != null and isDel != ''">
        AND IS_DEL = #{isDel}
      </if>
      AND EDA_NO = #{edaNo}
    </select>
  <!-- 后端覆盖率 -->
  <insert id="insert" parameterType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaBkConfig">
    <!--@mbg.generated-->
    insert into t_burypoints_eda_bk_config (BURYPOINTS_EDA_BK_CONFIG_ID, EDA_NO, ES_INDEX_NAME, INTERFACE_PROJECT_NAME,
      INTERFACE_TAG, INTERFACE_TYPE, CAL_TYPE, 
      FILTER_TYPE, LOG_FILTERS_KEYWORDS, INTERFACE_FILTERS, 
      IS_DEL,
      NOTE, LOG_FILTERS_EXCLUDE_KEYWORDS, INTERFACE_INDEX_NAME, 
      INTERFACE_EXCLUDE_FILTERS, LINK_LOCATION, EDA_BRANCH)
    values (#{burypointsEdaBkConfigId,jdbcType=BIGINT}, #{edaNo,jdbcType=VARCHAR}, #{esIndexName,jdbcType=VARCHAR}, #{interfaceProjectName,jdbcType=VARCHAR},
      #{interfaceTag,jdbcType=VARCHAR}, #{interfaceType,jdbcType=TINYINT}, #{calType,jdbcType=TINYINT}, 
      #{filterType,jdbcType=TINYINT}, #{logFiltersKeywords,jdbcType=VARCHAR}, #{interfaceFilters,jdbcType=LONGVARCHAR}, 
      #{isDel,jdbcType=TINYINT},
      #{note,jdbcType=VARCHAR}, #{logFiltersExcludeKeywords,jdbcType=VARCHAR}, #{interfaceIndexName,jdbcType=VARCHAR}, 
      #{interfaceExcludeFilters,jdbcType=LONGVARCHAR}, #{linkLocation,jdbcType=TINYINT}, #{edaBranch,jdbcType=VARCHAR})
  </insert>
  <!-- 前端覆盖率 -->
  <insert id="insertFt" parameterType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaBkConfig">
    <!--@mbg.generated-->
    insert into t_burypoints_eda_ft_config (BURYPOINTS_EDA_FT_CONFIG_ID, EDA_NO, EDA_BRANCH, LINK_LOCATION, ELE_RESOURCE_ID, ELE_EXT_FIELD, ELE_EXT_FIELD_VAL, IS_DEL)
    values (#{burypointsEdaBkConfigId,jdbcType=BIGINT}, #{edaNo,jdbcType=VARCHAR}, #{edaBranch,jdbcType=VARCHAR}, #{linkLocation,jdbcType=TINYINT},
    #{eleResourceId,jdbcType=VARCHAR}, #{eleExtField,jdbcType=VARCHAR}, #{eleExtFieldVal,jdbcType=VARCHAR}, #{isDel,jdbcType=TINYINT})
  </insert>

  <insert id="insertSelective" parameterType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaBkConfig">
    <!--@mbg.generated-->
    insert into t_burypoints_eda_bk_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      BURYPOINTS_EDA_BK_CONFIG_ID,
      <if test="edaNo != null and edaNo != ''">
        EDA_NO,
      </if>
      <if test="esIndexName != null and esIndexName != ''">
        ES_INDEX_NAME,
      </if>
      <if test="interfaceProjectName != null and interfaceProjectName != ''">
        INTERFACE_PROJECT_NAME,
      </if>
      <if test="interfaceTag != null and interfaceTag != ''">
        INTERFACE_TAG,
      </if>
      <if test="interfaceType != null">
        INTERFACE_TYPE,
      </if>
      <if test="calType != null">
        CAL_TYPE,
      </if>
      <if test="filterType != null">
        FILTER_TYPE,
      </if>
      <if test="logFiltersKeywords != null and logFiltersKeywords != ''">
        LOG_FILTERS_KEYWORDS,
      </if>
      <if test="interfaceFilters != null and interfaceFilters != ''">
        INTERFACE_FILTERS,
      </if>
      <if test="isDel != null">
        IS_DEL,
      </if>
      <if test="createdTime != null">
        CREATED_TIME,
      </if>
      <if test="lastModifiedTime != null">
        LAST_MODIFIED_TIME,
      </if>
      <if test="note != null and note != ''">
        NOTE,
      </if>
      <if test="logFiltersExcludeKeywords != null and logFiltersExcludeKeywords != ''">
        LOG_FILTERS_EXCLUDE_KEYWORDS,
      </if>
      <if test="interfaceIndexName != null and interfaceIndexName != ''">
        INTERFACE_INDEX_NAME,
      </if>
      <if test="interfaceExcludeFilters != null and interfaceExcludeFilters != ''">
        INTERFACE_EXCLUDE_FILTERS,
      </if>
      <if test="linkLocation != null and linkLocation != ''">
        LINK_LOCATION,
      </if>
      <if test="edaBranch != null and edaBranch != ''">
        EDA_BRANCH,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{burypointsEdaBkConfigId,jdbcType=BIGINT},
      <if test="edaNo != null and edaNo != ''">
        #{edaNo,jdbcType=VARCHAR},
      </if>
      <if test="esIndexName != null and esIndexName != ''">
        #{esIndexName,jdbcType=VARCHAR},
      </if>
      <if test="interfaceProjectName != null and interfaceProjectName != ''">
        #{interfaceProjectName,jdbcType=VARCHAR},
      </if>
      <if test="interfaceTag != null and interfaceTag != ''">
        #{interfaceTag,jdbcType=VARCHAR},
      </if>
      <if test="interfaceType != null">
        #{interfaceType,jdbcType=TINYINT},
      </if>
      <if test="calType != null">
        #{calType,jdbcType=TINYINT},
      </if>
      <if test="filterType != null">
        #{filterType,jdbcType=TINYINT},
      </if>
      <if test="logFiltersKeywords != null and logFiltersKeywords != ''">
        #{logFiltersKeywords,jdbcType=VARCHAR},
      </if>
      <if test="interfaceFilters != null and interfaceFilters != ''">
        #{interfaceFilters,jdbcType=LONGVARCHAR},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=TINYINT},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="note != null and note != ''">
        #{note,jdbcType=VARCHAR},
      </if>
      <if test="logFiltersExcludeKeywords != null and logFiltersExcludeKeywords != ''">
        #{logFiltersExcludeKeywords,jdbcType=VARCHAR},
      </if>
      <if test="interfaceIndexName != null and interfaceIndexName != ''">
        #{interfaceIndexName,jdbcType=VARCHAR},
      </if>
      <if test="interfaceExcludeFilters != null and interfaceExcludeFilters != ''">
        #{interfaceExcludeFilters,jdbcType=LONGVARCHAR},
      </if>
      <if test="linkLocation != null and linkLocation != ''">
        #{linkLocation,jdbcType=TINYINT},
      </if>
      <if test="edaBranch != null and edaBranch != ''">
        #{edaBranch,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <insert id="insertSelectiveFt" parameterType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaBkConfig">
    insert into t_burypoints_eda_ft_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      BURYPOINTS_EDA_FT_CONFIG_ID,
      <if test="edaNo != null and edaNo != ''">
        EDA_NO,
      </if>
      <if test="edaBranch != null and edaBranch != ''">
        EDA_BRANCH,
      </if>
      <if test="linkLocation != null and linkLocation != ''">
        LINK_LOCATION,
      </if>
      <if test="eleResourceId != null and eleResourceId != ''">
        ELE_RESOURCE_ID,
      </if>
      <if test="eleExtField != null and eleExtField != ''">
        ELE_EXT_FIELD,
      </if>
      <if test="eleExtFieldVal != null and eleExtFieldVal != ''">
        ELE_EXT_FIELD_VAL,
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=TINYINT},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="note != null and note != ''">
        #{note,jdbcType=VARCHAR},
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{burypointsEdaBkConfigId,jdbcType=BIGINT},
      <if test="edaNo != null and edaNo != ''">
        #{edaNo,jdbcType=VARCHAR},
      </if>
      <if test="edaBranch != null and edaBranch != ''">
        #{edaBranch,jdbcType=VARCHAR},
      </if>
      <if test="linkLocation != null and linkLocation != ''">
        #{linkLocation,jdbcType=TINYINT},
      </if>
      <if test="eleResourceId != null and eleResourceId != ''">
        #{eleResourceId,jdbcType=VARCHAR},
      </if>
      <if test="eleExtField != null and eleExtField != ''">
        #{eleExtField,jdbcType=VARCHAR},
      </if>
      <if test="eleExtFieldVal != null and eleExtFieldVal != ''">
        #{eleExtFieldVal,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaBkConfig">
    <!--@mbg.generated-->
    update t_burypoints_eda_bk_config
     set
        EDA_NO = #{edaNo,jdbcType=VARCHAR},
        ES_INDEX_NAME = #{esIndexName,jdbcType=VARCHAR},
        INTERFACE_PROJECT_NAME = #{interfaceProjectName,jdbcType=VARCHAR},
        INTERFACE_TAG = #{interfaceTag,jdbcType=VARCHAR},
        INTERFACE_TYPE = #{interfaceType,jdbcType=TINYINT},
        <if test="calType != null">
        CAL_TYPE = #{calType,jdbcType=TINYINT},
        </if>
        <if test="filterType != null">
        FILTER_TYPE = #{filterType,jdbcType=TINYINT},
        </if>
        LOG_FILTERS_KEYWORDS = #{logFiltersKeywords,jdbcType=VARCHAR},
        INTERFACE_FILTERS = #{interfaceFilters,jdbcType=LONGVARCHAR},
        LAST_MODIFIED_TIME = now(),
        <if test="isDel != null">
          IS_DEL = #{isDel,jdbcType=TINYINT},
        </if>
        <if test="note != null and note != ''">
          NOTE = #{note,jdbcType=VARCHAR},
        </if>
        LOG_FILTERS_EXCLUDE_KEYWORDS = #{logFiltersExcludeKeywords,jdbcType=VARCHAR},
        INTERFACE_INDEX_NAME = #{interfaceIndexName,jdbcType=VARCHAR},
        INTERFACE_EXCLUDE_FILTERS = #{interfaceExcludeFilters,jdbcType=LONGVARCHAR},
        LINK_LOCATION = #{linkLocation,jdbcType=TINYINT},
        EDA_BRANCH = #{edaBranch,jdbcType=VARCHAR}
    where BURYPOINTS_EDA_BK_CONFIG_ID = #{burypointsEdaBkConfigId,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKeySelectiveFt" parameterType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaBkConfig">
    update t_burypoints_eda_ft_config
    set
        EDA_NO = #{edaNo,jdbcType=VARCHAR},
        ELE_RESOURCE_ID = #{eleResourceId,jdbcType=VARCHAR},
        ELE_EXT_FIELD = #{eleExtField,jdbcType=VARCHAR},
        ELE_EXT_FIELD_VAL = #{eleExtFieldVal,jdbcType=VARCHAR},
        LAST_MODIFIED_TIME = now(),
        <if test="isDel != null">
          IS_DEL = #{isDel,jdbcType=TINYINT},
        </if>
        <if test="note != null and note != ''">
          NOTE = #{note,jdbcType=VARCHAR},
        </if>
        LINK_LOCATION = #{linkLocation,jdbcType=TINYINT},
        EDA_BRANCH = #{edaBranch,jdbcType=VARCHAR}
    where BURYPOINTS_EDA_FT_CONFIG_ID = #{burypointsEdaBkConfigId,jdbcType=BIGINT}
  </update>


  <update id="removeByPrimaryKeySelective">
    <!--@mbg.generated-->
    update t_burypoints_eda_bk_config
    set
    IS_DEL = 1,
    LAST_MODIFIED_TIME = now()
    where is_del = 0 and BURYPOINTS_EDA_BK_CONFIG_ID = #{burypointsEdaBkConfigId,jdbcType=BIGINT}
  </update>

  <update id="removeByPrimaryKeySelectiveFt">
    update t_burypoints_eda_ft_config
    set
    IS_DEL = 1,
    LAST_MODIFIED_TIME = now()
    where is_del = 0 and BURYPOINTS_EDA_FT_CONFIG_ID = #{burypointsEdaBkConfigId,jdbcType=BIGINT}
  </update>

</mapper>