<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.lgfesentryedamanagement.dao.mapper.TBurypointsEdaMapper">
  <resultMap id="BaseResultMap" type="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEda">
    <!--@mbg.generated-->
    <!--@Table t_burypoints_eda-->
    <id column="BURYPOINTS_EDA_ID" jdbcType="BIGINT" property="burypointsEdaId" />
    <result column="EDA_NO" jdbcType="VARCHAR" property="edaNo" />
    <result column="EDA_NAME" jdbcType="VARCHAR" property="edaName" />
    <result column="EDA_DESC" jdbcType="VARCHAR" property="edaDesc" />
    <result column="EDA_TYPE" jdbcType="INTEGER" property="edaType" />
    <result column="IS_DEL" jdbcType="TINYINT" property="isDel" />
    <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="LAST_MODIFIED_TIME" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="NOTE" jdbcType="VARCHAR" property="note" />
    <result column="EDA_BIZ_LINE" jdbcType="VARCHAR" property="edaBizLine" />
    <result column="EDA_PRODUCT" jdbcType="VARCHAR" property="edaProduct" />
    <result column="EDA_MODULE" jdbcType="VARCHAR" property="edaModule" />
    <result column="EDA_PARENT_NO" jdbcType="VARCHAR" property="edaParentNo" />
    <result column="BIZ_TYPE" jdbcType="INTEGER" property="bizType" />
    <result column="BIZ_BUR_CODE" jdbcType="VARCHAR" property="bizBurCode" />
    <result column="APP_DEVICE_UDID" jdbcType="VARCHAR" property="appDeviceUdid" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    BURYPOINTS_EDA_ID, EDA_NO, EDA_NAME, EDA_DESC, EDA_TYPE, IS_DEL, CREATED_TIME, LAST_MODIFIED_TIME, 
    NOTE, EDA_BIZ_LINE, EDA_PRODUCT, EDA_MODULE, EDA_PARENT_NO, BIZ_TYPE, BIZ_BUR_CODE, 
    APP_DEVICE_UDID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from t_burypoints_eda
    where BURYPOINTS_EDA_ID = #{burypointsEdaId,jdbcType=BIGINT}
  </select>

  <select id="queryByEdaNoList" resultType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEda">
    SELECT
    BURYPOINTS_EDA_ID, EDA_NO, EDA_NAME, EDA_DESC, EDA_TYPE, EDA_BIZ_LINE, EDA_PRODUCT, EDA_MODULE, EDA_PARENT_NO, BIZ_TYPE, BIZ_BUR_CODE, APP_DEVICE_UDID, CREATED_TIME, IS_DEL, JUDGE_TYPE
    FROM T_BURYPOINTS_EDA
    WHERE 1=1
    <if test="isDel != null and isDel != ''">
      AND IS_DEL = #{isDel}
    </if>
    AND EDA_NO IN
    <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="queryByEdaParentNo" resultType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEda">
    SELECT
      BURYPOINTS_EDA_ID, EDA_NO, EDA_NAME, EDA_DESC, EDA_TYPE, EDA_BIZ_LINE, EDA_PRODUCT,
      EDA_MODULE, EDA_PARENT_NO, BIZ_TYPE, BIZ_BUR_CODE, APP_DEVICE_UDID, CREATED_TIME, IS_DEL,JUDGE_TYPE
    FROM T_BURYPOINTS_EDA
    WHERE 1=1
    <if test="isDel != null and isDel != ''">
      AND IS_DEL = #{isDel}
    </if>
    <if test="isDel != null and isDel != ''">
      AND IS_DEL = #{isDel}
    </if>
    AND EDA_PARENT_NO = #{edaParentNo}
  </select>

  <select id="selectByEdaNo" resultType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEda">
    SELECT
    BURYPOINTS_EDA_ID, EDA_NO, EDA_NAME, EDA_DESC, EDA_TYPE, EDA_BIZ_LINE, EDA_PRODUCT,
    EDA_MODULE, EDA_PARENT_NO, BIZ_TYPE, BIZ_BUR_CODE, APP_DEVICE_UDID, CREATED_TIME, IS_DEL,JUDGE_TYPE
    FROM T_BURYPOINTS_EDA
    WHERE EDA_NO = #{edaNo}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from t_burypoints_eda
    where BURYPOINTS_EDA_ID = #{burypointsEdaId,jdbcType=BIGINT}
  </delete>

  <delete id="deleteByEdaNo" >
    <!--@mbg.generated-->
    delete from t_burypoints_eda
    where EDA_NO = #{edaNo}
  </delete>

  <update id="deleteByEdaNos">
    update  t_burypoints_eda set IS_DEL = 1 ,LAST_MODIFIED_TIME = now()
    WHERE EDA_NO IN
    <foreach collection="edaNos" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </update>


  <insert id="insert" keyColumn="BURYPOINTS_EDA_ID" keyProperty="burypointsEdaId" parameterType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEda" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_burypoints_eda (EDA_NO, EDA_NAME, EDA_DESC, 
      EDA_TYPE, IS_DEL, CREATED_TIME, 
      LAST_MODIFIED_TIME, NOTE, EDA_BIZ_LINE, 
      EDA_PRODUCT, EDA_MODULE, EDA_PARENT_NO, 
      BIZ_TYPE, BIZ_BUR_CODE, APP_DEVICE_UDID
      )
    values (#{edaNo,jdbcType=VARCHAR}, #{edaName,jdbcType=VARCHAR}, #{edaDesc,jdbcType=VARCHAR}, 
      #{edaType,jdbcType=INTEGER}, #{isDel,jdbcType=TINYINT}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{lastModifiedTime,jdbcType=TIMESTAMP}, #{note,jdbcType=VARCHAR}, #{edaBizLine,jdbcType=VARCHAR}, 
      #{edaProduct,jdbcType=VARCHAR}, #{edaModule,jdbcType=VARCHAR}, #{edaParentNo,jdbcType=VARCHAR}, 
      #{bizType,jdbcType=INTEGER}, #{bizBurCode,jdbcType=VARCHAR}, #{appDeviceUdid,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="BURYPOINTS_EDA_ID" keyProperty="burypointsEdaId" parameterType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEda" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_burypoints_eda
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="edaNo != null and edaNo != ''">
        EDA_NO,
      </if>
      <if test="edaName != null and edaName != ''">
        EDA_NAME,
      </if>
      <if test="edaDesc != null and edaDesc != ''">
        EDA_DESC,
      </if>
      <if test="edaType != null">
        EDA_TYPE,
      </if>
      <if test="isDel != null">
        IS_DEL,
      </if>
      <if test="createdTime != null">
        CREATED_TIME,
      </if>
      <if test="lastModifiedTime != null">
        LAST_MODIFIED_TIME,
      </if>
      <if test="note != null and note != ''">
        NOTE,
      </if>
      <if test="edaBizLine != null and edaBizLine != ''">
        EDA_BIZ_LINE,
      </if>
      <if test="edaProduct != null and edaProduct != ''">
        EDA_PRODUCT,
      </if>
      <if test="edaModule != null and edaModule != ''">
        EDA_MODULE,
      </if>
      <if test="edaParentNo != null and edaParentNo != ''">
        EDA_PARENT_NO,
      </if>
      <if test="bizType != null">
        BIZ_TYPE,
      </if>
      <if test="judgeType != null">
        JUDGE_TYPE,
      </if>
      <if test="bizBurCode != null and bizBurCode != ''">
        BIZ_BUR_CODE,
      </if>
      <if test="appDeviceUdid != null and appDeviceUdid != ''">
        APP_DEVICE_UDID,
      </if>
      <if test="syncTime != null">
        SYNC_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="edaNo != null and edaNo != ''">
        #{edaNo,jdbcType=VARCHAR},
      </if>
      <if test="edaName != null and edaName != ''">
        #{edaName,jdbcType=VARCHAR},
      </if>
      <if test="edaDesc != null and edaDesc != ''">
        #{edaDesc,jdbcType=VARCHAR},
      </if>
      <if test="edaType != null">
        #{edaType,jdbcType=INTEGER},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=TINYINT},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="note != null and note != ''">
        #{note,jdbcType=VARCHAR},
      </if>
      <if test="edaBizLine != null and edaBizLine != ''">
        #{edaBizLine,jdbcType=VARCHAR},
      </if>
      <if test="edaProduct != null and edaProduct != ''">
        #{edaProduct,jdbcType=VARCHAR},
      </if>
      <if test="edaModule != null and edaModule != ''">
        #{edaModule,jdbcType=VARCHAR},
      </if>
      <if test="edaParentNo != null and edaParentNo != ''">
        #{edaParentNo,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=INTEGER},
      </if>
      <if test="judgeType != null">
        #{judgeType,jdbcType=INTEGER},
      </if>
      <if test="bizBurCode != null and bizBurCode != ''">
        #{bizBurCode,jdbcType=VARCHAR},
      </if>
      <if test="appDeviceUdid != null and appDeviceUdid != ''">
        #{appDeviceUdid,jdbcType=VARCHAR},
      </if>
      <if test="syncTime != null">
        #{syncTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEda">
    <!--@mbg.generated-->
    update t_burypoints_eda
    <set>
      <if test="edaNo != null and edaNo != ''">
        EDA_NO = #{edaNo,jdbcType=VARCHAR},
      </if>
      <if test="edaName != null and edaName != ''">
        EDA_NAME = #{edaName,jdbcType=VARCHAR},
      </if>
      <if test="edaDesc != null and edaDesc != ''">
        EDA_DESC = #{edaDesc,jdbcType=VARCHAR},
      </if>
      <if test="edaType != null">
        EDA_TYPE = #{edaType,jdbcType=INTEGER},
      </if>
      <if test="isDel != null">
        IS_DEL = #{isDel,jdbcType=TINYINT},
      </if>
      LAST_MODIFIED_TIME = now(),
      <if test="note != null and note != ''">
        NOTE = #{note,jdbcType=VARCHAR},
      </if>
      <if test="edaBizLine != null and edaBizLine != ''">
        EDA_BIZ_LINE = #{edaBizLine,jdbcType=VARCHAR},
      </if>
      <if test="edaProduct != null and edaProduct != ''">
        EDA_PRODUCT = #{edaProduct,jdbcType=VARCHAR},
      </if>
      <if test="edaModule != null and edaModule != ''">
        EDA_MODULE = #{edaModule,jdbcType=VARCHAR},
      </if>
      <if test="edaParentNo != null and edaParentNo != ''">
        EDA_PARENT_NO = #{edaParentNo,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        BIZ_TYPE = #{bizType,jdbcType=INTEGER},
      </if>
      <if test="judgeType != null">
        JUDGE_TYPE = #{judgeType,jdbcType=INTEGER},
      </if>
      <if test="bizBurCode != null and bizBurCode != ''">
        BIZ_BUR_CODE = #{bizBurCode,jdbcType=VARCHAR},
      </if>
      <if test="appDeviceUdid != null and appDeviceUdid != ''">
        APP_DEVICE_UDID = #{appDeviceUdid,jdbcType=VARCHAR},
      </if>
      <if test="syncTime != null">
        SYNC_TIME = #{syncTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bizTypeModifiedTime != null">
        BIZ_TYPE_MODIFIED_TIME = #{bizTypeModifiedTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where BURYPOINTS_EDA_ID = #{burypointsEdaId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEda">
    <!--@mbg.generated-->
    update t_burypoints_eda
    set EDA_NO = #{edaNo,jdbcType=VARCHAR},
      EDA_NAME = #{edaName,jdbcType=VARCHAR},
      EDA_DESC = #{edaDesc,jdbcType=VARCHAR},
      EDA_TYPE = #{edaType,jdbcType=INTEGER},
      IS_DEL = #{isDel,jdbcType=TINYINT},
      CREATED_TIME = #{createdTime,jdbcType=TIMESTAMP},
      LAST_MODIFIED_TIME = #{lastModifiedTime,jdbcType=TIMESTAMP},
      NOTE = #{note,jdbcType=VARCHAR},
      EDA_BIZ_LINE = #{edaBizLine,jdbcType=VARCHAR},
      EDA_PRODUCT = #{edaProduct,jdbcType=VARCHAR},
      EDA_MODULE = #{edaModule,jdbcType=VARCHAR},
      EDA_PARENT_NO = #{edaParentNo,jdbcType=VARCHAR},
      BIZ_TYPE = #{bizType,jdbcType=INTEGER},
      BIZ_BUR_CODE = #{bizBurCode,jdbcType=VARCHAR},
      APP_DEVICE_UDID = #{appDeviceUdid,jdbcType=VARCHAR}
    where BURYPOINTS_EDA_ID = #{burypointsEdaId,jdbcType=BIGINT}
  </update>
</mapper>