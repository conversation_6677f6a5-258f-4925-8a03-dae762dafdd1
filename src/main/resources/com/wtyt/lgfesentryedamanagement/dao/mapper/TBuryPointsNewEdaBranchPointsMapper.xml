<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.lgfesentryedamanagement.dao.mapper.TBuryPointsNewEdaBranchPointsMapper">
    <resultMap id="BaseResultMap" type="com.wtyt.lgfesentryedamanagement.dao.bean.TBuryPointsNewEdaBranchPoints">
        <!--@mbg.generated-->
        <!--@Table t_burypoints_eda-->
        <id column="EDA_BRANCH_POINTS_ID" jdbcType="BIGINT" property="edaBranchPointsId" />
        <result column="EDA_BRANCH_ID" jdbcType="BIGINT" property="edaBranchId" />
        <result column="EDA_BRANCH_NO" jdbcType="VARCHAR" property="edaBranchNo" />
        <result column="EDA_MXCELL_ID" jdbcType="VARCHAR" property="edaMxcellId" />
        <result column="ELE_RESOURCE_ID" jdbcType="VARCHAR" property="eleResourceId" />
        <result column="ELE_RESOURCE_NAME" jdbcType="VARCHAR" property="eleResourceName" />
        <result column="ELE_EXT_JSON" jdbcType="VARCHAR" property="eleExtJson" />
        <result column="ELE_COLUMN_JSON" jdbcType="VARCHAR" property="eleColumnJson" />
        <result column="APP_TAG" jdbcType="VARCHAR" property="appTag" />
        <result column="EDA_LINK_LOC" jdbcType="BIGINT" property="edaLinkLoc" />
        <result column="BURYPOINTS_ELE_COLUMN_ID" jdbcType="BIGINT" property="burypointsEleColumnId" />
        <result column="IS_DEL" jdbcType="TINYINT" property="isDel" />
        <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime" />
        <result column="LAST_MODIFIED_TIME" jdbcType="TIMESTAMP" property="lastModifiedTime" />
        <result column="NOTE" jdbcType="VARCHAR" property="note" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        EDA_BRANCH_POINTS_ID, EDA_BRANCH_ID, EDA_BRANCH_NO, EDA_MXCELL_ID, ELE_RESOURCE_ID, ELE_EXT_JSON, ELE_COLUMN_JSON, APP_TAG, EDA_LINK_LOC, BURYPOINTS_ELE_COLUMN_ID, IS_DEL
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List" />
        from t_eda_branch_points
        where EDA_BRANCH_POINTS_ID = #{edaBranchPointsId,jdbcType=BIGINT}
    </select>

    <select id="selectOneEleColumnId" resultType="java.lang.Long">
        <!--@mbg.generated-->
        select BURYPOINTS_ELE_COLUMN_ID from t_burypoints_eda_points 
        where IS_DEL =0 
        and OPT_LINK_ID = #{optLinkId} 
        and EDA_MXCELL_ID = #{branchPoint.edaMxcellId} 
        and ELE_RESOURCE_ID = #{branchPoint.eleResourceId}
        and ELE_EXT_JSON = #{branchPoint.eleExtJson}
        and ELE_COLUMN_JSON = #{branchPoint.eleColumnJson} 
        limit 1
    </select>

    <insert id="batchInsert">
        INSERT INTO t_eda_branch_points(
            EDA_BRANCH_POINTS_ID, 
            EDA_BRANCH_ID, 
            EDA_BRANCH_NO, 
            EDA_MXCELL_ID, 
            ELE_RESOURCE_ID,
            ELE_RESOURCE_NAME, 
            ELE_EXT_JSON, 
            ELE_COLUMN_JSON, 
            APP_TAG, 
            EDA_LINK_LOC, 
            BURYPOINTS_ELE_COLUMN_ID,
            BRANCH_POINTS_EXTFIELDS_ID)
        VALUES
        <foreach collection="list" item="bean" separator="," >
            (#{bean.edaBranchPointsId}, #{bean.edaBranchId},#{bean.edaBranchNo},#{bean.edaMxcellId},#{bean.eleResourceId},#{bean.eleResourceName},
             #{bean.eleExtJson},#{bean.eleColumnJson},#{bean.appTag},#{bean.edaLinkLoc},#{bean.burypointsEleColumnId},#{bean.branchPointsExtfieldsId})
        </foreach>
    </insert>

    <update id="delByOptLinkId">
        UPDATE t_eda_branch_points set IS_DEL = 1, LAST_MODIFIED_TIME=now(), note='批量新增时删除老数据'
        where is_del=0
        and EDA_BRANCH_ID in (
            select distinct BURYPOINTS_EDA_BRANCH_ID from t_burypoints_eda_branch where is_del=0 and OPT_LINK_ID=#{optLinkId}
        )
    </update>

</mapper>