<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.lgfesentryedamanagement.dao.mapper.TBurypointsEdaFileMapper">


    <select id="selectDrawioFile" parameterType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaFileBean" resultType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaFileBean">
        select DRAWIO_FILE_ID,FILE_NAME AS DRAWIO_FILE_NAME,OPT_LINK_ID
        from t_drawio_file t
        where IS_DEL = 0 and OPT_LINK_ID = #{optLinkId}
        order by LAST_MODIFIED_TIME desc
            limit 1
    </select>

    <select id="selectDrawioFolderId" resultType="String">
        select DRAWIO_FOLDER_ID, FOLDER_NAME
        from t_drawio_folder
        where is_del = 0
          and UPPER(FOLDER_NAME) like CONCAT('%',UPPER(#{drawioFolderName}), '%')
    </select>



    <insert id="insertEdaFolder" parameterType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaFileBean">
        INSERT INTO t_drawio_folder (DRAWIO_FOLDER_ID,FOLDER_NAME,NOTE) VALUES(#{drawioFolderId},#{drawioFolderName},#{note})
    </insert>


    <insert id="insertEdaFile" parameterType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaFileBean">
        INSERT INTO t_drawio_file (DRAWIO_FILE_ID,FILE_NAME, DRAWIO_FOLDER_ID,OPT_LINK_ID,NOTE) VALUES(#{drawioFileId},#{drawioFileName}, #{drawioFolderId},#{optLinkId},#{note})
    </insert>

    <update id="updateEdaFileInfo" parameterType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaFileBean">
        update  t_drawio_file t
        set FILE_NAME =#{drawioFileName},LAST_MODIFIED_TIME = now(),NOTE = #{note}
        where DRAWIO_FILE_ID  = #{drawioFileId} and IS_DEL =0 and  OPT_LINK_ID = #{optLinkId}
    </update>



</mapper>