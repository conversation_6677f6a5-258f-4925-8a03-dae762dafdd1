<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.lgfesentryedamanagement.dao.mapper.EdaYidaSyncMapper">

  <!-- 检查表名是否存在 -->
  <select id="checkTableExistsWithShow" resultType="java.util.Map">
    show tables like #{tableName}
  </select>

  <!-- 查询表字段列表 -->
  <select id="allColumnsFromTableWithShow" resultType="java.util.Map">
    show columns from ${tableName}
  </select>
  <!-- 根据表单实例id查询数据是否存在 -->
  <select id="checkExistByFormInstanceId" resultType="java.lang.Integer">
    select count(1) from ${tableName} where is_del = 0 and form_instance_id = #{formInstanceId}
  </select>

  <!-- 执行sql: 主要是创建表、新增表字段、新增数据、更新数据、逻辑删除数据（更新的特例） -->
  <update id="executeSql">
    ${sql}
  </update>

  <!-- 新增数据 -->
  <insert id="insertData" >
    insert into ${tableName}
    <foreach collection="mapData.entrySet()"  separator="," open="(" close=")" index="key" item="value">
       ${key}
    </foreach>
    values
    <foreach collection="mapData.entrySet()"  separator="," open="(" close=")" index="key" item="value">
      #{value}
    </foreach>
  </insert>

  <!-- 根据实例id更新数据 -->
  <update id="updateByInstanceId" >
    update ${tableName} set
    <foreach collection="mapData.entrySet()"  separator="," index="key" item="value">
      ${key}=#{value}
    </foreach>
    where form_instance_id=#{formInstanceId}
  </update>

  <!-- 根据表单实例id逻辑删除数据 -->
  <update id="logicDelete">
    update ${tableName} set is_del = 1, last_sync_time=current_timestamp() where form_instance_id = #{formInstanceId} and is_del = 0
  </update>


</mapper>