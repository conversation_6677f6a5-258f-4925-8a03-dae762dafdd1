<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.lgfesentryedamanagement.dao.mapper.TBurypointsBizTypeCfgMapper">

    <select id="getBizTypeBySingle" resultType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsBizTypeCfg">
        SELECT ENTER_ENV,ENTER_HAS_TABLE,ENTER_BIZ_TYPE,EXIST_ENV,EXIST_HAS_TABLE,EXIST_BIZ_TYPE,MIX_BIZ_TYPE,TIPS
        FROM T_BURYPOINTS_BIZ_TYPE_CFG WHERE IS_DEL = 0 AND ENTER_ENV = #{env} AND ENTER_HAS_TABLE = #{hasTable} limit 1
    </select>


    <select id="getBizType" resultType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsBizTypeCfg">
        SELECT ENTER_ENV,ENTER_HAS_TABLE,ENTER_BIZ_TYPE,EXIST_ENV,EXIST_HAS_TABLE,EXIST_BIZ_TYPE,MIX_BIZ_TYPE,TIPS
        FROM T_BURYPOINTS_BIZ_TYPE_CFG WHERE IS_DEL = 0 AND ENTER_ENV = #{startEnv} AND ENTER_HAS_TABLE = #{startHasTable}
        AND EXIST_ENV = #{endEnv} AND EXIST_HAS_TABLE = #{endHasTable}  limit 1
    </select>

</mapper>