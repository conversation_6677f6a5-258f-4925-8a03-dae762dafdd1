<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.lgfesentryedamanagement.dao.mapper.TBuryPointsEleFieldMapper">


    <insert id="batchInsert">
        INSERT INTO T_BURYPOINTS_ELE_FIELD(BURYPOINTS_ELE_FIELD_ID, REL_TYPE, REL_ID, ELE_JSON, ELE_FIELD, ELE_FIELD_VAL)
        VALUES
        <foreach collection="list" item="bean" separator="," >
            (#{bean.burypointsEleFieldId}, #{bean.relType},#{bean.relId},#{bean.eleJson},#{bean.eleField}, #{bean.eleFieldVal})
        </foreach>
    </insert>

    <update id="delByOptLinkId">
        UPDATE T_BURYPOINTS_ELE_FIELD c set IS_DEL = 1, LAST_MODIFIED_TIME=now(), note='批量新增时删除老数据'
        where is_del=0 and REL_TYPE = #{relType} and exists(select 1 from T_BURYPOINTS_EDA_POINTS p where p.OPT_LINK_ID=#{optLinkId} and p.is_del = 0 and p.BURYPOINTS_EDA_POINTS_ID = c.REL_ID)
    </update>

</mapper>