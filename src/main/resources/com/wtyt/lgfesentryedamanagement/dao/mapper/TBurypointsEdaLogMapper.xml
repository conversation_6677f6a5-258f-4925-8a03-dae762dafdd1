<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.lgfesentryedamanagement.dao.mapper.TBurypointsEdaLogMapper">
  <resultMap id="BaseResultMap" type="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaLog">
    <!--@mbg.generated-->
    <!--@Table t_burypoints_eda_log-->
    <id column="BURYPOINTS_EDA_LOG_ID" jdbcType="BIGINT" property="burypointsEdaLogId" />
    <result column="OPT_TEAM" jdbcType="VARCHAR" property="optTeam" />
    <result column="OPT_USER_NAME" jdbcType="VARCHAR" property="optUserName" />
    <result column="EDA_BRANCH_NO" jdbcType="VARCHAR" property="edaBranchNo" />
    <result column="LOG_TYPE" jdbcType="INTEGER" property="logType" />
    <result column="OPT_NOTE" jdbcType="VARCHAR" property="optNote" />
    <result column="IS_DEL" jdbcType="TINYINT" property="isDel" />
    <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="LAST_MODIFIED_TIME" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="NOTE" jdbcType="VARCHAR" property="note" />
    <result column="OPT_TYPE" jdbcType="INTEGER" property="optType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    BURYPOINTS_EDA_LOG_ID, OPT_TEAM, OPT_USER_NAME, EDA_BRANCH_NO, LOG_TYPE, OPT_NOTE, 
    IS_DEL, CREATED_TIME, LAST_MODIFIED_TIME, NOTE, OPT_TYPE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from t_burypoints_eda_log
    where BURYPOINTS_EDA_LOG_ID = #{burypointsEdaLogId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from t_burypoints_eda_log
    where BURYPOINTS_EDA_LOG_ID = #{burypointsEdaLogId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="BURYPOINTS_EDA_LOG_ID" keyProperty="burypointsEdaLogId" parameterType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_burypoints_eda_log (OPT_TEAM, OPT_USER_NAME, EDA_BRANCH_NO, 
      LOG_TYPE, OPT_NOTE, IS_DEL, 
      CREATED_TIME, LAST_MODIFIED_TIME, NOTE, 
      OPT_TYPE)
    values (#{optTeam,jdbcType=VARCHAR}, #{optUserName,jdbcType=VARCHAR}, #{edaBranchNo,jdbcType=VARCHAR}, 
      #{logType,jdbcType=INTEGER}, #{optNote,jdbcType=VARCHAR}, #{isDel,jdbcType=TINYINT}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedTime,jdbcType=TIMESTAMP}, #{note,jdbcType=VARCHAR}, 
      #{optType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="BURYPOINTS_EDA_LOG_ID" keyProperty="burypointsEdaLogId" parameterType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_burypoints_eda_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="optTeam != null and optTeam != ''">
        OPT_TEAM,
      </if>
      <if test="optUserName != null and optUserName != ''">
        OPT_USER_NAME,
      </if>
      <if test="edaBranchNo != null and edaBranchNo != ''">
        EDA_BRANCH_NO,
      </if>
      <if test="logType != null">
        LOG_TYPE,
      </if>
      <if test="optNote != null and optNote != ''">
        OPT_NOTE,
      </if>
      <if test="isDel != null">
        IS_DEL,
      </if>
      <if test="createdTime != null">
        CREATED_TIME,
      </if>
      <if test="lastModifiedTime != null">
        LAST_MODIFIED_TIME,
      </if>
      <if test="note != null and note != ''">
        NOTE,
      </if>
      <if test="optType != null">
        OPT_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="optTeam != null and optTeam != ''">
        #{optTeam,jdbcType=VARCHAR},
      </if>
      <if test="optUserName != null and optUserName != ''">
        #{optUserName,jdbcType=VARCHAR},
      </if>
      <if test="edaBranchNo != null and edaBranchNo != ''">
        #{edaBranchNo,jdbcType=VARCHAR},
      </if>
      <if test="logType != null">
        #{logType,jdbcType=INTEGER},
      </if>
      <if test="optNote != null and optNote != ''">
        #{optNote,jdbcType=VARCHAR},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=TINYINT},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedTime != null">
        #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="note != null and note != ''">
        #{note,jdbcType=VARCHAR},
      </if>
      <if test="optType != null">
        #{optType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaLog">
    <!--@mbg.generated-->
    update t_burypoints_eda_log
    <set>
      <if test="optTeam != null and optTeam != ''">
        OPT_TEAM = #{optTeam,jdbcType=VARCHAR},
      </if>
      <if test="optUserName != null and optUserName != ''">
        OPT_USER_NAME = #{optUserName,jdbcType=VARCHAR},
      </if>
      <if test="edaBranchNo != null and edaBranchNo != ''">
        EDA_BRANCH_NO = #{edaBranchNo,jdbcType=VARCHAR},
      </if>
      <if test="logType != null">
        LOG_TYPE = #{logType,jdbcType=INTEGER},
      </if>
      <if test="optNote != null and optNote != ''">
        OPT_NOTE = #{optNote,jdbcType=VARCHAR},
      </if>
      <if test="isDel != null">
        IS_DEL = #{isDel,jdbcType=TINYINT},
      </if>
      <if test="createdTime != null">
        CREATED_TIME = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastModifiedTime != null">
        LAST_MODIFIED_TIME = #{lastModifiedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="note != null and note != ''">
        NOTE = #{note,jdbcType=VARCHAR},
      </if>
      <if test="optType != null">
        OPT_TYPE = #{optType,jdbcType=INTEGER},
      </if>
    </set>
    where BURYPOINTS_EDA_LOG_ID = #{burypointsEdaLogId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaLog">
    <!--@mbg.generated-->
    update t_burypoints_eda_log
    set OPT_TEAM = #{optTeam,jdbcType=VARCHAR},
      OPT_USER_NAME = #{optUserName,jdbcType=VARCHAR},
      EDA_BRANCH_NO = #{edaBranchNo,jdbcType=VARCHAR},
      LOG_TYPE = #{logType,jdbcType=INTEGER},
      OPT_NOTE = #{optNote,jdbcType=VARCHAR},
      IS_DEL = #{isDel,jdbcType=TINYINT},
      CREATED_TIME = #{createdTime,jdbcType=TIMESTAMP},
      LAST_MODIFIED_TIME = #{lastModifiedTime,jdbcType=TIMESTAMP},
      NOTE = #{note,jdbcType=VARCHAR},
      OPT_TYPE = #{optType,jdbcType=INTEGER}
    where BURYPOINTS_EDA_LOG_ID = #{burypointsEdaLogId,jdbcType=BIGINT}
  </update>
</mapper>