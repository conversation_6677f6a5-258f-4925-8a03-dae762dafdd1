<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.lgfesentryedamanagement.dao.mapper.TBurypointsFemErrorExtMapper">
    <insert id="batchInsert">
       INSERT INTO T_DRAWIO_FEM_ERROR_EXT (DRAWIO_FEM_ERROR_EXT_ID,DRAWIO_FEM_ERROR_ID, ERROR_REASON,REPORT_USER_NAME)
       VALUES 
        <foreach collection="list" item="bean" separator="," >
            (#{bean.drawioFemErrorExtId}, #{bean.drawioFemErrorId},#{bean.errorReason},#{bean.reportUserName})
        </foreach>
    </insert>

    <update id="updateOne">
        UPDATE T_DRAWIO_FEM_ERROR_EXT 
        set
            ERROR_REASON = #{bean.errorReason}, 
            ERROR_SOLUTION = #{bean.errorSolution},
            ISSUE_HANDLER = #{bean.issueHandler},
            REPORT_USER_NAME = #{bean.reportUserName},
            RESOLVE_TIME = now(),
            LAST_MODIFIED_TIME=now()
        where IS_DEL=0
        and DRAWIO_FEM_ERROR_EXT_ID = #{bean.drawioFemErrorExtId}
    </update>

</mapper>