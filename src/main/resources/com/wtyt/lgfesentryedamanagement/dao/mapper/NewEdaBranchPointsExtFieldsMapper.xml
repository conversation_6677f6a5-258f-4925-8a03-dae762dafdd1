<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.lgfesentryedamanagement.dao.mapper.NewEdaBranchPointsExtFieldsMapper">
    <insert id="batchInsert">
        INSERT INTO T_BRANCH_POINTS_EXTFIELDS(BRANCH_POINTS_EXTFIELDS_ID,ELE_EXT_FIELD1,ELE_EXT_FIELD_VAL1,ELE_EXT_FIELD2,ELE_EXT_FIELD_VAL2)
        VALUES
        <foreach collection="list" item="bean" separator="," >
            (#{bean.branchPointsExtfieldsId}, #{bean.eleExtField1},#{bean.eleExtFieldVal1},#{bean.eleExtField2},#{bean.eleExtFieldVal2})
        </foreach>
    </insert>

    <update id="delByOptLinkId">
         UPDATE T_BRANCH_POINTS_EXTFIELDS set IS_DEL = 1, LAST_MODIFIED_TIME=now(), note='批量新增时删除老数据'
            where is_del=0
            and BRANCH_POINTS_EXTFIELDS_ID in (
                select bp.BRANCH_POINTS_EXTFIELDS_ID
                    from t_burypoints_eda_branch b 
                    left join t_eda_branch_points bp on bp.EDA_BRANCH_ID = b.BURYPOINTS_EDA_BRANCH_ID 
                    where b.IS_DEL =0
                    and b.OPT_LINK_ID = #{optLinkId}
            )
    </update>

</mapper>