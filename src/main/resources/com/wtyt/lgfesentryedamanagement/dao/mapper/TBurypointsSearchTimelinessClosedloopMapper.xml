<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.lgfesentryedamanagement.dao.mapper.TBurypointsSearchTimelinessClosedloopMapper">
    <resultMap id="BaseResultMap" type="com.wtyt.lgfesentryedamanagement.dao.bean.TBuryPointsSearchTimelinessClosedloop">
        <!--@mbg.generated-->
        <!--@Table t_burypoints_eda-->
        <id column="SE_TIMELINESS_CLOSEDLOOP_ID" jdbcType="BIGINT" property="seTimelinessClosedloopId" />
        <result column="OPT_LINK_ID" jdbcType="VARCHAR" property="optLinkId" />
        <result column="EDA_BRANCH_NOS" jdbcType="VARCHAR" property="edaBranchNos" />
        <result column="SEARCH_DS" jdbcType="VARCHAR" property="searchDs" />
        <result column="ORG_IDS" jdbcType="VARCHAR" property="orgIds" />
        <result column="REAL_USER_IDS" jdbcType="VARCHAR" property="realUserIds" />
        <result column="TYPE" jdbcType="TINYINT" property="type" />
        <result column="ENV" jdbcType="TINYINT" property="env" />
        <result column="START_DATE_ID" jdbcType="VARCHAR" property="startDateId" />
        <result column="END_DATE_ID" jdbcType="VARCHAR" property="endDateId" />
        <result column="SEARCH_STATE" jdbcType="TINYINT" property="searchState" />
        <result column="SEARCH_FAIL_INFO" jdbcType="VARCHAR" property="searchFailInfo" />
        <result column="SEARCH_RESULT" jdbcType="VARCHAR" property="searchResult" />
        <result column="IS_DEL" jdbcType="TINYINT" property="isDel" />
        <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime" />
        <result column="LAST_MODIFIED_TIME" jdbcType="TIMESTAMP" property="lastModifiedTime" />
        <result column="NOTE" jdbcType="VARCHAR" property="note" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        SE_TIMELINESS_CLOSEDLOOP_ID, OPT_LINK_ID, EDA_BRANCH_NOS, SEARCH_DS,ORG_IDS,REAL_USER_IDS,TYPE,ENV, START_DATE_ID, END_DATE_ID, SEARCH_STATE,SEARCH_RESULT, SEARCH_FAIL_INFO, IS_DEL, CREATED_TIME, LAST_MODIFIED_TIME, NOTE
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List" />
        from t_se_timeliness_closedloop
        where SE_TIMELINESS_CLOSEDLOOP_ID = #{seTimelinessClosedloopId,jdbcType=BIGINT}
    </select>

     <select id="getSearchListByOptLinkId" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select <include refid="Base_Column_List" /> from t_se_timeliness_closedloop 
        where IS_DEL =0 
        and OPT_LINK_ID = #{bean.optLinkId}
        and Type = #{bean.type}
        order by CREATED_TIME desc
        limit #{bean.limit}
    </select>

    <insert id="batchInsert">
        INSERT INTO t_se_timeliness_closedloop(SE_TIMELINESS_CLOSEDLOOP_ID, OPT_LINK_ID, EDA_BRANCH_NOS, SEARCH_DS, START_DATE_ID, END_DATE_ID, SEARCH_STATE,ORG_IDS,REAL_USER_IDS,TYPE,ENV)
        VALUES
        <foreach collection="list" item="bean" separator="," >
            (#{bean.seTimelinessClosedloopId}, #{bean.optLinkId},#{bean.edaBranchNos},#{bean.searchDs},#{bean.startDateId},#{bean.endDateId},
             #{bean.searchState},#{bean.orgIds},#{bean.realUserIds},#{bean.type},#{bean.env})
        </foreach>
    </insert>

    <update id="updateOneSearchResultById">
        UPDATE t_se_timeliness_closedloop 
        set SEARCH_STATE = #{searchState}, 
            SEARCH_FAIL_INFO = #{searchFailInfo},
            SEARCH_RESULT = #{searchResult},
            LAST_MODIFIED_TIME=now(), 
            NOTE="更新查询结果"
        where IS_DEL=0
        and SE_TIMELINESS_CLOSEDLOOP_ID = #{seTimelinessClosedloopId}
    </update>

</mapper>