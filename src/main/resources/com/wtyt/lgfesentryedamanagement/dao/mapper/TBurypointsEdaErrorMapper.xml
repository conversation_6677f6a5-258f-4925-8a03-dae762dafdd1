<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.lgfesentryedamanagement.dao.mapper.TBurypointsEdaErrorMapper">

    <insert id="batchInsert">
        INSERT INTO T_BURYPOINTS_EDA_ERROR(BURYPOINTS_EDA_ERROR_ID, BURYPOINTS_EDA_REL_ID, ERROR_TYPE, ENV, BUR_DOMAIN_REL_ID,
        OPT_TEAM,OPT_USER_NAME,LOG_TYPE)
        VALUES
        <foreach collection="list" item="bean" separator="," >
            (#{bean.burypointsEdaErrorId}, #{bean.burypointsEdaRelId}, #{bean.errorType}, #{bean.env}, #{bean.burDomainRelId},
            #{bean.optTeam},#{bean.optUserName},#{bean.logType})
        </foreach>
    </insert>

    <insert id="insertEdaPointsError">
        insert into T_DRAWIO_FEM_ERROR(DRAWIO_FEM_ERROR_ID,DATA_ID,OPT_LINK_ID,EDA_MXCELL_ID,EXP_TYPE,EXP_TEXT,FROM_TYPE,IS_ALARM,DETECTION_ID)
        values (${@com.wtyt.generator.toolkit.UidToolkit@generateUidDefault()},#{dataId},#{optLinkId},#{edaMxcellId},#{expType},#{expText},#{bean.fromType},#{bean.isAlarm},#{bean.detectionId})
    </insert>


    <insert id="batchInsertEdaPointsError">
        INSERT INTO T_DRAWIO_FEM_ERROR(DRAWIO_FEM_ERROR_ID,DATA_ID,OPT_LINK_ID,EDA_MXCELL_ID,EXP_TYPE,EXP_TEXT,FROM_TYPE,IS_ALARM,DETECTION_ID)
        VALUES
        <foreach collection="list" item="bean" separator="," >
            (#{bean.drawioFemErrorId}, #{bean.dataId}, #{bean.optLinkId}, #{bean.edaMxcellId}, #{bean.expType},
            #{bean.expText},#{bean.fromType},#{bean.isAlarm},#{bean.detectionId})
        </foreach>
    </insert>
</mapper>