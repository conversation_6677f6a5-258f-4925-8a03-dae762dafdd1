<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.lgfesentryedamanagement.dao.mapper.TBurypointsEdaMasterMapper">
  <resultMap id="BaseResultMap" type="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaMaster">
    <!--@mbg.generated-->
    <!--@Table t_burypoints_eda_master-->
    <id column="BURYPOINTS_EDA_MASTER_ID" jdbcType="BIGINT" property="burypointsEdaMasterId" />
    <result column="EDA_NO" jdbcType="VARCHAR" property="edaNo" />
    <result column="EDA_NAME" jdbcType="VARCHAR" property="edaName" />
    <result column="EDA_ABBREVE_NAME" jdbcType="VARCHAR" property="edaAbbreveName" />
    <result column="EDA_DESC" jdbcType="VARCHAR" property="edaDesc" />
    <result column="EDA_BIZ_LINE" jdbcType="VARCHAR" property="edaBizLine" />
    <result column="EDA_PRODUCT" jdbcType="VARCHAR" property="edaProduct" />
    <result column="EDA_MODULE" jdbcType="VARCHAR" property="edaModule" />
    <result column="MAINTENANCE_TEAM" jdbcType="VARCHAR" property="maintenanceTeam" />
    <result column="EDA_STATUS" jdbcType="INTEGER" property="edaStatus" />
    <result column="MAINTENANCE_UI" jdbcType="VARCHAR" property="maintenanceUi" />
    <result column="COVERAGE_BASELINE" jdbcType="DECIMAL" property="coverageBaseline" />
    <result column="FLOATING_RATIO" jdbcType="DECIMAL" property="floatingRatio" />
    <result column="UNCONVENTIONAL_BASELINE_REASON" jdbcType="VARCHAR" property="unconventionalBaselineReason" />
    <result column="OPT_LINK_ID" jdbcType="VARCHAR" property="optLinkId" />
    <result column="EDA_TYPE" jdbcType="TINYINT" property="edaType" />
    <result column="IS_DEL" jdbcType="TINYINT" property="isDel" />
    <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="LAST_MODIFIED_TIME" jdbcType="TIMESTAMP" property="lastModifiedTime" />
    <result column="NOTE" jdbcType="VARCHAR" property="note" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    BURYPOINTS_EDA_MASTER_ID, EDA_NO, EDA_NAME, EDA_ABBREVE_NAME, EDA_DESC, EDA_BIZ_LINE, EDA_PRODUCT,
    EDA_MODULE, MAINTENANCE_TEAM, EDA_STATUS, MAINTENANCE_UI, COVERAGE_BASELINE,
    FLOATING_RATIO, UNCONVENTIONAL_BASELINE_REASON, OPT_LINK_ID, EDA_TYPE, IS_DEL, CREATED_TIME, LAST_MODIFIED_TIME,
    NOTE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from t_burypoints_eda_master
    where BURYPOINTS_EDA_MASTER_ID = #{burypointsEdaMasterId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from t_burypoints_eda_master
    where BURYPOINTS_EDA_MASTER_ID = #{burypointsEdaMasterId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByEdaNo">
    delete from t_burypoints_eda_master
    where EDA_NO = #{edaNo}
  </delete>
  <insert id="insert" parameterType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaMaster">
    <!--@mbg.generated-->
    insert into t_burypoints_eda_master (BURYPOINTS_EDA_MASTER_ID, EDA_NO, EDA_NAME, EDA_ABBREVE_NAME,
      EDA_DESC, EDA_BIZ_LINE, EDA_PRODUCT, 
      EDA_MODULE, MAINTENANCE_TEAM, EDA_STATUS, 
      MAINTENANCE_UI, COVERAGE_BASELINE,
      FLOATING_RATIO, UNCONVENTIONAL_BASELINE_REASON, 
      IS_DEL, CREATED_TIME, LAST_MODIFIED_TIME, 
      NOTE)
    values (#{burypointsEdaMasterId,jdbcType=BIGINT}, #{edaNo,jdbcType=VARCHAR}, #{edaName,jdbcType=VARCHAR}, #{edaAbbreveName,jdbcType=VARCHAR},
    #{edaDesc,jdbcType=VARCHAR}, #{edaBizLine,jdbcType=VARCHAR}, #{edaProduct,jdbcType=VARCHAR},
      #{edaModule,jdbcType=VARCHAR}, #{maintenanceTeam,jdbcType=VARCHAR}, #{edaStatus,jdbcType=INTEGER}, 
      #{maintenanceUi,jdbcType=VARCHAR}, #{coverageBaseline,jdbcType=DECIMAL},
      #{floatingRatio,jdbcType=DECIMAL}, #{unconventionalBaselineReason,jdbcType=VARCHAR}, 
      #{isDel,jdbcType=TINYINT}, #{createdTime,jdbcType=TIMESTAMP}, #{lastModifiedTime,jdbcType=TIMESTAMP}, 
      #{note,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaMaster">
    <!--@mbg.generated-->
    insert into t_burypoints_eda_master
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="burypointsEdaMasterId != null">
        BURYPOINTS_EDA_MASTER_ID,
      </if>
      <if test="edaNo != null and edaNo != ''">
        EDA_NO,
      </if>
      <if test="edaName != null and edaName != ''">
        EDA_NAME,
      </if>
      <if test="edaAbbreveName != null and edaAbbreveName != ''">
        EDA_ABBREVE_NAME,
      </if>
      <if test="edaDesc != null and edaDesc != ''">
        EDA_DESC,
      </if>
      <if test="edaBizLine != null and edaBizLine != ''">
        EDA_BIZ_LINE,
      </if>
      <if test="edaProduct != null and edaProduct != ''">
        EDA_PRODUCT,
      </if>
      <if test="edaModule != null and edaModule != ''">
        EDA_MODULE,
      </if>
      <if test="maintenanceTeam != null and maintenanceTeam != ''">
        MAINTENANCE_TEAM,
      </if>
      <if test="edaStatus != null">
        EDA_STATUS,
      </if>
      <if test="maintenanceUi != null and maintenanceUi != ''">
        MAINTENANCE_UI,
      </if>
      <if test="coverageBaseline != null">
        COVERAGE_BASELINE,
      </if>
      <if test="floatingRatio != null">
        FLOATING_RATIO,
      </if>
      <if test="unconventionalBaselineReason != null and unconventionalBaselineReason != ''">
        UNCONVENTIONAL_BASELINE_REASON,
      </if>
      <if test="optLinkId != null and optLinkId != ''">
        OPT_LINK_ID,
      </if>
      <if test="edaType != null">
        EDA_TYPE,
      </if>
      <if test="isDel != null">
        IS_DEL,
      </if>
      <if test="note != null and note != ''">
        NOTE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="burypointsEdaMasterId != null">
        #{burypointsEdaMasterId,jdbcType=BIGINT},
      </if>
      <if test="edaNo != null and edaNo != ''">
        #{edaNo,jdbcType=VARCHAR},
      </if>
      <if test="edaName != null and edaName != ''">
        #{edaName,jdbcType=VARCHAR},
      </if>
      <if test="edaAbbreveName != null and edaAbbreveName != ''">
        #{edaAbbreveName,jdbcType=VARCHAR},
      </if>
      <if test="edaDesc != null and edaDesc != ''">
        #{edaDesc,jdbcType=VARCHAR},
      </if>
      <if test="edaBizLine != null and edaBizLine != ''">
        #{edaBizLine,jdbcType=VARCHAR},
      </if>
      <if test="edaProduct != null and edaProduct != ''">
        #{edaProduct,jdbcType=VARCHAR},
      </if>
      <if test="edaModule != null and edaModule != ''">
        #{edaModule,jdbcType=VARCHAR},
      </if>
      <if test="maintenanceTeam != null and maintenanceTeam != ''">
        #{maintenanceTeam,jdbcType=VARCHAR},
      </if>
      <if test="edaStatus != null">
        #{edaStatus,jdbcType=INTEGER},
      </if>
      <if test="maintenanceUi != null and maintenanceUi != ''">
        #{maintenanceUi,jdbcType=VARCHAR},
      </if>
      <if test="coverageBaseline != null">
        #{coverageBaseline,jdbcType=DECIMAL},
      </if>
      <if test="floatingRatio != null">
        #{floatingRatio,jdbcType=DECIMAL},
      </if>
      <if test="unconventionalBaselineReason != null and unconventionalBaselineReason != ''">
        #{unconventionalBaselineReason,jdbcType=VARCHAR},
      </if>
      <if test="optLinkId != null and optLinkId != ''">
        #{optLinkId,jdbcType=VARCHAR},
      </if>
      <if test="edaType != null">
        #{edaType,jdbcType=INTEGER},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=TINYINT},
      </if>
      <if test="note != null and note != ''">
        #{note,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaMaster">
    <!--@mbg.generated-->
    update t_burypoints_eda_master
    <set>
      <if test="edaName != null and edaName != ''">
        EDA_NAME = #{edaName,jdbcType=VARCHAR},
      </if>
      <if test="edaDesc != null and edaDesc != ''">
        EDA_DESC = #{edaDesc,jdbcType=VARCHAR},
      </if>
      <if test="edaBizLine != null and edaBizLine != ''">
        EDA_BIZ_LINE = #{edaBizLine,jdbcType=VARCHAR},
      </if>
      <if test="edaProduct != null and edaProduct != ''">
        EDA_PRODUCT = #{edaProduct,jdbcType=VARCHAR},
      </if>
      <if test="edaModule != null and edaModule != ''">
        EDA_MODULE = #{edaModule,jdbcType=VARCHAR},
      </if>
      <if test="maintenanceTeam != null and maintenanceTeam != ''">
        MAINTENANCE_TEAM = #{maintenanceTeam,jdbcType=VARCHAR},
      </if>
      <if test="edaStatus != null">
        EDA_STATUS = #{edaStatus,jdbcType=INTEGER},
      </if>
      <if test="maintenanceUi != null and maintenanceUi != ''">
        MAINTENANCE_UI = #{maintenanceUi,jdbcType=VARCHAR},
      </if>
      <if test="coverageBaseline != null">
        COVERAGE_BASELINE = #{coverageBaseline,jdbcType=DECIMAL},
      </if>
      <if test="floatingRatio != null">
        FLOATING_RATIO = #{floatingRatio,jdbcType=DECIMAL},
      </if>
      <if test="unconventionalBaselineReason != null and unconventionalBaselineReason != ''">
        UNCONVENTIONAL_BASELINE_REASON = #{unconventionalBaselineReason,jdbcType=VARCHAR},
      </if>
      <if test="optLinkId != null and optLinkId != ''">
        OPT_LINK_ID = #{optLinkId,jdbcType=VARCHAR},
      </if>
      <if test="edaType != null">
        EDA_TYPE = #{edaType,jdbcType=INTEGER},
      </if>
      <if test="isDel != null">
        IS_DEL = #{isDel,jdbcType=TINYINT},
      </if>
      LAST_MODIFIED_TIME = now(),
      <if test="note != null and note != ''">
        NOTE = #{note,jdbcType=VARCHAR},
      </if>
    </set>
    where BURYPOINTS_EDA_MASTER_ID = #{burypointsEdaMasterId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaMaster">
    <!--@mbg.generated-->
    update t_burypoints_eda_master
    set EDA_NO = #{edaNo,jdbcType=VARCHAR},
      EDA_NAME = #{edaName,jdbcType=VARCHAR},
      EDA_DESC = #{edaDesc,jdbcType=VARCHAR},
      EDA_BIZ_LINE = #{edaBizLine,jdbcType=VARCHAR},
      EDA_PRODUCT = #{edaProduct,jdbcType=VARCHAR},
      EDA_MODULE = #{edaModule,jdbcType=VARCHAR},
      MAINTENANCE_TEAM = #{maintenanceTeam,jdbcType=VARCHAR},
      EDA_STATUS = #{edaStatus,jdbcType=INTEGER},
      MAINTENANCE_UI = #{maintenanceUi,jdbcType=VARCHAR},
      COVERAGE_BASELINE = #{coverageBaseline,jdbcType=DECIMAL},
      FLOATING_RATIO = #{floatingRatio,jdbcType=DECIMAL},
      UNCONVENTIONAL_BASELINE_REASON = #{unconventionalBaselineReason,jdbcType=VARCHAR},
      IS_DEL = #{isDel,jdbcType=TINYINT},
      CREATED_TIME = #{createdTime,jdbcType=TIMESTAMP},
      LAST_MODIFIED_TIME = #{lastModifiedTime,jdbcType=TIMESTAMP},
      NOTE = #{note,jdbcType=VARCHAR}
    where BURYPOINTS_EDA_MASTER_ID = #{burypointsEdaMasterId,jdbcType=BIGINT}
  </update>

  <update id="removeByEdaNoList">
    UPDATE T_BURYPOINTS_EDA_MASTER
    SET IS_DEL = 1
    WHERE IS_DEL = 0
    AND EDA_NO IN
    <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </update>

  <select id="selectByEdaNo" resultMap="BaseResultMap">
    select
      <include refid="Base_Column_List" />
    from
      t_burypoints_eda_master
    where
      EDA_NO = #{edaNo}
  </select>
  <select id="selectByOptLinkId" resultMap="BaseResultMap">
    select
      <include refid="Base_Column_List" />
    from
      t_burypoints_eda_master
    where
      OPT_LINK_ID = #{optLinkId}
  </select>

  <select id="selectByEdaName" resultMap="BaseResultMap">
    select
      <include refid="Base_Column_List" />
    from
      t_burypoints_eda_master
    where
      EDA_NAME = #{edaName}
  </select>
  <select id="edaMasterList" resultMap="BaseResultMap">
      SELECT
        <include refid="Base_Column_List" />
      FROM
        T_BURYPOINTS_EDA_MASTER
      WHERE
        <choose>
          <when test="filterStatus != null and filterStatus != ''">
            <if test="!filterStatus.equals('-1')">
              EDA_STATUS = #{filterStatus} AND IS_DEL = 0
            </if>
            <if test="filterStatus.equals('-1')">
              IS_DEL = 1
            </if>
          </when>
          <otherwise>
            IS_DEL = 0
          </otherwise>
        </choose>
        AND EDA_TYPE = #{edaType}
        <if test="edaNo != null and edaNo != ''">
          AND EDA_NO LIKE CONCAT('%', #{edaNo} ,'%')
        </if>
        <if test="edaName != null and edaName != ''">
          AND EDA_NAME LIKE CONCAT('%', #{edaName} ,'%')
        </if>
        <if test="edaAbbreveName != null and edaAbbreveName != ''">
          AND EDA_ABBREVE_NAME LIKE CONCAT('%', #{edaAbbreveName} ,'%')
        </if>
        <if test="maintenanceTeam != null and maintenanceTeam != ''">
          AND MAINTENANCE_TEAM = #{maintenanceTeam}
        </if>
      ORDER BY
        CREATED_TIME DESC
  </select>

  <select id="queryByEdaNoList" resultMap="BaseResultMap">
    SELECT
      BURYPOINTS_EDA_MASTER_ID, EDA_NO, EDA_NAME, EDA_ABBREVE_NAME, EDA_DESC, EDA_BIZ_LINE, EDA_PRODUCT,
      EDA_MODULE, MAINTENANCE_TEAM, EDA_STATUS, MAINTENANCE_UI, COVERAGE_BASELINE,
      FLOATING_RATIO, UNCONVENTIONAL_BASELINE_REASON, CREATED_TIME, OPT_LINK_ID, EDA_TYPE, IS_DEL
    FROM
    T_BURYPOINTS_EDA_MASTER
    WHERE 1 = 1
    <if test="isDel != null and isDel != ''">
      AND IS_DEL = #{isDel}
    </if>
    AND EDA_NO IN
    <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>

</mapper>