<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.lgfesentryedamanagement.dao.mapper.TBurypointsCoverRateMapper">

  <insert id="insertCoverRate">
    INSERT INTO T_BURYPOINTS_COVER_RATE(BURYPOINTS_COVER_RATE_ID, EDA_NO, START_TIME,
    END_TIME, EXECUTE_TIME, BK_VALUE, FT_VALUE, COVER_RATE, FT_EXECUTE_SQL, BACK_LIST, FRONT_LIST,
    OPT_USER_ID, OPT_USER_NAME, SEARCH_STATE, LINK_LOCATION)
    VALUES(#{burypointsCoverRateId}, #{edaNo}, STR_TO_DATE(#{startTime},'%Y-%m-%d %H:%i:%s'), STR_TO_DATE(#{endTime},'%Y-%m-%d %H:%i:%s'), STR_TO_DATE(#{executeTime},'%Y-%m-%d %H:%i:%s'), #{bkValue}, #{ftValue}, #{coverRate},
    #{ftExecuteSql}, #{backList}, #{frontList}, #{optUserId}, #{optUserName}, #{searchState}, #{linkLocation})
  </insert>

  <insert id="insertDetected">
    INSERT INTO T_BURYPOINTS_DETECTION(BURYPOINTS_DETECTION_ID, OPT_LINK_ID, START_TIME,
                                        END_TIME, SEARCH_TIME, EDA_NAME, ENV, SEARCH_ITEM, SEARCH_STATE, SEARCH_FAIL_INFO,
                                       EXECUTE_SQL)
    VALUES(#{burypointsDetectionId}, #{optLinkId}, STR_TO_DATE(#{startTime},'%Y-%m-%d %H:%i:%s'), STR_TO_DATE(#{endTime},'%Y-%m-%d %H:%i:%s'), STR_TO_DATE(#{executeTime},'%Y-%m-%d %H:%i:%s'), #{edaName}, #{env}, #{searchItemStr},
           #{searchState}, #{searchFailInfo}, #{ftExecuteSql})
  </insert>

  <update id="updateCoverRate">
    UPDATE T_BURYPOINTS_COVER_RATE
    SET LAST_MODIFIED_TIME = NOW()
    <if test="edaNo != null and edaNo != ''">
      , EDA_NO = #{edaNo}
    </if>
    <if test="startTime != null and startTime != ''">
      , START_TIME = STR_TO_DATE(#{startTime},'%Y-%m-%d %H:%i:%s')
    </if>
    <if test="endTime != null and endTime != ''">
      , END_TIME = STR_TO_DATE(#{endTime},'%Y-%m-%d %H:%i:%s')
    </if>
    <if test="executeTime != null and executeTime != ''">
      , EXECUTE_TIME = STR_TO_DATE(#{executeTime},'%Y-%m-%d %H:%i:%s')
    </if>
    <if test="bkValue != null and bkValue != ''">
      , BK_VALUE = #{bkValue}
    </if>
    <if test="ftValue != null and ftValue != ''">
      , FT_VALUE = #{ftValue}
    </if>
    <if test="coverRate != null and coverRate != ''">
      , COVER_RATE = #{coverRate}
    </if>
    <if test="ftExecuteSql != null and ftExecuteSql != ''">
      , FT_EXECUTE_SQL = #{ftExecuteSql}
    </if>
    <if test="backList != null and backList != ''">
      , BACK_LIST = #{backList}
    </if>
    <if test="frontList != null and frontList != ''">
      , FRONT_LIST = #{frontList}
    </if>
    <if test="optUserId != null and optUserId != ''">
      , OPT_USER_ID = #{optUserId}
    </if>
    <if test="optUserName != null and optUserName != ''">
      , OPT_USER_NAME = #{optUserName}
    </if>
    <if test="searchState != null and searchState != ''">
      , SEARCH_STATE = #{searchState}
    </if>
    <if test="linkLocation != null and linkLocation != ''">
      , LINK_LOCATION = #{linkLocation}
    </if>
    <if test="searchFailInfo != null and searchFailInfo != ''">
      , SEARCH_FAIL_INFO = #{searchFailInfo}
    </if>
    <if test="branchList != null and branchList != ''">
      , BRANCH_LIST = #{branchList}
    </if>
    WHERE BURYPOINTS_COVER_RATE_ID = #{burypointsCoverRateId}
  </update>

  <update id="updateDetected">
    UPDATE T_BURYPOINTS_DETECTION
    SET LAST_MODIFIED_TIME = NOW()
    <if test="ftExecuteSql != null and ftExecuteSql != ''">
      , EXECUTE_SQL = #{ftExecuteSql}
    </if>
    <if test="searchState != null and searchState != ''">
      , SEARCH_STATE = #{searchState}
    </if>
    <if test="searchFailInfo != null and searchFailInfo != ''">
      , SEARCH_FAIL_INFO = #{searchFailInfo}
    </if>
    <if test="searchData != null and searchData != ''">
      , SEARCH_DATA = #{searchData}
    </if>
    <if test="coverData != null and coverData != ''">
      , COVER_DATA = #{coverData}
    </if>
    WHERE BURYPOINTS_DETECTION_ID = #{burypointsDetectionId}
  </update>

    <select id="queryCoverRateList" resultType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsCoverRate">
      SELECT
      R.BURYPOINTS_COVER_RATE_ID, R.EDA_NO, M.EDA_NAME, M.EDA_ABBREVE_NAME, R.START_TIME,
      R.END_TIME, R.EXECUTE_TIME, R.BK_VALUE, R.FT_VALUE, R.COVER_RATE, R.FT_EXECUTE_SQL, R.BACK_LIST, R.FRONT_LIST,
      R.OPT_USER_ID, R.OPT_USER_NAME, R.SEARCH_STATE, R.LINK_LOCATION, R.SEARCH_FAIL_INFO, R.BRANCH_LIST
      FROM T_BURYPOINTS_COVER_RATE R
      LEFT JOIN T_BURYPOINTS_EDA_MASTER M ON R.EDA_NO = M.EDA_NO AND M.IS_DEL = 0
      WHERE R.IS_DEL = 0 AND R.OPT_USER_ID = #{optUserId}
      ORDER BY R.EXECUTE_TIME DESC LIMIT 5
    </select>
  <select id="queryDetectedList"
          resultType="com.wtyt.lgfesentryedamanagement.bkc.bean.response.Req5545032OBean">
    select
      BURYPOINTS_DETECTION_ID searchId,
      OPT_LINK_ID,
      START_TIME,
      END_TIME,
      SEARCH_TIME,
      ENV,
      SEARCH_TIME,
      EDA_NAME ,
      SEARCH_ITEM searchItemStr,
      SEARCH_STATE,
      SEARCH_FAIL_INFO
    from
      T_BURYPOINTS_DETECTION
    where
      IS_DEL = 0 and OPT_LINK_ID  = #{optLinkId}
    order by
      SEARCH_TIME desc
      limit 5
  </select>
  <select id="queryDetectedDetail"
          resultType="com.wtyt.lgfesentryedamanagement.bkc.bean.response.Req5545032OBean">
    select
      BURYPOINTS_DETECTION_ID searchId,
      OPT_LINK_ID,
      START_TIME,
      END_TIME,
      SEARCH_TIME,
      ENV,
      SEARCH_TIME,
      EDA_NAME ,
      SEARCH_ITEM searchStr,
      SEARCH_DATA,
      COVER_DATA,
      SEARCH_STATE
    from
      T_BURYPOINTS_DETECTION
    where
      IS_DEL = 0 and BURYPOINTS_DETECTION_ID  = #{searchId}
  </select>
</mapper>