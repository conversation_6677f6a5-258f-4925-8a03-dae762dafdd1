<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.lgfesentryedamanagement.dao.mapper.TBuryPointsEdaPointsMapper">
    <resultMap id="BaseResultMap" type="com.wtyt.lgfesentryedamanagement.dao.bean.TBuryPointsEdaPoints">
        <!--@mbg.generated-->
        <!--@Table t_burypoints_eda-->
        <id column="BURYPOINTS_EDA_POINTS_ID" jdbcType="BIGINT" property="burypointsEdaPointsId" />
        <result column="EDA_MXCELL_ID" jdbcType="VARCHAR" property="edaMxcellId" />
        <result column="EDA_MXCELL_PARENT_ID" jdbcType="VARCHAR" property="edaMxcellParentId" />
        <result column="EDA_MXCELL_SOURCE_ID" jdbcType="VARCHAR" property="edaMxcellSourceId" />
        <result column="EDA_MXCELL_TARGET_ID" jdbcType="VARCHAR" property="edaMxcellTargetId" />
        <result column="ELE_RESOURCE_ID" jdbcType="VARCHAR" property="eleResourceId" />
        <result column="ELE_EXT_JSON" jdbcType="VARCHAR" property="eleExtJson" />
        <result column="ELE_COLUMN_JSON" jdbcType="VARCHAR" property="eleColumnJson" />
        <result column="APP_TAG" jdbcType="VARCHAR" property="appTag" />
        <result column="ELE_EDA_NO" jdbcType="VARCHAR" property="eleEdaNo" />
        <result column="OPT_LINK_ID" jdbcType="INTEGER" property="optLinkId" />
        <result column="EDA_LINK_LOC" jdbcType="BIGINT" property="edaLinkLoc" />
        <result column="IS_DEL" jdbcType="TINYINT" property="isDel" />
        <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime" />
        <result column="LAST_MODIFIED_TIME" jdbcType="TIMESTAMP" property="lastModifiedTime" />
        <result column="NOTE" jdbcType="VARCHAR" property="note" />
        <result column="ELE_RESOURCE_NAME" jdbcType="VARCHAR" property="eleResourceName" />
        <result column="ELE_EXT_FIELD" jdbcType="VARCHAR" property="eleExtField" />
        <result column="ELE_EXT_FIELD_VAL" jdbcType="VARCHAR" property="eleExtFieldVal" />
        <result column="BP_PARENT_MXCELL_IDS" jdbcType="VARCHAR" property="bpParentMxcellIds" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        BURYPOINTS_EDA_POINTS_ID, EDA_MXCELL_ID, EDA_MXCELL_PARENT_ID, EDA_MXCELL_SOURCE_ID, EDA_MXCELL_TARGET_ID,
            ELE_RESOURCE_ID, ELE_EXT_JSON, ELE_COLUMN_JSON, APP_TAG, ELE_EDA_NO, OPT_LINK_ID, EDA_LINK_LOC, IS_DEL, CREATED_TIME,
            LAST_MODIFIED_TIME, NOTE, ELE_RESOURCE_NAME, ELE_EXT_FIELD, ELE_EXT_FIELD_VAL
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List" />
        from t_burypoints_eda_points
        where BURYPOINTS_EDA_POINTS_ID = #{burypointsEdaPointsId,jdbcType=BIGINT}
    </select>

    <select id="selectByMxcellId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List" />
        from t_burypoints_eda_points
        where EDA_MXCELL_ID = #{edaMxcellId} and OPT_LINK_ID = #{optLinkId} and IS_DEL = 0
    </select>

    <insert id="batchInsert">
        INSERT INTO T_BURYPOINTS_EDA_POINTS(BURYPOINTS_EDA_POINTS_ID, EDA_MXCELL_ID, EDA_MXCELL_PARENT_ID, EDA_MXCELL_SOURCE_ID, EDA_MXCELL_TARGET_ID,
        ELE_RESOURCE_ID, ELE_EXT_JSON, ELE_COLUMN_JSON, APP_TAG, ELE_EDA_NO, OPT_LINK_ID, EDA_LINK_LOC,  NOTE, ELE_RESOURCE_NAME, ELE_EXT_FIELD, ELE_EXT_FIELD_VAL,EDA_MXCELL_TYPE,BP_PARENT_MXCELL_IDS, BURYPOINTS_ELE_COLUMN_ID)
        VALUES
        <foreach collection="list" item="bean" separator="," >
            (#{bean.burypointsEdaPointsId}, #{bean.edaMxcellId},#{bean.edaMxcellParentId},#{bean.edaMxcellSourceId},#{bean.edaMxcellTargetId},
             #{bean.eleResourceId},#{bean.eleExtJson},#{bean.eleColumnJson},#{bean.appTag},#{bean.eleEdaNo},#{bean.optLinkId},#{bean.edaLinkLoc},#{bean.note},#{bean.eleResourceName},
             #{bean.eleExtField},#{bean.eleExtFieldVal},#{bean.edaMxcellType},#{bean.bpParentMxcellIds}, #{bean.burypointsEleColumnId})
        </foreach>
    </insert>

    <update id="delByOptLinkId">
        UPDATE T_BURYPOINTS_EDA_POINTS set IS_DEL = 1, LAST_MODIFIED_TIME=now(), note='批量新增时删除老数据'
        where OPT_LINK_ID=#{optLinkId} and is_del=0
    </update>

    <select id="selectBpMxcellIdByOptLinkId" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select distinct EDA_MXCELL_ID ,BP_PARENT_MXCELL_IDS , OPT_LINK_ID, EDA_MXCELL_TYPE
        from t_burypoints_eda_points
        where OPT_LINK_ID = #{optLinkId} and IS_DEL = 0 and nvl(BP_PARENT_MXCELL_IDS,'') !=''
    </select>

    <select id="queryList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from t_burypoints_eda_points
        WHERE BURYPOINTS_EDA_POINTS_ID IN
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

</mapper>