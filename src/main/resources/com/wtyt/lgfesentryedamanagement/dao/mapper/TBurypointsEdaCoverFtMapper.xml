<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtyt.lgfesentryedamanagement.dao.mapper.TBurypointsEdaCoverFtMapper">

    <insert id="batchInsert">
        INSERT INTO T_BURYPOINTS_EDA_COVER_FT(BURYPOINTS_EDA_COVER_FT_ID, EDA_MXCELL_ID, OPT_LINK_ID, APP_TAG, ELE_RESOURCE_ID,
        ELE_EXT_FIELD, ELE_EXT_FIELD_VAL, ELE_EXT_JSON, ELE_COLUMN_JSON, BURYPOINTS_ELE_COLUMN_ID)
        VALUES
        <foreach collection="list" item="bean" separator="," >
            (#{bean.burypointsEdaCoverFtId}, #{bean.edaMxcellId},#{bean.optLinkId},#{bean.appTag},#{bean.eleResourceId},
             #{bean.eleExtField},#{bean.eleExtFieldVal},#{bean.eleExtJson},#{bean.eleColumnJson},#{bean.burypointsEleColumnId})
        </foreach>
    </insert>

    <update id="delByOptLinkId">
        UPDATE T_BURYPOINTS_EDA_COVER_FT SET IS_DEL = 1, LAST_MODIFIED_TIME=now(), note='批量新增时删除老数据'
        WHERE OPT_LINK_ID=#{optLinkId} AND IS_DEL = 0
    </update>
    <select id="queryList" resultType="com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaCoverFt">
        SELECT BURYPOINTS_EDA_COVER_FT_ID, EDA_MXCELL_ID, OPT_LINK_ID, APP_TAG, ELE_RESOURCE_ID,
        ELE_EXT_FIELD, ELE_EXT_FIELD_VAL, ELE_EXT_JSON, ELE_COLUMN_JSON
        FROM T_BURYPOINTS_EDA_COVER_FT 
        WHERE BURYPOINTS_EDA_COVER_FT_ID IN
        <foreach collection="list" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

</mapper>