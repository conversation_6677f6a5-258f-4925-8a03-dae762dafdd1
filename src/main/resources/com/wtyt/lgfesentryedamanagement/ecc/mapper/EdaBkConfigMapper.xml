<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wtyt.lgfesentryedamanagement.ecc.mapper.EdaBkConfigMapper">

    <sql id="Bk_Base_Column_List">
        ebc.BURYPOINTS_EDA_BK_CONFIG_ID, ebc.EDA_NO, ebc.ES_INDEX_NAME, ebc.INTERFACE_PROJECT_NAME, ebc.INTERFACE_TAG,
        ebc.INTERFACE_TYPE, ebc.CAL_TYPE, ebc.FILTER_TYPE, ebc.LOG_FILTERS_KEYWORDS, ebc.INTERFACE_FILTERS, ebc.IS_DEL,
        ebc.CREATED_TIME, ebc.LAST_MODIFIED_TIME, ebc.NOTE, ebc.LOG_FILTERS_EXCLUDE_KEYWORDS, ebc.INTERFACE_INDEX_NAME,
        ebc.INTERFACE_EXCLUDE_FILTERS, ebc.LINK_LOCATION, ebc.EDA_BRANCH,
        NULL AS ELE_RESOURCE_ID,
        NULL AS ELE_EXT_FIELD,
        NULL AS ELE_EXT_FIELD_VAL,
        '1' as CONFIG_TYPE
    </sql>

    <sql id="Ft_Base_Column_List">
        ebc.BURYPOINTS_EDA_FT_CONFIG_ID AS BURYPOINTS_EDA_BK_CONFIG_ID, ebc.EDA_NO,
        NULL AS ES_INDEX_NAME,
        NULL AS INTERFACE_PROJECT_NAME,
        NULL AS INTERFACE_TAG,
        NULL AS INTERFACE_TYPE,
        NULL AS CAL_TYPE,
        NULL AS FILTER_TYPE,
        NULL AS LOG_FILTERS_KEYWORDS,
        NULL AS INTERFACE_FILTERS,
        ebc.IS_DEL,
        ebc.CREATED_TIME,
        ebc.LAST_MODIFIED_TIME,
        ebc.NOTE,
        NULL AS LOG_FILTERS_EXCLUDE_KEYWORDS,
        NULL AS INTERFACE_INDEX_NAME,
        NULL AS INTERFACE_EXCLUDE_FILTERS,
        ebc.LINK_LOCATION,
        ebc.EDA_BRANCH,
        ebc.ELE_RESOURCE_ID,
        ebc.ELE_EXT_FIELD,
        ebc.ELE_EXT_FIELD_VAL,
        '2' as CONFIG_TYPE
    </sql>

    <select id="edaBkConfigList" resultType="com.wtyt.lgfesentryedamanagement.ecc.bean.do_.EdaBkConfigDo" parameterType="com.wtyt.lgfesentryedamanagement.ecc.bean.param.EdaBkConfigListParam">
        SELECT  * FROM (

            SELECT
               <include refid="Bk_Base_Column_List"/>,em.MAINTENANCE_TEAM,em.EDA_NAME, em.EDA_ABBREVE_NAME, branch.EDA_NAME AS 'edaBranchName'
            FROM
                T_BURYPOINTS_EDA_BK_CONFIG ebc
            INNER JOIN T_BURYPOINTS_EDA_MASTER em ON ebc.EDA_NO = em.EDA_NO
            LEFT JOIN T_BURYPOINTS_EDA branch ON branch.EDA_NO = ebc.EDA_BRANCH AND branch.IS_DEL = 0
            WHERE
                ebc.IS_DEL = #{isDel}
                AND em.IS_DEL = 0
                <if test="edaNo != null and edaNo != ''">
                    AND ebc.EDA_NO LIKE CONCAT('%', #{edaNo} ,'%')
                </if>
                <if test="edaName != null and edaName != ''">
                    AND em.EDA_NAME LIKE CONCAT('%', #{edaName} ,'%')
                </if>
                <if test="edaAbbreveName != null and edaAbbreveName != ''">
                    AND em.EDA_ABBREVE_NAME LIKE CONCAT('%', #{edaAbbreveName} ,'%')
                </if>
                <if test="maintenanceTeam != null and maintenanceTeam != ''">
                    AND em.MAINTENANCE_TEAM = #{maintenanceTeam}
                </if>
                <if test="edaBranch != null and edaBranch != ''">
                    AND ebc.EDA_BRANCH LIKE CONCAT('%', #{edaBranch} ,'%')
                </if>
                <if test="interfaceProjectName != null and interfaceProjectName != ''">
                    AND ebc.INTERFACE_PROJECT_NAME LIKE CONCAT('%', #{interfaceProjectName} ,'%')
                </if>
                <if test="interfaceTag != null and interfaceTag != ''">
                    AND ebc.INTERFACE_TAG LIKE CONCAT('%', #{interfaceTag} ,'%')
                </if>
                <if test="linkLocation != null and linkLocation != ''">
                    AND ebc.LINK_LOCATION = #{linkLocation}
                </if>

            UNION ALL

            SELECT
            <include refid="Ft_Base_Column_List"/>,em.MAINTENANCE_TEAM,em.EDA_NAME, em.EDA_ABBREVE_NAME, branch.EDA_NAME AS 'edaBranchName'
            FROM
            T_BURYPOINTS_EDA_FT_CONFIG ebc
            INNER JOIN T_BURYPOINTS_EDA_MASTER em ON ebc.EDA_NO = em.EDA_NO
            LEFT JOIN T_BURYPOINTS_EDA branch ON branch.EDA_NO = ebc.EDA_BRANCH AND branch.IS_DEL = 0
            WHERE
            ebc.IS_DEL = #{isDel}
            AND em.IS_DEL = 0
            <if test="edaNo != null and edaNo != ''">
                AND ebc.EDA_NO LIKE CONCAT('%', #{edaNo} ,'%')
            </if>
            <if test="edaName != null and edaName != ''">
                AND em.EDA_NAME LIKE CONCAT('%', #{edaName} ,'%')
            </if>
            <if test="edaAbbreveName != null and edaAbbreveName != ''">
                AND em.EDA_ABBREVE_NAME LIKE CONCAT('%', #{edaAbbreveName} ,'%')
            </if>
            <if test="maintenanceTeam != null and maintenanceTeam != ''">
                AND em.MAINTENANCE_TEAM = #{maintenanceTeam}
            </if>
            <if test="edaBranch != null and edaBranch != ''">
                AND ebc.EDA_BRANCH LIKE CONCAT('%', #{edaBranch} ,'%')
            </if>
            <if test="interfaceProjectName != null and interfaceProjectName != ''">
                <!--这个是后端覆盖率的查询条件，如果填了，则查不到前端覆盖率的数据 -->
                AND 1=0
            </if>
            <if test="interfaceTag != null and interfaceTag != ''">
                <!--这个是后端覆盖率的查询条件，如果填了，则查不到前端覆盖率的数据 -->
                AND 1=0
            </if>
            <if test="linkLocation != null and linkLocation != ''">
                AND ebc.LINK_LOCATION = #{linkLocation}
            </if>
        ) T ORDER BY CREATED_TIME DESC, BURYPOINTS_EDA_BK_CONFIG_ID DESC
    </select>
</mapper>