spring:
  #数据库配置
  test:
    datasource:
      lgfem:
        # 使用董动态数据库
        dynamic: true
        dbType: obmysql
        # 开启了动态数据库则最后面下面的五个配置可以不填(填了也没有用)
        schema: sit_lgfem
        type: com.alibaba.druid.pool.DruidDataSource
        url: ****************************************************************
        username: u_sit_lgfem
        password: 'FmUvLmF5LVI2W9Uu'
    
#日志配置
logging:
  config: classpath:config/log4j2-dev.xml

sftp:
  host: **************
  port: 22
  username: info
  password: PBfgj93Info
  path: /home/<USER>/syf/webapps/projects