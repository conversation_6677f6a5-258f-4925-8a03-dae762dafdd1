package com.wtyt.lgfesentryedamanagement.testDev.ecc;

import com.wtyt.lgfesentryedamanagement.ecc.bean.param.EdaBkConfigListParam;
import com.wtyt.lgfesentryedamanagement.ecc.bean.param.EdaBkConfigParam;
import com.wtyt.lgfesentryedamanagement.ecc.bean.vo.EdaBkConfigVo;
import com.wtyt.lgfesentryedamanagement.ecc.service.BurypointsEdaBkConfigService;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545021IBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545022IBean;
import com.wtyt.lgfesentryedamanagement.eda.service.BurypointsEdaYidaService;
import com.wtyt.lgfesentryedamanagement.pub.bean.PageVo;
import com.wtyt.lgfesentryedamanagement.testDev.DevBaseTest;
import com.wtyt.qst.util.util.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.Map;

public class BurypointsEdaBkConfigServiceTest extends DevBaseTest {

    @Autowired
    private BurypointsEdaBkConfigService burypointsEdaBkConfigService;

    @Test
    public void addEdaBkConfig() throws Exception {
        EdaBkConfigParam edaBkConfigParam = new EdaBkConfigParam();
        edaBkConfigParam.setEdaNo("EDA-TEST-001");
        edaBkConfigParam.setEdaBranch("EDA-TEST-001-001");
        edaBkConfigParam.setInterfaceTag("0000000");
        edaBkConfigParam.setInterfaceProjectName("ceshi-2014");
        edaBkConfigParam.setInterfaceType("0");
        edaBkConfigParam.setInterfaceIndexName("namename");
        edaBkConfigParam.setLinkLocation("1");
        edaBkConfigParam.setEdaBranch("WLHY-ZH-001-002");//
        edaBkConfigParam.setOptTeam("TCO");
        edaBkConfigParam.setOptUserId("123456");
        edaBkConfigParam.setOptUserName("测试用户");
        edaBkConfigParam.setConfigType("2");
        edaBkConfigParam.setEleResourceId("abcdefg-111111");
        edaBkConfigParam.setEleExtField("aaa");
        edaBkConfigParam.setEleExtFieldVal("bbb1234");
        burypointsEdaBkConfigService.addEdaBkConfig(edaBkConfigParam);
    }

    @Test
    public void editEdaBkConfig() throws Exception {
        EdaBkConfigParam edaBkConfigParam = new EdaBkConfigParam();
        edaBkConfigParam.setBurypointsEdaBkConfigId("729856300235980818");
        edaBkConfigParam.setEdaNo("EDA-FL-TEST-001");
        edaBkConfigParam.setEdaBranch("FL-TEST-002");
        edaBkConfigParam.setInterfaceTag("1234");
        edaBkConfigParam.setInterfaceProjectName("ceshi-2014");
        edaBkConfigParam.setInterfaceType("0");
        edaBkConfigParam.setInterfaceIndexName("namename");
        edaBkConfigParam.setLinkLocation("1");
        edaBkConfigParam.setFilterType("1");
        edaBkConfigParam.setOptTeam("TCO");
        edaBkConfigParam.setOptUserId("123456");
        edaBkConfigParam.setOptUserName("测试用户");

        edaBkConfigParam.setEleResourceId("abcdefg-22222");
        edaBkConfigParam.setEleExtField("vvv");
        edaBkConfigParam.setEleExtFieldVal("ccc1234");

        burypointsEdaBkConfigService.editEdaBkConfig(edaBkConfigParam);
    }


    @Test
    public void edaBkConfigList() throws Exception {
        EdaBkConfigListParam edaBkConfigListParam = new EdaBkConfigListParam();
        edaBkConfigListParam.setLinkLocation("1");
        edaBkConfigListParam.setIsDel("0");
        edaBkConfigListParam.setEdaNo("EDA-TEST-001");
        PageVo<EdaBkConfigVo> result =  burypointsEdaBkConfigService.edaBkConfigList(edaBkConfigListParam);
        System.out.println("====" + JsonUtil.objectToJson(result));
    }

    @Test
    public void queryCoverRateInterfaceFilters() throws IOException {

        Map result =  burypointsEdaBkConfigService.queryCoverRateInterfaceFilters();
        System.out.println("====" + JsonUtil.objectToJson(result));
    }

}
