package com.wtyt.lgfesentryedamanagement.testDev.bkc;

import com.wtyt.generator.toolkit.UidToolkit;
import com.wtyt.lgfesentryedamanagement.bkc.bean.param.Req5545009IBean;
import com.wtyt.lgfesentryedamanagement.bkc.bean.param.Req5545031IBean;
import com.wtyt.lgfesentryedamanagement.bkc.bean.response.Req5545020OBean;
import com.wtyt.lgfesentryedamanagement.bkc.bean.response.Req5545032OBean;
import com.wtyt.lgfesentryedamanagement.bkc.impl.BkcCalServiceImpl;
import com.wtyt.lgfesentryedamanagement.bkc.utils.BkcMakerToolkits;
import com.wtyt.lgfesentryedamanagement.ecc.bean.param.EdaBkConfigListParam;
import com.wtyt.lgfesentryedamanagement.ecc.bean.param.EdaBkConfigParam;
import com.wtyt.lgfesentryedamanagement.ecc.bean.vo.EdaBkConfigVo;
import com.wtyt.lgfesentryedamanagement.ecc.service.BurypointsEdaBkConfigService;
import com.wtyt.lgfesentryedamanagement.pub.bean.PageVo;
import com.wtyt.lgfesentryedamanagement.testDev.DevBaseTest;
import com.wtyt.qst.util.util.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Calendar;

public class BkcCalServiceTest extends DevBaseTest {

    @Autowired
    private BkcCalServiceImpl bkcCalServiceImpl;
    @Autowired
    private BkcCalServiceImpl service;

    @Test
    public void getDetectedDetail() throws Exception {
        Req5545031IBean data = new Req5545031IBean();
        data.setSearchId("946831502145011712");
        Req5545032OBean req5545032OBean = bkcCalServiceImpl.getDetectedDetail(data);
        System.out.println("==========" + JsonUtil.objectToJson(req5545032OBean));
    }
    @Test
    public void getDetectedCoverRate() throws Exception {
        Req5545009IBean edaBkConfigParam = new Req5545009IBean();
        edaBkConfigParam.setEdaNo("YD_FINST-K7D66L91T16KZRUO6H5TW5BYAZS73KY7C9WULYM9");
        edaBkConfigParam.setLinkLocation("0");
        edaBkConfigParam.setStartTime("2024-08-14 16:35:00");
        edaBkConfigParam.setEndTime("2024-08-14 16:43:57");
        edaBkConfigParam.setOptTeam("TCO");
        edaBkConfigParam.setOptUserId("23");
        edaBkConfigParam.setOptUserName("测试用户");
        bkcCalServiceImpl.getDetectedCoverRate(edaBkConfigParam);
        Thread.sleep(2000000);
    }

    @Test
    public void getColverRateList() throws Exception {
        Req5545009IBean edaBkConfigParam = new Req5545009IBean();
        edaBkConfigParam.setEdaNo("YSPZ-002");
        edaBkConfigParam.setLinkLocation("1");
        edaBkConfigParam.setStartTime("2024-02-02 12:11:21");
        edaBkConfigParam.setEndTime("2024-03-02 12:11:21");
        edaBkConfigParam.setOptTeam("TCO");
        edaBkConfigParam.setOptUserId("23");
        edaBkConfigParam.setOptUserName("测试用户");
        Req5545020OBean result = bkcCalServiceImpl.getColverRateList(edaBkConfigParam);
        System.out.println("========" + JsonUtil.objectToJson(result));
    }


    @Test
    public void calEveryCoverJob() throws Exception {
        String reqJson = "{\"data\":{\"calDate\" : \"20240511\", \"linkLocation\" : \"1\"}}";
        Calendar calDate = BkcMakerToolkits.analysisEveryDayTime(reqJson);
        String linkLocation = BkcMakerToolkits.analysisLinkLocation(reqJson);
        String result = bkcCalServiceImpl.calEveryCoverJob(calDate, linkLocation);
        System.out.println("========" + result);
        Thread.sleep(500000);
    }
    @Test
    public void calNewEveryCoverJob() throws Exception {
        String reqJson = "{\"data\":{\"calDate\" : \"20250304\"}}";
        Calendar calDate = BkcMakerToolkits.analysisEveryDayTime(reqJson);
        String result = bkcCalServiceImpl.calNewEveryCoverJob(calDate);
        System.out.println("========" + result);
        Thread.sleep(500000);
    }


    @Test
    public void toDetected() throws Exception {
        Req5545031IBean data = new Req5545031IBean();
        data.setEndTime("2025-03-06 23:59:59");
        data.setStartTime("2025-03-06 00:00:00");
        data.setOptLinkId("FINST-SNB667B17VAJ95RVCJLIK4CIQ6HF3W3JQWWTLGJG");
        data.setEnv("FAT");
        List<String> abclist = new ArrayList<String>();
        abclist.add("覆盖率");
        data.setSearchItem(abclist);
        bkcCalServiceImpl.toDetected(data);
    }

}
