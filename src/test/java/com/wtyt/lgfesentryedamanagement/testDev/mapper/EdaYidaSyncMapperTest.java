package com.wtyt.lgfesentryedamanagement.testDev.mapper;

import com.wtyt.lgfesentryedamanagement.dao.mapper.EdaYidaSyncMapper;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.JsonToolkit;
import com.wtyt.lgfesentryedamanagement.testDev.DevBaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class EdaYidaSyncMapperTest extends DevBaseTest {

    @Resource
    private EdaYidaSyncMapper edaYidaSyncMapper;

    @Test
    public void test_allColumns() {
        List<Map<String, String>> tForm0839E2AFD76A492E8A8 = edaYidaSyncMapper.allColumnsFromTableWithShow("T_FORM_0839E2AFD76A492E8A8");
        System.out.println(JsonToolkit.objectToJson(tForm0839E2AFD76A492E8A8));
    }

    @Test
    public void test_insertData() {
        String tableName = "t_maxinchun_test";
        Map<String, String> dataMap = new LinkedHashMap<>();
        dataMap.put("test_1", "test\n\"123'3");
        dataMap.put("test_json", "");
        dataMap.put("form_instance_id", "123");
        int result = edaYidaSyncMapper.insertData(tableName, dataMap);
        System.out.println(result);
    }

    @Test
    public void test_updateByInstanceId() {
        String tableName = "t_maxinchun_test";
        String formInstanceId = "123";
        Map<String, String> dataMap = new LinkedHashMap<>();
        dataMap.put("test_1", "test\n\"123'4");
        dataMap.put("test_json", "[{\"pid\": \"5H9662C'19N0JR2Q38OOBA4ENM8DO2QVA9SCTL9G91\", \"title\": \"资金和账户管理 （运费账户，账户明细）\", \"appType\": \"APP_B4YS2RVCKTP8NI5V14W7\", \"formType\": \"receipt\", \"formUuid\": \"FORM-26442A65A42743B0AFB0CDDB80DE92D54AYV\", \"subTitle\": \"\", \"instanceId\": \"FINST-XA866RD10U3I1OY6DCF3V6XYNAYE2NBRI8YSLJVT\"}, {\"pid\": \"5H9662C19N0JR2Q38OOBA4ENM8DO2QVA9SCTLAG91\", \"title\": \"账务管理（规划中）\", \"appType\": \"APP_B4YS2RVCKTP8NI5V14W7\", \"formType\": \"receipt\", \"formUuid\": \"FORM-26442A65A42743B0AFB0CDDB80DE92D54AYV\", \"subTitle\": \"\", \"instanceId\": \"FINST-XA866RD10U3I1OY6DCF3V6XYNAYE2NBRI8YSLNVT\"}, {\"pid\": \"5H9662C19N0JR2Q38OOBA4ENM8DO2QVA9SCTLBG91\", \"title\": \"运作须知管理 （归到运作过程管理里面？）\", \"appType\": \"APP_B4YS2RVCKTP8NI5V14W7\", \"formType\": \"receipt\", \"formUuid\": \"FORM-26442A65A42743B0AFB0CDDB80DE92D54AYV\", \"subTitle\": \"\", \"instanceId\": \"FINST-XA866RD10U3I1OY6DCF3V6XYNAYE2NBRI8YSLQVT\"}, {\"pid\": \"5H9662C19N0JR2Q38OOBA4ENM8DO2QVA9SCTLCG91\", \"title\": \"运作费用管理\", \"appType\": \"APP_B4YS2RVCKTP8NI5V14W7\", \"formType\": \"receipt\", \"formUuid\": \"FORM-26442A65A42743B0AFB0CDDB80DE92D54AYV\", \"subTitle\": \"\", \"instanceId\": \"FINST-XA866RD10U3I1OY6DCF3V6XYNAYE2NBRI8YSLRVT\"}, {\"pid\": \"5H9662C19N0JR2Q38OOBA4ENM8DO2QVA9SCTLDG91\", \"title\": \"运营任务管理\", \"appType\": \"APP_B4YS2RVCKTP8NI5V14W7\", \"formType\": \"receipt\", \"formUuid\": \"FORM-26442A65A42743B0AFB0CDDB80DE92D54AYV\", \"subTitle\": \"\", \"instanceId\": \"FINST-XA866RD10U3I1OY6DCF3V6XYNAYE2NBRI8YSLSVT\"}, {\"pid\": \"5H9662C19N0JR2Q38OOBA4ENM8DO2QVA9SCTLEG91\", \"title\": \"摇号派车？？--运力资源管理\", \"appType\": \"APP_B4YS2RVCKTP8NI5V14W7\", \"formType\": \"receipt\", \"formUuid\": \"FORM-26442A65A42743B0AFB0CDDB80DE92D54AYV\", \"subTitle\": \"\", \"instanceId\": \"FINST-XA866RD10U3I1OY6DCF3V6XYNAYE2NBRI8YSL6WT\"}, {\"pid\": \"5H9662C19N0JR2Q38OOBA4ENM8DO2QVA9SCTLFG91\", \"title\": \"消息中心\", \"appType\": \"APP_B4YS2RVCKTP8NI5V14W7\", \"formType\": \"receipt\", \"formUuid\": \"FORM-26442A65A42743B0AFB0CDDB80DE92D54AYV\", \"subTitle\": \"用户在系统中的收到的各种消息推送\", \"instanceId\": \"FINST-G9766T716VFIM4C9EDN8DDJ4FWUV2OEXP8YSLM581\"}, {\"pid\": \"5H9662C19N0JR2Q38OOBA4ENM8DO2QVA9SCTLGG91\", \"title\": \"项目管理 （客户管理？/企业管理？/项目管理与配置？）\", \"appType\": \"APP_B4YS2RVCKTP8NI5V14W7\", \"formType\": \"receipt\", \"formUuid\": \"FORM-26442A65A42743B0AFB0CDDB80DE92D54AYV\", \"subTitle\": \"\", \"instanceId\": \"FINST-XA866RD10U3I1OY6DCF3V6XYNAYE2NBRI8YSLDWT\"}, {\"pid\": \"5H9662C19N0JR2Q38OOBA4ENM8DO2QVA9SCTLHG91\", \"title\": \"项目管理 （业务项目管理）\", \"appType\": \"APP_B4YS2RVCKTP8NI5V14W7\", \"formType\": \"receipt\", \"formUuid\": \"FORM-26442A65A42743B0AFB0CDDB80DE92D54AYV\", \"subTitle\": \"\", \"instanceId\": \"FINST-XA866RD10U3I1OY6DCF3V6XYNAYE2NBRI8YSLCWT\"}, {\"pid\": \"5H9662C19N0JR2Q38OOBA4ENM8DO2QVA9SCTLIG91\", \"title\": \"线路管理\", \"appType\": \"APP_B4YS2RVCKTP8NI5V14W7\", \"formType\": \"receipt\", \"formUuid\": \"FORM-26442A65A42743B0AFB0CDDB80DE92D54AYV\", \"subTitle\": \"\", \"instanceId\": \"FINST-XA866RD10U3I1OY6DCF3V6XYNAYE2NBRI8YSLEWT\"}, {\"pid\": \"5H9662C19N0JR2Q38OOBA4ENM8DO2QVA9SCTLJG91\", \"title\": \"系统开屏页\", \"appType\": \"APP_B4YS2RVCKTP8NI5V14W7\", \"formType\": \"receipt\", \"formUuid\": \"FORM-26442A65A42743B0AFB0CDDB80DE92D54AYV\", \"subTitle\": \"PC端打开系统未登陆时展示的除了登陆相关的页面。\\nAPP端启动APP后进入APP前的开屏页，广告页等\", \"instanceId\": \"FINST-XA866RD10U3I1OY6DCF3V6XYNAYE2NBRI8YSLFWT\"}, {\"pid\": \"5H9662C19N0JR2Q38OOBA4ENM8DO2QVA9SCTLKG91\", \"title\": \"随车清单管理\", \"appType\": \"APP_B4YS2RVCKTP8NI5V14W7\", \"formType\": \"receipt\", \"formUuid\": \"FORM-26442A65A42743B0AFB0CDDB80DE92D54AYV\", \"subTitle\": \"\", \"instanceId\": \"FINST-XA866RD10U3I1OY6DCF3V6XYNAYE2NBRI8YSLKWT\"}, {\"pid\": \"5H9662C19N0JR2Q38OOBA4ENM8DO2QVA9SCTLLG91\", \"title\": \"收支汇总\", \"appType\": \"APP_B4YS2RVCKTP8NI5V14W7\", \"formType\": \"receipt\", \"formUuid\": \"FORM-26442A65A42743B0AFB0CDDB80DE92D54AYV\", \"subTitle\": \"\", \"instanceId\": \"FINST-XA866RD10U3I1OY6DCF3V6XYNAYE2NBRI8YSLQWT\"}, {\"pid\": \"5H9662C19N0JR2Q38OOBA4ENM8DO2QVA9SCTLMG91\", \"title\": \"审批中心\", \"appType\": \"APP_B4YS2RVCKTP8NI5V14W7\", \"formType\": \"receipt\", \"formUuid\": \"FORM-26442A65A42743B0AFB0CDDB80DE92D54AYV\", \"subTitle\": \"企业在运作的各环节过程需要的审批流程相关的功能，与钉钉的OA审批类似\", \"instanceId\": \"FINST-XA866RD10U3I1OY6DCF3V6XYNAYE2NBRI8YSLRWT\"}, {\"pid\": \"5H9662C19N0JR2Q38OOBA4ENM8DO2QVA9SCTLNG91\", \"title\": \"权限管理\", \"appType\": \"APP_B4YS2RVCKTP8NI5V14W7\", \"formType\": \"receipt\", \"formUuid\": \"FORM-26442A65A42743B0AFB0CDDB80DE92D54AYV\", \"subTitle\": \"包含行为权限和数据权限，行为权限是用户在系统中能使用企业开通的什么功能和字段，数据权限是用户能在企业下看到哪些数据\", \"instanceId\": \"FINST-G9766T716VFIM4C9EDN8DDJ4FWUV2OEXP8YSLP581\"}, {\"pid\": \"5H9662C19N0JR2Q38OOBA4ENM8DO2QVA9SCTLOG91\", \"title\": \"结算管理\", \"appType\": \"APP_B4YS2RVCKTP8NI5V14W7\", \"formType\": \"receipt\", \"formUuid\": \"FORM-26442A65A42743B0AFB0CDDB80DE92D54AYV\", \"subTitle\": \"使用各种方式结算运费给需要收取运费的各角色相关的功能\", \"instanceId\": \"FINST-G9766T716VFIM4C9EDN8DDJ4FWUV2OEXP8YSLQ581\"}, {\"pid\": \"5H9662C19N0JR2Q38OOBA4ENM8DO2QVA9SCTLPG91\", \"title\": \"会员认证\", \"appType\": \"APP_B4YS2RVCKTP8NI5V14W7\", \"formType\": \"receipt\", \"formUuid\": \"FORM-26442A65A42743B0AFB0CDDB80DE92D54AYV\", \"subTitle\": \"对承运司机的身份和车辆资质进行认证相关的功能\", \"instanceId\": \"FINST-G9766T716VFIM4C9EDN8DDJ4FWUV2OEXP8YSLS581\"}, {\"pid\": \"5H9662C19N0JR2Q38OOBA4ENM8DO2QVA9SCTLQG91\", \"title\": \"风控合规？？ 前置运营？？\", \"appType\": \"APP_B4YS2RVCKTP8NI5V14W7\", \"formType\": \"receipt\", \"formUuid\": \"FORM-26442A65A42743B0AFB0CDDB80DE92D54AYV\", \"subTitle\": \"\", \"instanceId\": \"FINST-XA866RD10U3I1OY6DCF3V6XYNAYE2NBRI8YSLJXT\"}, {\"pid\": \"5H9662C19N0JR2Q38OOBA4ENM8DO2QVA9SCTLRG91\", \"title\": \"地址簿管理\", \"appType\": \"APP_B4YS2RVCKTP8NI5V14W7\", \"formType\": \"receipt\", \"formUuid\": \"FORM-26442A65A42743B0AFB0CDDB80DE92D54AYV\", \"subTitle\": \"对装卸货地点，仓库等的详细地址的管理\", \"instanceId\": \"FINST-XA866RD10U3I1OY6DCF3V6XYNAYE2NBRI8YSLMXT\"}, {\"pid\": \"5H9662C19N0JR2Q38OOBA4ENM8DO2QVA9SCTLSG91\", \"title\": \"运力资源管理\", \"appType\": \"APP_B4YS2RVCKTP8NI5V14W7\", \"formType\": \"receipt\", \"formUuid\": \"FORM-26442A65A42743B0AFB0CDDB80DE92D54AYV\", \"subTitle\": \"\", \"instanceId\": \"FINST-XA866RD10U3I1OY6DCF3V6XYNAYE2NBRI8YSLOXT\"}, {\"pid\": \"5H9662C19N0JR2Q38OOBA4ENM8DO2QVA9SCTLTG91\", \"title\": \"票据管理\", \"appType\": \"APP_B4YS2RVCKTP8NI5V14W7\", \"formType\": \"receipt\", \"formUuid\": \"FORM-26442A65A42743B0AFB0CDDB80DE92D54AYV\", \"subTitle\": \"企业完成运输业务后，在平台完成开票相关的功能\", \"instanceId\": \"FINST-G9766T716VFIM4C9EDN8DDJ4FWUV2OEXP8YSLU581\"}, {\"pid\": \"5H9662C19N0JR2Q38OOBA4ENM8DO2QVA9SCTLUG91\", \"title\": \"运输凭证管理\", \"appType\": \"APP_B4YS2RVCKTP8NI5V14W7\", \"formType\": \"receipt\", \"formUuid\": \"FORM-26442A65A42743B0AFB0CDDB80DE92D54AYV\", \"subTitle\": \"运输过程中产生的各种凭证图片或纸质文件的管理，比如磅单，回单，车头照等\", \"instanceId\": \"FINST-G9766T716VFIM4C9EDN8DDJ4FWUV2OEXP8YSLV581\"}, {\"pid\": \"5H9662C19N0JR2Q38OOBA4ENM8DO2RVA9SCTLVG91\", \"title\": \"运作过程管理\", \"appType\": \"APP_B4YS2RVCKTP8NI5V14W7\", \"formType\": \"receipt\", \"formUuid\": \"FORM-26442A65A42743B0AFB0CDDB80DE92D54AYV\", \"subTitle\": \"又称为：运作流程数字化\\n物流运输过程中的各个环节的协同动作与方式的管理\", \"instanceId\": \"FINST-XA866RD10U3I1OY6DCF3V6XYNAYE2NBRI8YSLSXT\"}, {\"pid\": \"5H9662C19N0JR2Q38OOBA4ENM8DO2RVA9SCTLWG91\", \"title\": \"账号设置\", \"appType\": \"APP_B4YS2RVCKTP8NI5V14W7\", \"formType\": \"receipt\", \"formUuid\": \"FORM-26442A65A42743B0AFB0CDDB80DE92D54AYV\", \"subTitle\": \"\", \"instanceId\": \"FINST-G9766T716VFIM4C9EDN8DDJ4FWUV2OEXP8YSLW581\"}, {\"pid\": \"5H9662C19N0JR2Q38OOBA4ENM8DO2RVA9SCTLXG91\", \"title\": \"登陆模块\", \"appType\": \"APP_B4YS2RVCKTP8NI5V14W7\", \"formType\": \"receipt\", \"formUuid\": \"FORM-26442A65A42743B0AFB0CDDB80DE92D54AYV\", \"subTitle\": \"使用各种方式登陆系统，退出系统相关的功能。\", \"instanceId\": \"FINST-G9766T716VFIM4C9EDN8DDJ4FWUV2OEXP8YSLX581\"}, {\"pid\": \"5H9662C19N0JR2Q38OOBA4ENM8DO2RVA9SCTLYG91\", \"title\": \"运输计划管理\", \"appType\": \"APP_B4YS2RVCKTP8NI5V14W7\", \"formType\": \"receipt\", \"formUuid\": \"FORM-26442A65A42743B0AFB0CDDB80DE92D54AYV\", \"subTitle\": \"\", \"instanceId\": \"FINST-G9766T716VFIM4C9EDN8DDJ4FWUV2OEXP8YSLY581\"}, {\"pid\": \"5H9662C19N0JR2Q38OOBA4ENM8DO2RVA9SCTLZG91\", \"title\": \"运输任务管理\", \"appType\": \"APP_B4YS2RVCKTP8NI5V14W7\", \"formType\": \"receipt\", \"formUuid\": \"FORM-26442A65A42743B0AFB0CDDB80DE92D54AYV\", \"subTitle\": \"\", \"instanceId\": \"FINST-G9766T716VFIM4C9EDN8DDJ4FWUV2OEXP8YSLZ581\"}, {\"pid\": \"5H9662C19N0JR2Q38OOBA4ENM8DO2RVA9SCTL0H91\", \"title\": \"运输合同管理（运输任务管理）/协议管理（隐私协议：账号设置理解）\", \"appType\": \"APP_B4YS2RVCKTP8NI5V14W7\", \"formType\": \"receipt\", \"formUuid\": \"FORM-26442A65A42743B0AFB0CDDB80DE92D54AYV\", \"subTitle\": \"\", \"instanceId\": \"FINST-XA866RD10U3I1OY6DCF3V6XYNAYE2NBRI8YSLXVT\"}]");
        int result = edaYidaSyncMapper.updateByInstanceId(tableName, formInstanceId, dataMap);
        System.out.println(result);
    }

    @Test
    public void test_logicDelete() {
        String tableName = "t_maxinchun_test";
        String formInstanceId = "123";
        int result = edaYidaSyncMapper.logicDelete(tableName, formInstanceId);
        System.out.println(result);
    }

}
