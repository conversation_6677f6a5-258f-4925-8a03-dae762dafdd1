package com.wtyt.lgfesentryedamanagement.testDev.mapper;

import com.wtyt.generator.toolkit.UidToolkit;
import com.wtyt.lgfesentryedamanagement.dao.bean.TBuryPointsEdaPoints;
import com.wtyt.lgfesentryedamanagement.dao.mapper.TBuryPointsEdaPointsMapper;
import com.wtyt.lgfesentryedamanagement.testDev.DevBaseTest;
import org.junit.Test;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class TBuryPointsEdaPointsMapperTest extends DevBaseTest {

    @Resource
    private TBuryPointsEdaPointsMapper edaPointsMapper;


    @Test
    public void test_batchInsert() {
        List<TBuryPointsEdaPoints> list = new ArrayList<>();
        TBuryPointsEdaPoints bean1 = new TBuryPointsEdaPoints();
        bean1.setBurypointsEdaPointsId(UidToolkit.generateUidDefault());
        bean1.setEleEdaNo("YD_test1");
        bean1.setEdaMxcellId("1");
        bean1.setEdaMxcellParentId("0");
        bean1.setEleResourceId("111");
        bean1.setEleResourceName("1111");
        bean1.setOptLinkId("test1");
        bean1.setEleExtJson("121313");
        bean1.setEdaLinkLoc(1213L);

        TBuryPointsEdaPoints bean2 = new TBuryPointsEdaPoints();
        bean2.setBurypointsEdaPointsId(UidToolkit.generateUidDefault());
        bean2.setEleEdaNo("YD_test1");
        bean2.setOptLinkId("test1");
        bean2.setEleExtJson("121314");
        bean2.setEdaMxcellId("1");
        bean2.setEdaMxcellParentId("0");
        bean2.setEleResourceId("111");
        bean2.setEleResourceName("1111");
        bean2.setEdaLinkLoc(1215L);

        list.add(bean1);
        list.add(bean2);
        int i = edaPointsMapper.batchInsert(list);
        System.out.println(i);
    }

    @Test
    public void test_delByOptLinkId() {
        String optLinkId = "test1";
        int result = edaPointsMapper.delByOptLinkId(optLinkId);
        System.out.println(result);
    }

}
