package com.wtyt.lgfesentryedamanagement.testDev.eda;

import com.wtyt.lgfesentryedamanagement.dao.bean.TBurypointsEdaMaster;
import com.wtyt.lgfesentryedamanagement.dao.mapper.TBurypointsEdaMasterMapper;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.EdaMasterListParam;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545014IParam;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545039IBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.vo.EdaMasterVo;
import com.wtyt.lgfesentryedamanagement.eda.service.BurypointsEdaMasterService;
import com.wtyt.lgfesentryedamanagement.eda.service.BurypointsEdaSyncService;
import com.wtyt.lgfesentryedamanagement.pub.bean.PageVo;
import com.wtyt.lgfesentryedamanagement.testDev.DevBaseTest;
import com.wtyt.qst.util.util.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class BurypointsEdaSyncServiceTest extends DevBaseTest {

    @Autowired
    private BurypointsEdaSyncService burypointsEdaSyncService;
    @Autowired
    private TBurypointsEdaMasterMapper tBurypointsEdaMasterMapper;
    @Autowired
    private BurypointsEdaMasterService burypointsEdaMasterService;


    @Test
    public void edaMasterList() throws Exception {

        EdaMasterListParam edaMasterListParam = new EdaMasterListParam();
        edaMasterListParam.setEdaType("1");
        PageVo<EdaMasterVo> edaList = burypointsEdaMasterService.edaMasterList(edaMasterListParam);
        System.out.println("====" + JsonUtil.objectToJson(edaList));

    }


    @Test
    public void addEdaBkConfig() throws Exception {

        List<String> edaNoList = new ArrayList<>();
        edaNoList.add("YSPZ-001");
        edaNoList.add("YSPZ-002");
        List<TBurypointsEdaMaster> edaList = tBurypointsEdaMasterMapper.queryByEdaNoList(edaNoList, "1");
        System.out.println("====" + JsonUtil.objectToJson(edaList));

    }

    @Test
    public void transMasterEda() throws Exception {

        Req5545014IParam data = new Req5545014IParam();
        data.setOptUserId("23");
        data.setSyncEdaBranch("1");
        data.setEnv("UAT");
        data.setSyncEdaBkConfig("1");
        data.setEdaNoList(Arrays.asList("EDA-TEST-001"));
        data.setOptTeam("TCO");
        data.setOptUserName("xxxx");
        burypointsEdaSyncService.transMasterEda(data);
        //System.out.println("====" + JsonUtil.objectToJson(edaList));

    }


    @Test
    public void syncEagleSystemToEda() throws Exception {
        Req5545039IBean data = new Req5545039IBean();
        data.setOptLinkId("940792374873627242");
        data.setMaintenanceTeam("DO");
        data.setMaintenanceUi("崔晓雪");
        data.setOptLinkName("司机申请支付预付款");
        burypointsEdaSyncService.syncEagleSystemToEda(data);
        //System.out.println("====" + JsonUtil.objectToJson(edaList));

    }
}
