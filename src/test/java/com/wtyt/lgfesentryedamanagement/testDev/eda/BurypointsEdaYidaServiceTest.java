package com.wtyt.lgfesentryedamanagement.testDev.eda;

import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545021IBean;
import com.wtyt.lgfesentryedamanagement.eda.bean.param.Req5545022IBean;
import com.wtyt.lgfesentryedamanagement.eda.service.BurypointsEdaYidaService;
import com.wtyt.lgfesentryedamanagement.testDev.DevBaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class BurypointsEdaYidaServiceTest extends DevBaseTest {

    @Autowired
    private BurypointsEdaYidaService burypointsEdaYidaService;

    @Test
    public void test_syncEdaFormDataByInstId() throws Exception {
        Req5545021IBean data = new Req5545021IBean();
        data.setFormUuid("FORM-0839E2AFD76A492E8A803F80EED75816OJ4P");
        data.setFormInstanceId("FINST-ZNC669D1YMZIARKB6S5RQ6CO0FO8222GJ3FTLJUZ");
        burypointsEdaYidaService.syncYidaFormDataByInstId(data);
    }

    @Test
    public void test_syncYidaFormDataAll() throws Exception {
        Req5545022IBean data = new Req5545022IBean();
        data.setFormUuids("FORM-0839E2AFD76A492E8A803F80EED75816OJ4P,FORM-14FFA441F67D48208689FFA906780A7B6QCG");
        burypointsEdaYidaService.syncYidaFormDataAll(data);
    }

}
