package com.wtyt.lgfesentryedamanagement.testDev;

import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * 开发使用的spring容器测试基类，不使用 TestDataSourceConfig，
 * 仍然使用代码中的数据库连接配置，一般连的 FAT 环境，数据库相关操作需要注意
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@ActiveProfiles("dev")
public class DevBaseTest {


}
