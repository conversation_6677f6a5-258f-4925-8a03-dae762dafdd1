package com.wtyt.lgfesentryedamanagement.testDev.rpc;

import com.wtyt.lgfesentryedamanagement.pub.rpc.LugeService;
import com.wtyt.lgfesentryedamanagement.pub.rpc.RpcSqService;
import com.wtyt.lgfesentryedamanagement.pub.rpc.bean.request.Luge1076Request;
import com.wtyt.lgfesentryedamanagement.pub.rpc.bean.request.Luge8000533Request;
import com.wtyt.lgfesentryedamanagement.pub.rpc.bean.request.Luge8000535Request;
import com.wtyt.lgfesentryedamanagement.pub.rpc.bean.response.Luge1076Response;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.JsonToolkit;
import com.wtyt.lgfesentryedamanagement.testDev.DevBaseTest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

public class RpcSqServiceTest extends DevBaseTest {

    @Autowired
    private RpcSqService rpcSqService;

    @Autowired
    private LugeService lugeService;

    @Test
    public void test1076() throws Exception {
        Luge1076Response luge1076Response = rpcSqService.postMicLrOld("1076", Luge1076Request.ofType("21"), Luge1076Response.class);
        System.out.println(JsonToolkit.objectToJsonNotNull(luge1076Response));
    }

    @Test
    public void test_invoke8000535() throws Exception {
        Luge8000535Request luge8000535Request = new Luge8000535Request();
        luge8000535Request.setTaskId("65f2c3d7be8702851ad44fb4");
        luge8000535Request.setOperatorId("5f696ca0dfa3c12488e2a405");
        luge8000535Request.setCustomfieldName("架构评审");

        List<Luge8000535Request.ValueDto> valueDtoList = new ArrayList<>();
        Luge8000535Request.ValueDto valueDto = new Luge8000535Request.ValueDto();
        valueDto.setTitle("已评审");
        valueDtoList.add(valueDto);

        luge8000535Request.setValue(valueDtoList);

        lugeService.invoke8000535(luge8000535Request);
    }

    @Test
    public void test_invoke8000533() throws Exception {
        Luge8000533Request luge8000533Request = new Luge8000533Request();
        luge8000533Request.setRefId("dinga4a007414b2a0b4c"); // 线上钉钉
        luge8000533Request.setExtraUserId("4258471638823391");
        lugeService.invoke8000533(luge8000533Request);
    }

}
