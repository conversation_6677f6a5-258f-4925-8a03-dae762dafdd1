package com.wtyt.lgfesentryedamanagement.testDev.rpc;

import com.aliyun.dingtalkyida_1_0.models.*;
import com.wtyt.lgfesentryedamanagement.pub.rpc.RpcYdService;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.JsonToolkit;
import com.wtyt.lgfesentryedamanagement.testDev.DevBaseTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class RpcYdServiceTest extends DevBaseTest {

    @Autowired
    private RpcYdService rpcYdService;

    @Test
    public void test_queryFormComponentInfo() throws Exception {
        String formUuid = "FORM-0839E2AFD76A492E8A803F80EED75816OJ4P";
        GetFormComponentDefinitionListResponse response1 = rpcYdService.queryFormComponentInfo(formUuid);
        System.out.println(JsonToolkit.objectToJson(response1)); // 22 个，包含页面及容器id
        List<GetFieldDefByUuidResponseBody.GetFieldDefByUuidResponseBodyResult> fieldDefByUuid = rpcYdService.getFieldDefByUuid(formUuid);
        System.out.println("------组件定义接口-------");
        System.out.println(JsonToolkit.objectToJson(fieldDefByUuid)); // 20 个，不包含页面及容器id
    }

    @Test
    public void test_queryFormInstanceInfo() throws Exception {
        String formUuid = "FORM-0839E2AFD76A492E8A803F80EED75816OJ4P";
        String formInstanceId = "FINST-LIC66BB1QCXIXC029FBKWBYY1LF02AAVEG8TL0J31";
        BatchGetFormDataByIdListResponseBody.BatchGetFormDataByIdListResponseBodyResult instanceById = rpcYdService.getInstanceById(formUuid, formInstanceId);
        System.out.println("----instanceById----");// 有 formData 和 instanceValue(以组件形式展示实例数据)
        System.out.println(JsonToolkit.objectToJson(instanceById));

        System.out.println("----formDataByID----");
        GetFormDataByIDResponseBody formDataByID = rpcYdService.getFormDataByFormInstId(formInstanceId);
        System.out.println(JsonToolkit.objectToJson(formDataByID));// 只有formData
    }

    @Test
    public void test_getFormListInAppWithOptions() throws Exception {
        GetFormListInAppResponseBody.GetFormListInAppResponseBodyResult responseBody = rpcYdService.getFormListInAppWithOptions(1);
        System.out.println(JsonToolkit.objectToJson(responseBody));
    }

    @Test
    public void test_pageSearchFormDataList() throws Exception {
        String formUuid = "FORM-0839E2AFD76A492E8A803F80EED75816OJ4P";
        List<SearchFormDatasResponseBody.SearchFormDatasResponseBodyData> dataList = rpcYdService.pageSearchFormDataList(formUuid, 1);
        System.out.println(JsonToolkit.objectToJson(dataList));
    }

}
