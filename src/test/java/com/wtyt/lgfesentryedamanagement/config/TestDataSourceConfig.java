package com.wtyt.lgfesentryedamanagement.config;

import com.github.springtestdbunit.bean.DatabaseDataSourceConnectionFactoryBean;
import com.wtyt.commons.base.test.toolkit.DataSourceConfigToolkit;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;

import javax.sql.DataSource;

@TestConfiguration
@Profile({"build", "testcase"})
public class TestDataSourceConfig {


    @Value("${spring.test.datasource.lgfem.url}")
    private String url;
    @Value("${spring.test.datasource.lgfem.username}")
    private String username;
    @Value("${spring.test.datasource.lgfem.password}")
    private String password;
    @Value("${spring.test.datasource.lgfem.schema}")
    private String schema;
    @Value("${spring.test.datasource.lgfem.dbType}")
    private String dbType;

    @Primary
    @Bean(name = "lgfemDataSource")
    public DataSource getLgfemDataSource(){
        return DataSourceConfigToolkit.dataSource(url, username, password);
    }

    @Bean(name = "lgfemDataSourceFactory")
    public DatabaseDataSourceConnectionFactoryBean getDatabaseDataSourceConnectionFactoryBean(@Qualifier("lgfemDataSource") DataSource dataSource) {
        return DataSourceConfigToolkit.databaseDataSourceConnectionFactoryBean(dataSource, schema, dbType);
    }


}
