package com.wtyt.lgfesentryedamanagement.ecc;

import com.github.springtestdbunit.annotation.DatabaseOperation;
import com.github.springtestdbunit.annotation.DatabaseSetup;
import com.github.springtestdbunit.annotation.ExpectedDatabase;
import com.github.springtestdbunit.assertion.DatabaseAssertionMode;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.wtyt.commons.base.test.annotation.TestInterface;
import com.wtyt.lgfesentryedamanagement.ApplicationXlsBaseTests;
import com.wtyt.money.commons.bean.ResDataBean;
import com.wtyt.money.commons.consts.HintConsts;
import com.wtyt.money.commons.consts.ReCodeConsts;
import org.junit.Assert;
import org.junit.Test;

import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @date 2024/2/1 15:33
 * @vesion 1.0
 * @desc
 */
@TestInterface(sid = "5545005")
@DatabaseSetup(value = "EdaBkConfig5545005Tests.xlsx:clean", connection = "lgfemDataSourceFactory", type = DatabaseOperation.CLEAN_INSERT)
public class EdaBkConfig5545005Tests extends ApplicationXlsBaseTests {
    private static final String URL = "/edaBkConfig/add";

    /**
     * 正常新增eda覆盖率配置
     *
     * @throws Exception
     */
    @Test
    @DatabaseSetup(value = "EdaBkConfig5545005Tests.xlsx:001", connection = "lgfemDataSourceFactory", type = DatabaseOperation.CLEAN_INSERT)
    @ExpectedDatabase(value = "EdaBkConfig5545005Tests.xlsx:001_ex", connection = "lgfemDataSourceFactory", assertionMode = DatabaseAssertionMode.NON_STRICT_UNORDERED)
    public void test5545005_001() throws Exception {



        JsonObject reqJs  = new JsonObject();
        reqJs.addProperty("edaNo", "128WW8988211");//eda编号
        reqJs.addProperty("interfaceTag", "110");//接口标识
        reqJs.addProperty("interfaceProjectName", "lg-fesentry-eda-management");//接口所在项目
        reqJs.addProperty("interfaceType", "0");//接口类型：structs:3,springmvc:2,卡友地带网关:1,金融网关:0
        reqJs.addProperty("interfaceIndexName", "bury_point-collect");//索引名
        JsonArray interfaceFilters = new JsonArray();//接口类型的过滤条件
        JsonObject fl1  = new JsonObject();
        fl1.addProperty("fkey", "request_sid");
        JsonArray fvalues = new JsonArray();
        fvalues.add("19110013222");
        fvalues.add("19110013223");
        fl1.add("fvalue", fvalues);
        fl1.addProperty("frel", "2");
        interfaceFilters.add(fl1);

        JsonObject fl2  = new JsonObject();
        fl2.addProperty("fkey", "request_content");
        fl2.addProperty("fvalue", "\"dataSource\":\"1762\"");
        interfaceFilters.add(fl2);

        reqJs.add("interfaceFilters", interfaceFilters);//接口类型的过滤条件
        reqJs.addProperty("optTeam", "XE");//操作者团队
        reqJs.addProperty("optUserName", "dzl");//操作者

        String responseContent = this.sendRequest(URL, reqJs);
        Type type = new TypeToken<ResDataBean>() {}.getType();
        ResDataBean resDataBean = gson.fromJson(responseContent, type);
        Assert.assertEquals(ReCodeConsts.SUCCESS, resDataBean.getReCode());
        Assert.assertEquals(HintConsts.SYSTEM_SUCC, resDataBean.getReInfo());
    }

    /**
     * 参数校验:eda不存在
     *
     * @throws Exception
     */
    @Test
    public void test5545005_002() throws Exception {

        JsonObject reqJs  = new JsonObject();
        reqJs.addProperty("edaNo", "128WW8988211z");//eda编号
        reqJs.addProperty("interfaceTag", "110");//接口标识
        reqJs.addProperty("interfaceProjectName", "lg-fesentry-eda-management");//接口所在项目
        reqJs.addProperty("interfaceType", "0");//接口类型：structs:3,springmvc:2,卡友地带网关:1,金融网关:0
        reqJs.addProperty("interfaceIndexName", "bury_point-collect");//索引名
        JsonArray interfaceFilters = new JsonArray();//接口类型的过滤条件
        JsonObject fl1  = new JsonObject();
        fl1.addProperty("fkey", "request_sid");
        JsonArray fvalues = new JsonArray();
        fvalues.add("19110013222");
        fvalues.add("19110013223");
        fl1.add("fvalue", fvalues);
        fl1.addProperty("frel", "2");
        interfaceFilters.add(fl1);

        JsonObject fl2  = new JsonObject();
        fl2.addProperty("fkey", "request_content");
        fl2.addProperty("fvalue", "\"dataSource\":\"1762\"");
        interfaceFilters.add(fl2);

        reqJs.add("interfaceFilters", interfaceFilters);//接口类型的过滤条件
        reqJs.addProperty("optTeam", "XE");//操作者团队
        reqJs.addProperty("optUserName", "dzl");//操作者

        String responseContent = this.sendRequest(URL, reqJs);
        Type type = new TypeToken<ResDataBean>() {}.getType();
        ResDataBean resDataBean = gson.fromJson(responseContent, type);
        Assert.assertEquals(ReCodeConsts.TIP, resDataBean.getReCode());
        Assert.assertEquals("当前EDA不存在", resDataBean.getReInfo());
    }


    /**
     * 参数校验:eda已删除
     *
     * @throws Exception
     */
    @Test
    @DatabaseSetup(value = "EdaBkConfig5545005Tests.xlsx:002", connection = "lgfemDataSourceFactory", type = DatabaseOperation.CLEAN_INSERT)
    public void test5545005_003() throws Exception {



        JsonObject reqJs  = new JsonObject();
        reqJs.addProperty("edaNo", "128WW8988211");//eda编号
        reqJs.addProperty("interfaceTag", "110");//接口标识
        reqJs.addProperty("interfaceProjectName", "lg-fesentry-eda-management");//接口所在项目
        reqJs.addProperty("interfaceType", "0");//接口类型：structs:3,springmvc:2,卡友地带网关:1,金融网关:0
        reqJs.addProperty("interfaceIndexName", "bury_point-collect");//索引名
        JsonArray interfaceFilters = new JsonArray();//接口类型的过滤条件
        JsonObject fl1  = new JsonObject();
        fl1.addProperty("fkey", "request_sid");
        JsonArray fvalues = new JsonArray();
        fvalues.add("19110013222");
        fvalues.add("19110013223");
        fl1.add("fvalue", fvalues);
        fl1.addProperty("frel", "2");
        interfaceFilters.add(fl1);

        JsonObject fl2  = new JsonObject();
        fl2.addProperty("fkey", "request_content");
        fl2.addProperty("fvalue", "\"dataSource\":\"1762\"");
        interfaceFilters.add(fl2);

        reqJs.add("interfaceFilters", interfaceFilters);//接口类型的过滤条件
        reqJs.addProperty("optTeam", "XE");//操作者团队
        reqJs.addProperty("optUserName", "dzl");//操作者

        String responseContent = this.sendRequest(URL, reqJs);
        Type type = new TypeToken<ResDataBean>() {}.getType();
        ResDataBean resDataBean = gson.fromJson(responseContent, type);
        Assert.assertEquals(ReCodeConsts.TIP, resDataBean.getReCode());
        Assert.assertEquals("当前EDA已删除", resDataBean.getReInfo());
    }
}
