package com.wtyt.lgfesentryedamanagement.ecc;

import com.github.springtestdbunit.annotation.DatabaseOperation;
import com.github.springtestdbunit.annotation.DatabaseSetup;
import com.github.springtestdbunit.annotation.ExpectedDatabase;
import com.github.springtestdbunit.assertion.DatabaseAssertionMode;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.wtyt.commons.base.test.annotation.TestInterface;
import com.wtyt.lgfesentryedamanagement.ApplicationXlsBaseTests;
import com.wtyt.money.commons.bean.ResDataBean;
import com.wtyt.money.commons.consts.HintConsts;
import com.wtyt.money.commons.consts.ReCodeConsts;
import org.junit.Assert;
import org.junit.Test;

import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @date 2024/2/2 9:03
 * @vesion 1.0
 * @desc
 */
@TestInterface(sid = "5545007")
@DatabaseSetup(value = "EdaBkConfig5545007Tests.xlsx:clean", connection = "lgfemDataSourceFactory", type = DatabaseOperation.CLEAN_INSERT)
public class EdaBkConfig5545007Tests extends ApplicationXlsBaseTests {

    private static final String URL = "/edaBkConfig/delete";


    /**
     * 正常删除
     * @throws Exception
     */
    @Test
    @DatabaseSetup(value = "EdaBkConfig5545007Tests.xlsx:001", connection = "lgfemDataSourceFactory", type = DatabaseOperation.CLEAN_INSERT)
    @ExpectedDatabase(value = "EdaBkConfig5545007Tests.xlsx:001_ex", connection = "lgfemDataSourceFactory", assertionMode = DatabaseAssertionMode.NON_STRICT_UNORDERED)
    public void test5545007_001() throws Exception {
        JsonObject reqJs  = new JsonObject();
        reqJs.addProperty("burypointsEdaBkConfigId", "1");//EDA编号
        reqJs.addProperty("optTeam", "XE");//操作者团队
        reqJs.addProperty("optUserName", "dzl");//操作者
        String responseContent = this.sendRequest(URL, reqJs);

        Type type = new TypeToken<ResDataBean>() {}.getType();
        ResDataBean resDataBean = gson.fromJson(responseContent, type);
        Assert.assertEquals(ReCodeConsts.SUCCESS, resDataBean.getReCode());
        Assert.assertEquals(HintConsts.SYSTEM_SUCC, resDataBean.getReInfo());
    }

    /**
     * 实体bean设置值
     * @throws Exception
     */
    @Test
    @DatabaseSetup(value = "EdaBkConfig5545007Tests.xlsx:001", connection = "lgfemDataSourceFactory", type = DatabaseOperation.CLEAN_INSERT)
    @ExpectedDatabase(value = "EdaBkConfig5545007Tests.xlsx:002_ex", connection = "lgfemDataSourceFactory", assertionMode = DatabaseAssertionMode.NON_STRICT_UNORDERED)
    public void test5545007_002() throws Exception {
        JsonObject reqJs  = new JsonObject();
        reqJs.addProperty("burypointsEdaBkConfigId", "1");//EDA编号
        reqJs.addProperty("optTeam", "XE");//操作者团队
        reqJs.addProperty("optUserName", "dzl");//操作者
        reqJs.addProperty("interfaceTag", "444");//接口标识
        String responseContent = this.sendRequest(URL, reqJs);

        Type type = new TypeToken<ResDataBean>() {}.getType();
        ResDataBean resDataBean = gson.fromJson(responseContent, type);
        Assert.assertEquals(ReCodeConsts.SUCCESS, resDataBean.getReCode());
        Assert.assertEquals(HintConsts.SYSTEM_SUCC, resDataBean.getReInfo());
    }
}
