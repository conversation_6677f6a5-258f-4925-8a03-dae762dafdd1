package com.wtyt.lgfesentryedamanagement.ecc;

import com.github.springtestdbunit.annotation.DatabaseOperation;
import com.github.springtestdbunit.annotation.DatabaseSetup;
import com.github.springtestdbunit.annotation.ExpectedDatabase;
import com.github.springtestdbunit.assertion.DatabaseAssertionMode;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.wtyt.commons.base.test.annotation.TestInterface;
import com.wtyt.lgfesentryedamanagement.ApplicationXlsBaseTests;
import com.wtyt.money.commons.bean.ResDataBean;
import com.wtyt.money.commons.consts.HintConsts;
import com.wtyt.money.commons.consts.ReCodeConsts;
import org.junit.Assert;
import org.junit.Test;

import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @date 2024/2/1 9:49
 * @vesion 1.0
 * @desc
 */
@TestInterface(sid = "5545006")
@DatabaseSetup(value = "EdaBkConfig5545006Tests.xlsx:clean", connection = "lgfemDataSourceFactory", type = DatabaseOperation.CLEAN_INSERT)
public class EdaBkConfig5545006Tests extends ApplicationXlsBaseTests {

    private static final String URL = "/edaBkConfig/edit";
    /**
     * 正常更新eda配置
     * @throws Exception
     */
    @Test
    @DatabaseSetup(value = "EdaBkConfig5545006Tests.xlsx:001", connection = "lgfemDataSourceFactory", type = DatabaseOperation.CLEAN_INSERT)
    @ExpectedDatabase(value = "EdaBkConfig5545006Tests.xlsx:001_ex", connection = "lgfemDataSourceFactory", assertionMode = DatabaseAssertionMode.NON_STRICT_UNORDERED)
    public void test5545006_001() throws Exception {
        JsonObject reqJs  = new JsonObject();
        reqJs.addProperty("burypointsEdaBkConfigId", "1");//eda编号
        reqJs.addProperty("interfaceTag", "1000000000000000");//接口标识
        reqJs.addProperty("interfaceProjectName", "lg-fesentry-eda-management");//接口所在项目
        reqJs.addProperty("interfaceType", "0");//接口类型：structs:3,springmvc:2,卡友地带网关:1,金融网关:0
        reqJs.addProperty("interfaceIndexName", "bury_point-collect");//索引名
        JsonArray interfaceFilters = new JsonArray();//接口类型的过滤条件
        JsonObject fl1  = new JsonObject();
        fl1.addProperty("fkey", "request_sid");
        fl1.addProperty("frel", "2");
        JsonArray fvalues = new JsonArray();
        fvalues.add("19110013222");
        fvalues.add("19110013223");
        fl1.add("fvalue", fvalues);

        interfaceFilters.add(fl1);

        JsonObject fl2  = new JsonObject();
        fl2.addProperty("fkey", "request_content");
        fl2.addProperty("fvalue", "\"dataSource\":\"1762\"");
        interfaceFilters.add(fl2);

        reqJs.add("interfaceFilters", interfaceFilters);//接口类型的过滤条件
        reqJs.addProperty("optTeam", "XE");//操作者团队
        reqJs.addProperty("optUserName", "dzl");//操作者

        String responseContent = this.sendRequest(URL, reqJs);
        Type type = new TypeToken<ResDataBean>() {}.getType();
        ResDataBean resDataBean = gson.fromJson(responseContent, type);
        Assert.assertEquals(ReCodeConsts.SUCCESS, resDataBean.getReCode());
        Assert.assertEquals(HintConsts.SYSTEM_SUCC, resDataBean.getReInfo());
    }

    /**
     * 异常:eda已刪除
     *
     * @throws Exception
     */
    @Test
    @DatabaseSetup(value = "EdaBkConfig5545006Tests.xlsx:002", connection = "lgfemDataSourceFactory", type = DatabaseOperation.CLEAN_INSERT)
    public void test5545006_002() throws Exception {
        JsonObject reqJs = new JsonObject();
        reqJs.addProperty("burypointsEdaBkConfigId", "1");//eda编号
        reqJs.addProperty("interfaceTag", "1000000000000000");//接口标识
        reqJs.addProperty("interfaceProjectName", "lg-fesentry-eda-management");//接口所在项目
        reqJs.addProperty("interfaceType", "0");//接口类型：structs:3,springmvc:2,卡友地带网关:1,金融网关:0
        reqJs.addProperty("interfaceIndexName", "bury_point-collect");//索引名
        JsonArray interfaceFilters = new JsonArray();//接口类型的过滤条件
        JsonObject fl1 = new JsonObject();
        fl1.addProperty("fkey", "request_sid");
        fl1.addProperty("frel", "2");
        JsonArray fvalues = new JsonArray();
        fvalues.add("19110013222");
        fvalues.add("19110013223");
        fl1.add("fvalue", fvalues);

        interfaceFilters.add(fl1);

        JsonObject fl2 = new JsonObject();
        fl2.addProperty("fkey", "request_content");
        fl2.addProperty("fvalue", "\"dataSource\":\"1762\"");
        interfaceFilters.add(fl2);

        reqJs.add("interfaceFilters", interfaceFilters);//接口类型的过滤条件
        reqJs.addProperty("optTeam", "XE");//操作者团队
        reqJs.addProperty("optUserName", "dzl");//操作者

        String responseContent = this.sendRequest(URL, reqJs);
        Type type = new TypeToken<ResDataBean>() {
        }.getType();
        ResDataBean resDataBean = gson.fromJson(responseContent, type);
        Assert.assertEquals(ReCodeConsts.TIP, resDataBean.getReCode());
        Assert.assertEquals("当前EDA已删除", resDataBean.getReInfo());
    }



    /**
     * 异常:eda配置已刪除
     *
     * @throws Exception
     */
    @Test
    @DatabaseSetup(value = "EdaBkConfig5545006Tests.xlsx:003", connection = "lgfemDataSourceFactory", type = DatabaseOperation.CLEAN_INSERT)
    public void test5545006_003() throws Exception {
        JsonObject reqJs = new JsonObject();
        reqJs.addProperty("burypointsEdaBkConfigId", "1");//eda编号
        reqJs.addProperty("interfaceTag", "1000000000000000");//接口标识
        reqJs.addProperty("interfaceProjectName", "lg-fesentry-eda-management");//接口所在项目
        reqJs.addProperty("interfaceType", "0");//接口类型：structs:3,springmvc:2,卡友地带网关:1,金融网关:0
        reqJs.addProperty("interfaceIndexName", "bury_point-collect");//索引名
        JsonArray interfaceFilters = new JsonArray();//接口类型的过滤条件
        JsonObject fl1 = new JsonObject();
        fl1.addProperty("fkey", "request_sid");
        fl1.addProperty("frel", "2");
        JsonArray fvalues = new JsonArray();
        fvalues.add("19110013222");
        fvalues.add("19110013223");
        fl1.add("fvalue", fvalues);

        interfaceFilters.add(fl1);

        JsonObject fl2 = new JsonObject();
        fl2.addProperty("fkey", "request_content");
        fl2.addProperty("fvalue", "\"dataSource\":\"1762\"");
        interfaceFilters.add(fl2);

        reqJs.add("interfaceFilters", interfaceFilters);//接口类型的过滤条件
        reqJs.addProperty("optTeam", "XE");//操作者团队
        reqJs.addProperty("optUserName", "dzl");//操作者

        String responseContent = this.sendRequest(URL, reqJs);
        Type type = new TypeToken<ResDataBean>() {
        }.getType();
        ResDataBean resDataBean = gson.fromJson(responseContent, type);
        Assert.assertEquals(ReCodeConsts.TIP, resDataBean.getReCode());
        Assert.assertEquals("当前EDA覆盖率配置已删除", resDataBean.getReInfo());
    }


    /**
     * 异常:eda不存在
     *
     * @throws Exception
     */
    @Test
    @DatabaseSetup(value = "EdaBkConfig5545006Tests.xlsx:004", connection = "lgfemDataSourceFactory", type = DatabaseOperation.CLEAN_INSERT)
    public void test5545006_004() throws Exception {
        JsonObject reqJs = new JsonObject();
        reqJs.addProperty("burypointsEdaBkConfigId", "1");//eda编号
        reqJs.addProperty("interfaceTag", "1000000000000000");//接口标识
        reqJs.addProperty("interfaceProjectName", "lg-fesentry-eda-management");//接口所在项目
        reqJs.addProperty("interfaceType", "0");//接口类型：structs:3,springmvc:2,卡友地带网关:1,金融网关:0
        reqJs.addProperty("interfaceIndexName", "bury_point-collect");//索引名
        JsonArray interfaceFilters = new JsonArray();//接口类型的过滤条件
        JsonObject fl1 = new JsonObject();
        fl1.addProperty("fkey", "request_sid");
        fl1.addProperty("frel", "2");
        JsonArray fvalues = new JsonArray();
        fvalues.add("19110013222");
        fvalues.add("19110013223");
        fl1.add("fvalue", fvalues);

        interfaceFilters.add(fl1);

        JsonObject fl2 = new JsonObject();
        fl2.addProperty("fkey", "request_content");
        fl2.addProperty("fvalue", "\"dataSource\":\"1762\"");
        interfaceFilters.add(fl2);

        reqJs.add("interfaceFilters", interfaceFilters);//接口类型的过滤条件
        reqJs.addProperty("optTeam", "XE");//操作者团队
        reqJs.addProperty("optUserName", "dzl");//操作者

        String responseContent = this.sendRequest(URL, reqJs);
        Type type = new TypeToken<ResDataBean>() {
        }.getType();
        ResDataBean resDataBean = gson.fromJson(responseContent, type);
        Assert.assertEquals(ReCodeConsts.TIP, resDataBean.getReCode());
        Assert.assertEquals("当前EDA不存在", resDataBean.getReInfo());
    }


    /**
     * 正常更新eda配置-修改过滤条件
     *
     * @throws Exception
     */
    @Test
    @DatabaseSetup(value = "EdaBkConfig5545006Tests.xlsx:005", connection = "lgfemDataSourceFactory", type = DatabaseOperation.CLEAN_INSERT)
    @ExpectedDatabase(value = "EdaBkConfig5545006Tests.xlsx:005_ex", connection = "lgfemDataSourceFactory", assertionMode = DatabaseAssertionMode.NON_STRICT_UNORDERED)
    public void test5545006_005() throws Exception {
        JsonObject reqJs = new JsonObject();
        reqJs.addProperty("burypointsEdaBkConfigId", "1");//eda编号
        reqJs.addProperty("interfaceTag", "1000000000000000");//接口标识
        reqJs.addProperty("interfaceProjectName", "lg-fesentry-eda-management");//接口所在项目
        reqJs.addProperty("interfaceType", "0");//接口类型：structs:3,springmvc:2,卡友地带网关:1,金融网关:0
        reqJs.addProperty("interfaceIndexName", "bury_point-collect");//索引名
        JsonArray interfaceFilters = new JsonArray();//接口类型的过滤条件
        JsonObject fl1 = new JsonObject();
        fl1.addProperty("fkey", "request_sid");
        fl1.addProperty("frel", "2");
        JsonArray fvalues = new JsonArray();
        fvalues.add("19110013222");
        fvalues.add("2");
        fl1.add("fvalue", fvalues);

        interfaceFilters.add(fl1);

        JsonObject fl2 = new JsonObject();
        fl2.addProperty("fkey", "request_content");
        fl2.addProperty("fvalue", "\"dataSource\":\"1562\"");
        interfaceFilters.add(fl2);

        reqJs.add("interfaceFilters", interfaceFilters);//接口类型的过滤条件
        reqJs.addProperty("optTeam", "XE");//操作者团队
        reqJs.addProperty("optUserName", "dzl");//操作者

        String responseContent = this.sendRequest(URL, reqJs);
        Type type = new TypeToken<ResDataBean>() {
        }.getType();
        ResDataBean resDataBean = gson.fromJson(responseContent, type);
        Assert.assertEquals(ReCodeConsts.SUCCESS, resDataBean.getReCode());
        Assert.assertEquals(HintConsts.SYSTEM_SUCC, resDataBean.getReInfo());
    }

}
