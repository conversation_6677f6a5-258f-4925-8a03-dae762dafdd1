package com.wtyt.lgfesentryedamanagement.eda;

import com.aliyun.dingtalkyida_1_0.Client;
import com.aliyun.dingtalkyida_1_0.models.*;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.wtyt.commons.base.test.toolkit.MockToolkit;
import com.wtyt.lg.commons.bean.ResDataBean;
import com.wtyt.lgfesentryedamanagement.ApplicationXlsBaseTests;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.JsonToolkit;
import mockit.Mock;
import mockit.MockUp;
import org.junit.Assert;
import org.junit.Test;

import java.lang.reflect.Type;
import java.util.ArrayList;

public class EdaYida5545022Tests  extends ApplicationXlsBaseTests {
    private MockUp<Client> clientMock;

    private static final String url = "/yida/syncYidaFormDataAll";
/*
检验为空的场景
 */

    @Test
    public void test5545022_001() throws Exception {
        JsonObject result5545022 = new JsonObject();
        result5545022.addProperty("formUuids", "");
        String responseContent = this.sendRequest(url, result5545022.toString());
        Type type = new TypeToken<ResDataBean>() {
        }.getType();
        ResDataBean resDataBean = gson.fromJson(responseContent, type);
        Assert.assertEquals("1", resDataBean.getReCode());
    }


    @Test
    public void test5545022_002() throws Exception {
        MockToolkit.getInstance().addMock("-1076", f -> {
            JsonObject result1076 = new JsonObject();
            JsonObject result1076_001 = new JsonObject();
//        JsonObject result1076_002 = new JsonObject();
            result1076_001.addProperty("access_token", "78_6ElDpKtDTGYlqM-jO92cZgRnwLi0u6_nfUAWSTc-lDA12YM8pd6tktO1hs65UmK-RJmEiXeI4m0u6eA6CNEk-NGFRfp4ZjoL3OgxRhhcMR9HFt1IntYtD7hJmncQTBhAIAFIQ");
//        result1076_001.addProperty("req_msg","成功");
//        result1076_001.addProperty("req_code","200");
            result1076.add("result", result1076_001);
//        result1076.add("data",result1076_002);
            logger.info("mock 1076的数据为：{}", new Gson().toJson(result1076));
            return result1076_001;
        });
        clientMock = new MockUp<Client>() {
            @Mock
            public SearchFormDatasResponse searchFormDatasWithOptions(SearchFormDatasRequest request, SearchFormDatasHeaders headers, com.aliyun.teautil.models.RuntimeOptions runtime) throws Exception {
                SearchFormDatasResponse response = new SearchFormDatasResponse();
                SearchFormDatasResponseBody body = null;
                if (request.getCurrentPage() != null && request.getCurrentPage() == 1) {
                    //调用方法
                    String result = mockdata_001();
                    System.out.println("mock batchGetFormDataByIdListWithOptions001 body 数据： " + result);
                    body = JsonToolkit.jsonToObject(result, SearchFormDatasResponseBody.class);
                } else {
                    body = new SearchFormDatasResponseBody();
                    body.setData(new ArrayList<>());
                }

                response.setStatusCode(200);
                response.setBody(body);
                return response;
            }

            @Mock
            public GetFieldDefByUuidResponse getFieldDefByUuidWithOptions(GetFieldDefByUuidRequest request, GetFieldDefByUuidHeaders headers, com.aliyun.teautil.models.RuntimeOptions runtime) throws Exception {
                GetFieldDefByUuidResponse response = new GetFieldDefByUuidResponse();
                String result = mockdata_002();
                System.out.println("mock  getFieldDefByUuidWithOptions002 body 数据： " + result);
                GetFieldDefByUuidResponseBody body = JsonToolkit.jsonToObject(result, GetFieldDefByUuidResponseBody.class);
                response.setStatusCode(200);
                response.setBody(body);
                return response;

            }
        };


        JsonObject result5545022 = new JsonObject();
        result5545022.addProperty("formUuids", "FORM-EF6Y93URN24F1SCX15VA2P918LPEIJ2H3UFORCJ1");
        String responseContent = this.sendRequest(url, result5545022);
        Type type = new TypeToken<ResDataBean>() {
        }.getType();
        ResDataBean resDataBean = gson.fromJson(responseContent, type);
        Assert.assertEquals("0", resDataBean.getReCode());

    }

    public String mockdata_001() {
        JsonObject result = new JsonObject();
        result.addProperty("currentPage", "1");
        result.addProperty("totalCount", "100");
        JsonArray resultlist = new JsonArray();
        JsonObject resultList_001 = new JsonObject();
        resultList_001.addProperty("dataId", 1002);
        resultList_001.addProperty("formInstanceId", "FINST-BNKJDRF");
        resultList_001.addProperty("createdTimeGMT", "2024-03-07T09:23Z");
        resultList_001.addProperty("modifiedTimeGMT", "2024-03-07T09:23Z");
        resultList_001.addProperty("formUuid", "FORM-EF6Y93URN24F1SCX15VA2P918LPEIJ2H3UFORCJ1");
        resultList_001.addProperty("modelUuid", "FORM-EF6Y93URN24F1SCX15VA2P918LPEIJ2H3UFORCJ1");


        JsonObject originator = new JsonObject();
        originator.addProperty("userId","1731111122212122");
        JsonObject userName = new JsonObject();
        userName.addProperty("nameInChinese","张三");
        userName.addProperty("nameInEnglish","ZhangSan");
        userName.addProperty("type","i18n");
        originator.add("userName",userName);
        originator.addProperty("departmentName","开发部");
        originator.addProperty("email","<EMAIL>");
        resultList_001.add("originator",originator);

        JsonObject modifyUser  = new JsonObject();
        modifyUser.addProperty("userId","173111112212");
        JsonObject userName_1 = new JsonObject();
        userName_1.addProperty("nameInChinese","张三");
        userName_1.addProperty("nameInEnglish","ZhangSan");
        userName_1.addProperty("type","i18n");
        modifyUser.add("userName",userName_1);
        modifyUser.addProperty("departmentName","开发部");
        modifyUser.addProperty("email","<EMAIL>");

        resultList_001.add("modifyUser",modifyUser);
        resultList_001.addProperty("instanceValue", "[{\"fieldData\":{\"text\":\"选项一\",\"value\":\"one\"},\"fieldId\":\"textField_laq7xxx\"}]");
        resultList_001.addProperty("title", "张三提交的表单");
        resultList_001.addProperty("serialNo", "serialNo");
        resultList_001.addProperty("version", 3);
        resultList_001.addProperty("creatorUserId", "1731234567");
        resultList_001.addProperty("modifierUserId", "1731234567");
        resultList_001.addProperty("sequence", "1223");
        resultlist.add(resultList_001);
        result.add("data", resultlist);
        return new Gson().toJson(result);
    }

    public String mockdata_002() {
        JsonObject result_id = new JsonObject();
        result_id.addProperty("success", true);
        JsonArray resultlist_id = new JsonArray();
        JsonObject resultList_id_001 = new JsonObject();
        resultList_id_001.addProperty("componentName", "RadioField");
        resultList_id_001.addProperty("fieldId", "textField_laq7xxx");
        resultList_id_001.addProperty("behavior", "NORMAL");
        resultList_id_001.addProperty("label", "选项一");
        //resultList_id_001.addProperty("props", "223");
        //resultList_id_001.addProperty("children", "单行文本");
        resultlist_id.add(resultList_id_001);
        result_id.add("result", resultlist_id);
        return new Gson().toJson(result_id);
    }
}






