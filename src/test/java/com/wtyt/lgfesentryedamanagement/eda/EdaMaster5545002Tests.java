package com.wtyt.lgfesentryedamanagement.eda;

import com.github.springtestdbunit.annotation.DatabaseOperation;
import com.github.springtestdbunit.annotation.DatabaseSetup;
import com.github.springtestdbunit.annotation.ExpectedDatabase;
import com.github.springtestdbunit.assertion.DatabaseAssertionMode;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.wtyt.commons.base.test.annotation.TestInterface;
import com.wtyt.lgfesentryedamanagement.ApplicationXlsBaseTests;
import com.wtyt.money.commons.bean.ResDataBean;
import com.wtyt.money.commons.consts.HintConsts;
import com.wtyt.money.commons.consts.ReCodeConsts;
import org.junit.Assert;
import org.junit.Test;

import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @date 2024/2/1 9:49
 * @vesion 1.0
 * @desc
 */
@TestInterface(sid = "5545002")
@DatabaseSetup(value = "EdaMaster5545002Tests.xlsx:clean", connection = "lgfemDataSourceFactory", type = DatabaseOperation.CLEAN_INSERT)
public class EdaMaster5545002Tests extends ApplicationXlsBaseTests {

    private static final String URL = "/edaMaster/edit";
    /**
     * 正常更新eda
     * @throws Exception
     */
    @Test
    @DatabaseSetup(value = "EdaMaster5545002Tests.xlsx:001", connection = "lgfemDataSourceFactory", type = DatabaseOperation.CLEAN_INSERT)
    @ExpectedDatabase(value = "EdaMaster5545002Tests.xlsx:001_ex", connection = "lgfemDataSourceFactory", assertionMode = DatabaseAssertionMode.NON_STRICT_UNORDERED)
    public void test5545002_001() throws Exception {
        JsonObject reqJs  = new JsonObject();
        reqJs.addProperty("edaNo", "128WW8988211");//EDA编号
        reqJs.addProperty("edaName", "测W试0");//EDA中文名称
        reqJs.addProperty("edaStatus", "1");//EDA状态：0: 不呈现，1：呈现
        reqJs.addProperty("edaDesc", "测试");//EDA描述信息
        reqJs.addProperty("maintenanceTeam", "xe");//维护团队
        reqJs.addProperty("maintenanceUi", "bibo");//负责ui
        reqJs.addProperty("coverageBaseline", "100.00");//覆盖率基线（百分比）
        reqJs.addProperty("floatingRatio", "6");//浮动比例（百分比）
        reqJs.addProperty("unconventionalBaselineReason", "");//非常规覆盖率基线原因
        reqJs.addProperty("edaBizLine", "新大陆");//业务线
        reqJs.addProperty("edaProduct", "新大陆App");//产品服务
        reqJs.addProperty("edaModule", "货源计划");//产品模块
        reqJs.addProperty("optTeam", "XE");//操作者团队
        reqJs.addProperty("optUserName", "dzl");//操作者
        String responseContent = this.sendRequest(URL, reqJs);

        Type type = new TypeToken<ResDataBean>() {}.getType();
        ResDataBean resDataBean = gson.fromJson(responseContent, type);
        Assert.assertEquals(ReCodeConsts.SUCCESS, resDataBean.getReCode());
        Assert.assertEquals(HintConsts.SYSTEM_SUCC, resDataBean.getReInfo());
    }

    /**
     * 验证参数校验:非常规覆盖率基线原因不能为空
     *
     * @throws Exception
     */
    @Test
    public void test5545002_002() throws Exception {
        JsonObject reqJs = new JsonObject();
        reqJs.addProperty("edaNo", "128WW8988211");//EDA编号
        reqJs.addProperty("edaName", "测W试0");//EDA中文名称
        reqJs.addProperty("edaStatus", "1");//EDA状态：0: 不呈现，1：呈现
        reqJs.addProperty("edaDesc", "测试");//EDA描述信息
        reqJs.addProperty("maintenanceTeam", "xe");//维护团队
        reqJs.addProperty("maintenanceUi", "bibo");//负责ui
        reqJs.addProperty("coverageBaseline", "120");//覆盖率基线（百分比）
        reqJs.addProperty("floatingRatio", "10");//浮动比例（百分比）
        reqJs.addProperty("unconventionalBaselineReason", "");//非常规覆盖率基线原因
        reqJs.addProperty("edaBizLine", "新大陆");//业务线
        reqJs.addProperty("edaProduct", "新大陆App");//产品服务
        reqJs.addProperty("edaModule", "货源计划");//产品模块
        reqJs.addProperty("optTeam", "XE");//操作者团队
        reqJs.addProperty("optUserName", "dzl");//操作者
        String responseContent = this.sendRequest(URL, reqJs);

        Type type = new TypeToken<ResDataBean>() {
        }.getType();
        ResDataBean resDataBean = gson.fromJson(responseContent, type);
        Assert.assertEquals(ReCodeConsts.TIP, resDataBean.getReCode());
        Assert.assertEquals("非常规覆盖率基线原因不能为空", resDataBean.getReInfo());
    }


    /**
     * 验证已删除
     *
     * @throws Exception
     */
    @Test
    @DatabaseSetup(value = "EdaMaster5545002Tests.xlsx:002", connection = "lgfemDataSourceFactory", type = DatabaseOperation.CLEAN_INSERT)
    public void test5545002_003() throws Exception {
        JsonObject reqJs = new JsonObject();
        reqJs.addProperty("edaNo", "128WW8988211");//EDA编号
        reqJs.addProperty("edaName", "测W试0");//EDA中文名称
        reqJs.addProperty("edaStatus", "1");//EDA状态：0: 不呈现，1：呈现
        reqJs.addProperty("edaDesc", "测试");//EDA描述信息
        reqJs.addProperty("maintenanceTeam", "xe");//维护团队
        reqJs.addProperty("maintenanceUi", "bibo");//负责ui
        reqJs.addProperty("coverageBaseline", "100");//覆盖率基线（百分比）
        reqJs.addProperty("floatingRatio", "6");//浮动比例（百分比）
        reqJs.addProperty("unconventionalBaselineReason", "");//非常规覆盖率基线原因
        reqJs.addProperty("edaBizLine", "新大陆");//业务线
        reqJs.addProperty("edaProduct", "新大陆App");//产品服务
        reqJs.addProperty("edaModule", "货源计划");//产品模块
        reqJs.addProperty("optTeam", "XE");//操作者团队
        reqJs.addProperty("optUserName", "dzl");//操作者
        String responseContent = this.sendRequest(URL, reqJs);

        Type type = new TypeToken<ResDataBean>() {
        }.getType();
        ResDataBean resDataBean = gson.fromJson(responseContent, type);
        Assert.assertEquals(ReCodeConsts.TIP, resDataBean.getReCode());
        Assert.assertEquals("当前EDA已删除", resDataBean.getReInfo());
    }

    /**
     * 验证不存在
     *
     * @throws Exception
     */
    @Test
    @DatabaseSetup(value = "EdaMaster5545002Tests.xlsx:002", connection = "lgfemDataSourceFactory", type = DatabaseOperation.CLEAN_INSERT)
    public void test5545002_004() throws Exception {
        JsonObject reqJs = new JsonObject();
        reqJs.addProperty("edaNo", "128WW8988211x");//EDA编号
        reqJs.addProperty("edaName", "测W试0");//EDA中文名称
        reqJs.addProperty("edaStatus", "1");//EDA状态：0: 不呈现，1：呈现
        reqJs.addProperty("edaDesc", "测试");//EDA描述信息
        reqJs.addProperty("maintenanceTeam", "xe");//维护团队
        reqJs.addProperty("maintenanceUi", "bibo");//负责ui
        reqJs.addProperty("coverageBaseline", "100");//覆盖率基线（百分比）
        reqJs.addProperty("floatingRatio", "6");//浮动比例（百分比）
        reqJs.addProperty("unconventionalBaselineReason", "");//非常规覆盖率基线原因
        reqJs.addProperty("edaBizLine", "新大陆");//业务线
        reqJs.addProperty("edaProduct", "新大陆App");//产品服务
        reqJs.addProperty("edaModule", "货源计划");//产品模块
        reqJs.addProperty("optTeam", "XE");//操作者团队
        reqJs.addProperty("optUserName", "dzl");//操作者
        String responseContent = this.sendRequest(URL, reqJs);

        Type type = new TypeToken<ResDataBean>() {
        }.getType();
        ResDataBean resDataBean = gson.fromJson(responseContent, type);
        Assert.assertEquals(ReCodeConsts.TIP, resDataBean.getReCode());
        Assert.assertEquals("当前EDA不存在", resDataBean.getReInfo());
    }

    /**
     * 验证修改重名
     * @throws Exception
     */
    @Test
    @DatabaseSetup(value = "EdaMaster5545002Tests.xlsx:003", connection = "lgfemDataSourceFactory", type = DatabaseOperation.CLEAN_INSERT)
    public void test5545002_005() throws Exception {
        JsonObject reqJs  = new JsonObject();
        reqJs.addProperty("edaNo", "128WW8988212");//EDA编号
        reqJs.addProperty("edaName", "测W试0");//EDA中文名称
        reqJs.addProperty("edaStatus", "1");//EDA状态：0: 不呈现，1：呈现
        reqJs.addProperty("edaDesc", "测试");//EDA描述信息
        reqJs.addProperty("maintenanceTeam", "xe");//维护团队
        reqJs.addProperty("maintenanceUi", "bibo");//负责ui
        reqJs.addProperty("coverageBaseline", "100");//覆盖率基线（百分比）
        reqJs.addProperty("floatingRatio", "6");//浮动比例（百分比）
        reqJs.addProperty("unconventionalBaselineReason", "");//非常规覆盖率基线原因
        reqJs.addProperty("edaBizLine", "新大陆");//业务线
        reqJs.addProperty("edaProduct", "新大陆App");//产品服务
        reqJs.addProperty("edaModule", "货源计划");//产品模块
        reqJs.addProperty("optTeam", "XE");//操作者团队
        reqJs.addProperty("optUserName", "dzl");//操作者
        String responseContent = this.sendRequest(URL, reqJs);

        Type type = new TypeToken<ResDataBean>() {}.getType();
        ResDataBean resDataBean = gson.fromJson(responseContent, type);
        Assert.assertEquals(ReCodeConsts.TIP, resDataBean.getReCode());
        Assert.assertEquals("当前EDA名已存在", resDataBean.getReInfo());
    }
}
