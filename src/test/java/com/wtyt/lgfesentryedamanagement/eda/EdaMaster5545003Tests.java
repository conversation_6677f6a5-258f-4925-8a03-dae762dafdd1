package com.wtyt.lgfesentryedamanagement.eda;

import com.github.springtestdbunit.annotation.DatabaseOperation;
import com.github.springtestdbunit.annotation.DatabaseSetup;
import com.github.springtestdbunit.annotation.ExpectedDatabase;
import com.github.springtestdbunit.assertion.DatabaseAssertionMode;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.wtyt.commons.base.test.annotation.TestInterface;
import com.wtyt.lgfesentryedamanagement.ApplicationXlsBaseTests;
import com.wtyt.money.commons.bean.ResDataBean;
import com.wtyt.money.commons.consts.HintConsts;
import com.wtyt.money.commons.consts.ReCodeConsts;
import org.junit.Assert;
import org.junit.Test;

import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @date 2024/2/1 11:08
 * @vesion 1.0
 * @desc
 */
@TestInterface(sid = "5545003")
@DatabaseSetup(value = "EdaMaster5545003Tests.xlsx:clean", connection = "lgfemDataSourceFactory", type = DatabaseOperation.CLEAN_INSERT)
public class EdaMaster5545003Tests extends ApplicationXlsBaseTests {

    private static final String URL = "/edaMaster/delete";

    /**
     * 正常删除
     * @throws Exception
     */
    @Test
    @DatabaseSetup(value = "EdaMaster5545003Tests.xlsx:001", connection = "lgfemDataSourceFactory", type = DatabaseOperation.CLEAN_INSERT)
    @ExpectedDatabase(value = "EdaMaster5545003Tests.xlsx:001_ex", connection = "lgfemDataSourceFactory", assertionMode = DatabaseAssertionMode.NON_STRICT_UNORDERED)
    public void test5545003_001() throws Exception {
        JsonObject reqJs  = new JsonObject();
        reqJs.addProperty("edaNo", "128WW8988211");//EDA编号
        reqJs.addProperty("optTeam", "XE");//操作者团队
        reqJs.addProperty("optUserName", "dzl");//操作者
        String responseContent = this.sendRequest(URL, reqJs);

        Type type = new TypeToken<ResDataBean>() {}.getType();
        ResDataBean resDataBean = gson.fromJson(responseContent, type);
        Assert.assertEquals(ReCodeConsts.SUCCESS, resDataBean.getReCode());
        Assert.assertEquals(HintConsts.SYSTEM_SUCC, resDataBean.getReInfo());
    }


    /**
     * bean设置值
     * @throws Exception
     */
    @Test
    @DatabaseSetup(value = "EdaMaster5545003Tests.xlsx:001", connection = "lgfemDataSourceFactory", type = DatabaseOperation.CLEAN_INSERT)
    @ExpectedDatabase(value = "EdaMaster5545003Tests.xlsx:002_ex", connection = "lgfemDataSourceFactory", assertionMode = DatabaseAssertionMode.NON_STRICT_UNORDERED)
    public void test5545003_002() throws Exception {
        JsonObject reqJs  = new JsonObject();
        reqJs.addProperty("edaNo", "128WW8988211");//EDA编号
        reqJs.addProperty("optTeam", "XE");//操作者团队
        reqJs.addProperty("optUserName", "dzl");//操作者
        reqJs.addProperty("coverageBaseline", "12");//EDA编号
        reqJs.addProperty("maintenanceTeam", "mm");//维护团队
        String responseContent = this.sendRequest(URL, reqJs);

        Type type = new TypeToken<ResDataBean>() {}.getType();
        ResDataBean resDataBean = gson.fromJson(responseContent, type);
        Assert.assertEquals(ReCodeConsts.SUCCESS, resDataBean.getReCode());
        Assert.assertEquals(HintConsts.SYSTEM_SUCC, resDataBean.getReInfo());
    }
}
