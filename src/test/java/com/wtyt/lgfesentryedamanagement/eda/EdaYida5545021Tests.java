package com.wtyt.lgfesentryedamanagement.eda;

import com.aliyun.dingtalkyida_1_0.models.*;
import com.aliyun.dingtalkyida_1_0.Client;
import com.github.springtestdbunit.annotation.DatabaseOperation;
import com.github.springtestdbunit.annotation.DatabaseSetup;
import com.github.springtestdbunit.annotation.ExpectedDatabase;
import com.github.springtestdbunit.assertion.DatabaseAssertionMode;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.wtyt.commons.base.test.annotation.MvcExpectedDatabase;
import com.wtyt.commons.base.test.consts.AssertionMode;
import com.wtyt.commons.base.test.toolkit.MockToolkit;
import com.wtyt.lg.commons.bean.ResDataBean;
import com.wtyt.lgfesentryedamanagement.ApplicationXlsBaseTests;
import com.wtyt.lgfesentryedamanagement.bkc.bean.response.Req5545020OBean;
import com.wtyt.lgfesentryedamanagement.pub.toolkits.JsonToolkit;
import mockit.Mock;
import mockit.MockUp;
import org.junit.Assert;
import org.junit.Test;

import java.lang.reflect.Type;

public class EdaYida5545021Tests extends ApplicationXlsBaseTests {

    private static final String url = "/yida/syncYidaFormDataByInstId";
    //mock 这个类
    private MockUp<Client> clientMock;

    public class result extends ResDataBean<Req5545020OBean> {
        private static final long serialVersionUID = 1L;
    }

    /*
    输出hello world
     */
    @Test
    public void Test5545021_001() throws Exception {
        System.out.println("hello, world!");
    }

    /*
    校验为空的场景
     */
    @Test
    public void Test5545021_002() throws Exception {
        JsonObject result5545021 = new JsonObject();
        result5545021.addProperty("optType", "");
        result5545021.addProperty("formUuid", "");
        result5545021.addProperty("formInstanceId", "");
        String responseContent = this.sendRequest(url, result5545021.toString());

        Type type = new TypeToken<ResDataBean>() {
        }.getType();
        ResDataBean resDataBean = gson.fromJson(responseContent, type);
        Assert.assertEquals("1", resDataBean.getReCode());
    }

    /*
    校验首次新建表单填写数据，同步到线下数据库是否正确
 */
    @DatabaseSetup(value = "EdaYida5545021Tests.xlsx:001", connection = "lgfemDataSourceFactory", type = DatabaseOperation.CLEAN_INSERT)
    @ExpectedDatabase(value = "EdaYida5545021Tests.xlsx:001EX", connection = "lgfemDataSourceFactory", assertionMode = DatabaseAssertionMode.NON_STRICT_UNORDERED)
    @Test
    public void Test5545021_003() throws Exception {

        MockToolkit.getInstance().addMock("-1076", f -> {
            JsonObject result1076 = new JsonObject();
            JsonObject result1076_001 = new JsonObject();
//        JsonObject result1076_002 = new JsonObject();
            result1076_001.addProperty("access_token", "78_6ElDpKtDTGYlqM-jO92cZgRnwLi0u6_nfUAWSTc-lDA12YM8pd6tktO1hs65UmK-RJmEiXeI4m0u6eA6CNEk-NGFRfp4ZjoL3OgxRhhcMR9HFt1IntYtD7hJmncQTBhAIAFIQ");
//        result1076_001.addProperty("req_msg","成功");
//        result1076_001.addProperty("req_code","200");
            result1076.add("result", result1076_001);
//        result1076.add("data",result1076_002);
            logger.info("mock 1076的数据为：{}", new Gson().toJson(result1076));
            return result1076_001;
        });

        clientMock = new MockUp<Client>() {
            @Mock
            public BatchGetFormDataByIdListResponse batchGetFormDataByIdListWithOptions(BatchGetFormDataByIdListRequest request, BatchGetFormDataByIdListHeaders headers, com.aliyun.teautil.models.RuntimeOptions runtime) throws Exception {
                BatchGetFormDataByIdListResponse response = new BatchGetFormDataByIdListResponse();
                //调用方法
                String result = mockdata_001();
                System.out.println("mock batchGetFormDataByIdListWithOptions001 body 数据： " + result);
                BatchGetFormDataByIdListResponseBody body = JsonToolkit.jsonToObject(result, BatchGetFormDataByIdListResponseBody.class);
                response.setStatusCode(200);
                response.setBody(body);
                return response;
            }
            @Mock
            public GetFieldDefByUuidResponse getFieldDefByUuidWithOptions(GetFieldDefByUuidRequest request, GetFieldDefByUuidHeaders headers, com.aliyun.teautil.models.RuntimeOptions runtime) throws Exception {
                GetFieldDefByUuidResponse response = new GetFieldDefByUuidResponse();
                String result = mockdata_002();
                System.out.println("mock  getFieldDefByUuidWithOptions002 body 数据： " + result);
                GetFieldDefByUuidResponseBody body = JsonToolkit.jsonToObject(result, GetFieldDefByUuidResponseBody.class);
                response.setStatusCode(200);
                response.setBody(body);
                return response;
            }
        };


        JsonObject result5545021 = new JsonObject();
        result5545021.addProperty("optType", "1");
        result5545021.addProperty("formUuid", "form_082af95a211c4835bbbfe9db723eae81vcl0");
        result5545021.addProperty("formInstanceId", "FINST-J8766S91O2UYN87ZX3XOF1MY8MBA2912BSV0L24");
        String responseContent = this.sendRequest(url, result5545021.toString());

        Type type = new TypeToken<ResDataBean>() {
        }.getType();
        ResDataBean resDataBean = gson.fromJson(responseContent, type);
        Assert.assertEquals("0", resDataBean.getReCode());
    }

    public String mockdata_001() {
        JsonObject result = new JsonObject();
        JsonArray resultlist = new JsonArray();
        JsonObject resultList_001 = new JsonObject();
        //JsonObject resultList_001_1 = new JsonObject();
        resultList_001.addProperty("createTimeGMT", "2022-03-04T17:12Z");
        resultList_001.addProperty("sequence", "0900");

        JsonObject modifyUser = new JsonObject();
        modifyUser.addProperty("userId", "ding173982232112232");
        resultList_001.add("modifyUser", modifyUser);
        JsonObject name = new JsonObject();
        name.addProperty("nameInChinese", "张三");
        name.addProperty("nameInEnglish", "ZhangSan");
        modifyUser.add("name", name);

        //resultList_001.addProperty("modifyUser","");
        resultList_001.addProperty("sequence", "IMPORT-388664B1BAUVB3AYZE1RIUE88TDM1QI9WIOWK2");
        resultList_001.addProperty("creatorUserId", "ding12345");
        resultList_001.addProperty("formUuid", "form_082af95a211c4835bbbfe9db723eae81vcl0");
        resultList_001.addProperty("serialNumber", "YIDA909202202250027");
        resultList_001.addProperty("modifiedTimeGMT", "2021-05-01T12:34Z");
        resultList_001.addProperty("modifier", "manager123");

        JsonObject originator = new JsonObject();
        originator.addProperty("userId", "ding173982232112232");
        resultList_001.add("originator", originator);
        JsonObject name_1 = new JsonObject();
        name_1.addProperty("nameInChinese", "张三");
        name_1.addProperty("nameInEnglish", "ZhangSan");
        originator.add("name", name_1);

        resultList_001.addProperty("formInstanceId", "FINST-J8766S91O2UYN87ZX3XOF1MY8MBA2912BSV0L24");
        resultList_001.addProperty("id", 12345);
        resultList_001.addProperty("title", "李四发起的请购单");
        resultList_001.addProperty("version", 2);
        resultList_001.addProperty("instanceValue", "[{\"fieldData\":{\"text\":\"选项一\",\"value\":\"one\"},\"fieldId\":\"textField_laq7xxx\"}]");
        resultlist.add(resultList_001);
        result.add("result", resultlist);
        return new Gson().toJson(result);
    }

    public String mockdata_002() {
        JsonObject result_id = new JsonObject();
        result_id.addProperty("success", true);
        JsonArray resultlist_id = new JsonArray();
        JsonObject resultList_id_001 = new JsonObject();
        resultList_id_001.addProperty("componentName", "RadioField");
        resultList_id_001.addProperty("fieldId", "textField_laq7xxx");
        resultList_id_001.addProperty("behavior", "NORMAL");
        resultList_id_001.addProperty("label", "选项一");
        //resultList_id_001.addProperty("props", "223");
        //resultList_id_001.addProperty("children", "单行文本");
        resultlist_id.add(resultList_id_001);
        result_id.add("result", resultlist_id);
        return new Gson().toJson(result_id);
    }
}


