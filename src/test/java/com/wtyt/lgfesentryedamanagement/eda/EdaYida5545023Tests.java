package com.wtyt.lgfesentryedamanagement.eda;

import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.wtyt.commons.base.test.toolkit.MockToolkit;
import com.wtyt.lg.commons.bean.ResDataBean;
import com.wtyt.lgfesentryedamanagement.ApplicationXlsBaseTests;
import mockit.Mock;
import org.junit.Assert;
import org.junit.Test;

import java.lang.reflect.Type;

public class EdaYida5545023Tests extends ApplicationXlsBaseTests {

    String URL = "/yida/syncYidaToTeambition";

    @Test
    public void test5545023_001() throws Exception {
        JsonObject result5545023 = new JsonObject();
        result5545023.addProperty("taskId", "");
        result5545023.addProperty("operatorId", "");
        result5545023.addProperty("archReviewStatus", "");
        String responseContent = this.sendRequest(URL, result5545023.toString());

        Type type = new TypeToken<ResDataBean>() {
        }.getType();
        ResDataBean resDataBean = gson.fromJson(responseContent, type);
        Assert.assertEquals("1", resDataBean.getReCode());
    }

    @Test
    public void test5545023_002() throws Exception {
        MockToolkit.getInstance().addMock("8000535",f ->{
        JsonObject result8000535 = new JsonObject();
        result8000535.addProperty("reCode","0");
        result8000535.addProperty("reInfo","请求成功");
        return  result8000535;
        });


        JsonObject result5545023 = new JsonObject();
        result5545023.addProperty("taskId", "2324");
        result5545023.addProperty("operatorId", "3434");
        result5545023.addProperty("archReviewStatus", "待评审");
        String responseContent = this.sendRequest(URL, result5545023.toString());

        Type type = new TypeToken<ResDataBean>() {
        }.getType();
        ResDataBean resDataBean = gson.fromJson(responseContent, type);
        Assert.assertEquals("0", resDataBean.getReCode());

    }
}