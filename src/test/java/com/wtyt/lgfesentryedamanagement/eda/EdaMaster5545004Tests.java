package com.wtyt.lgfesentryedamanagement.eda;

import com.github.springtestdbunit.annotation.DatabaseOperation;
import com.github.springtestdbunit.annotation.DatabaseSetup;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.wtyt.commons.base.test.annotation.MvcExpectedDatabase;
import com.wtyt.commons.base.test.annotation.TestInterface;
import com.wtyt.lgfesentryedamanagement.ApplicationXlsBaseTests;
import com.wtyt.money.commons.bean.ResDataBean;
import com.wtyt.money.commons.consts.HintConsts;
import com.wtyt.money.commons.consts.ReCodeConsts;
import org.junit.Assert;
import org.junit.Test;

import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @date 2024/2/1 13:37
 * @vesion 1.0
 * @desc
 */
@TestInterface(sid = "5545004")
@DatabaseSetup(value = "EdaMaster5545004Tests.xlsx:clean", connection = "lgfemDataSourceFactory", type = DatabaseOperation.CLEAN_INSERT)
public class EdaMaster5545004Tests extends ApplicationXlsBaseTests {

    private static final String URL = "/edaMaster/list";

    /**
     * 验证正常查询及排序
     * @throws Exception
     */
    @Test
    @DatabaseSetup(value = "EdaMaster5545004Tests.xlsx:001", connection = "lgfemDataSourceFactory", type = DatabaseOperation.CLEAN_INSERT)
    @MvcExpectedDatabase(value = "EdaMaster5545004Tests.xlsx:001_mex")
    public void test5545004_001() throws Exception {
        JsonObject reqJs = new JsonObject();
        reqJs.addProperty("pageNumber","1");
        reqJs.addProperty("pageSize","10");

        String responseContent = this.sendRequest(URL, reqJs);
        Type type = new TypeToken<ResDataBean>() {
        }.getType();
        ResDataBean resDataBean = gson.fromJson(responseContent, type);
        Assert.assertEquals(ReCodeConsts.SUCCESS, resDataBean.getReCode());
        Assert.assertEquals(HintConsts.SYSTEM_SUCC, resDataBean.getReInfo());
        this.verifyMvcActualData(resDataBean.getResult());
    }


    /**
     * 验证分页功能及排序
     * @throws Exception
     */
    @Test
    @DatabaseSetup(value = "EdaMaster5545004Tests.xlsx:001", connection = "lgfemDataSourceFactory", type = DatabaseOperation.CLEAN_INSERT)
    @MvcExpectedDatabase(value = "EdaMaster5545004Tests.xlsx:002_mex")
    public void test5545004_002() throws Exception {
        JsonObject reqJs = new JsonObject();
        reqJs.addProperty("pageNumber","2");
        reqJs.addProperty("pageSize","1");

        String responseContent = this.sendRequest(URL, reqJs);
        Type type = new TypeToken<ResDataBean>() {
        }.getType();
        ResDataBean resDataBean = gson.fromJson(responseContent, type);
        Assert.assertEquals(ReCodeConsts.SUCCESS, resDataBean.getReCode());
        Assert.assertEquals(HintConsts.SYSTEM_SUCC, resDataBean.getReInfo());
        this.verifyMvcActualData(resDataBean.getResult());
    }

    /**
     * 验证模糊查询
     * @throws Exception
     */
    @Test
    @DatabaseSetup(value = "EdaMaster5545004Tests.xlsx:001", connection = "lgfemDataSourceFactory", type = DatabaseOperation.CLEAN_INSERT)
    @MvcExpectedDatabase(value = "EdaMaster5545004Tests.xlsx:003_mex")
    public void test5545004_003() throws Exception {
        JsonObject reqJs = new JsonObject();
        reqJs.addProperty("pageNumber","1");
        reqJs.addProperty("pageSize","10");
        reqJs.addProperty("edaNo","88213");

        String responseContent = this.sendRequest(URL, reqJs);
        Type type = new TypeToken<ResDataBean>() {
        }.getType();
        ResDataBean resDataBean = gson.fromJson(responseContent, type);
        Assert.assertEquals(ReCodeConsts.SUCCESS, resDataBean.getReCode());
        Assert.assertEquals(HintConsts.SYSTEM_SUCC, resDataBean.getReInfo());
        this.verifyMvcActualData(resDataBean.getResult());
    }

}
