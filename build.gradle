buildscript {
    ext {
        springBootVersion = '2.0.9.RELEASE'
    }
    repositories {
        mavenCentral()
        maven {
            url 'https://repo.log56.com/nexus3/repository/maven-public/'
            credentials {
                username "log56repo"
                password "M@GhpXMAa8fmQfWj"
            }
        }
    }
    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
        classpath("org.hidetake:gradle-ssh-plugin:2.9.0")
        classpath("org.sonarsource.scanner.gradle:sonarqube-gradle-plugin:2.6.2")
        //解决命令太长报错
        classpath "gradle.plugin.ua.eshepelyuk:ManifestClasspath:1.0.0"
    }
}

apply plugin: 'java'
apply plugin: 'eclipse'
apply plugin: 'org.hidetake.ssh'
apply plugin: 'jacoco'
apply plugin: 'org.sonarqube'
apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'
apply plugin: "ua.eshepelyuk.ManifestClasspath"

group = 'com.wtyt'
version = '0.0.1'
sourceCompatibility = '1.8'

repositories {
    mavenCentral()
    maven {
        url 'https://repo.log56.com/nexus3/repository/maven-public/'
        credentials {
            username "log56repo"
            password "M@GhpXMAa8fmQfWj"
        }
    }
}

configurations {
    compile.exclude group: 'ch.qos.logback', module: 'logback-classic'
    compile.exclude module: 'log4j-to-slf4j'
    compile.exclude group: 'com.vaadin.external.google'
}

ext {
    springCloudVersion = 'Finchley.SR1'
    xstreamVersion = '1.4.15'
    jacksonCoreVersion = '2.9.10'
    jacksonDatabindVersion = '********'
    jacksonAnnotationsVersion = '2.9.10'
}
processResources{
    from('src/main/java'){
        include '**/*Mapper.xml'
    }
}

jacocoTestReport {
    reports {
        html.enabled true
        html.destination file("$buildDir/reports/tests/jacoco")
    }
    afterEvaluate {
        classDirectories = files(classDirectories.files.collect {
            fileTree(dir: it, exclude: ['com/wtyt/**/commons/toolkit/**'])
        })
    }
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-jdbc'
    implementation 'com.wtyt:m-security-wrapper:2.1.4'
    implementation('com.oceanbase:oceanbase-client:2.4.13')
    implementation 'com.wtyt.jars:lg-trace:RC1.2.5.2'
    implementation('org.springframework.boot:spring-boot-starter-log4j2')
    implementation 'mysql:mysql-connector-java:8.0.25'
    implementation('org.springframework.cloud:spring-cloud-starter-netflix-eureka-client')
    implementation('org.mybatis.spring.boot:mybatis-spring-boot-starter:1.3.2')
    //需要分页时引入
    implementation('com.github.pagehelper:pagehelper-spring-boot-starter:1.2.7')
    implementation('org.springframework.boot:spring-boot-starter-web')
    implementation('com.ctrip.framework.apollo:apollo-client')
    implementation('com.wtyt.jars:kydd-apollo-autoconfig:RC1.2.7.4')

    //统一json
    implementation("com.fasterxml.jackson.core:jackson-annotations:${jacksonAnnotationsVersion}")
    implementation("com.fasterxml.jackson.core:jackson-core:${jacksonCoreVersion}")
    implementation("com.fasterxml.jackson.core:jackson-databind:${jacksonDatabindVersion}")
    //漏洞强制升级
    implementation("com.thoughtworks.xstream:xstream:${xstreamVersion}")
    //compile('com.wtyt.jars:dataphin-jdbc-jar:v1.0.0');
    compile('com.aliyun.odps:odps-sdk-core:0.36.4-public');
    implementation 'org.elasticsearch.client:elasticsearch-rest-high-level-client:7.6.2'
    implementation 'org.elasticsearch.client:elasticsearch-rest-client:7.16.1'
    implementation 'org.elasticsearch:elasticsearch:7.6.2'
    implementation 'com.wtyt.jars:lg-hippo4jclient-starter:RC1.0.3'
    implementation 'org.json:json:20180130'
    //拼音
    implementation 'com.belerweb:pinyin4j:2.5.1'
    //brd埋点
    compile('com.wtyt.lg:bury-point-collector:1.3.0');
    //sql打印工具
    implementation('com.wtyt:lg-mybatis-toolkits:1.0.1')
    implementation('com.alibaba:druid')
    implementation('org.skyscreamer:jsonassert:1.5.0')
    implementation("com.wtyt:lg-common-toolkits:2.0.8") {
        exclude group: 'com.alibaba', module: 'druid'
    }
    // 金融网关及社区网关调用
    implementation 'com.wtyt:m-gateway-sdk:0.1.4'
    // 钉钉OpenApi调用，包含宜搭OpenApi
    implementation 'com.aliyun:dingtalk:2.0.14'
    //三方工具包
    implementation('cn.hutool:hutool-all:5.8.12')
    //生成雪花算法ID
    compile('com.wtyt.jars:uid-generator-wtyt:RC2.0.1')
    compile('org.apache.commons:commons-text:1.10.0')

    compileOnly('org.projectlombok:lombok:1.18.4')
    annotationProcessor('org.projectlombok:lombok:1.18.4')
    testCompileOnly('org.projectlombok:lombok:1.18.4')
    testAnnotationProcessor('org.projectlombok:lombok:1.18.4')

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testCompile('com.wtyt.money:m-mssbase-test:+')

}
dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
    }
}

processResources {
    from('src/main/java') {
        include '**/*Mapper.xml'
    }
}

compileJava {
    sourceCompatibility = 1.8
    targetCompatibility = 1.8
    options.encoding = 'UTF-8'
}


test {
    systemProperties 'BUILD_FLAG' : '1'
    exclude 'com/wtyt/lgfesentryedamanagement/testDev/**'
}

jacocoTestReport {
    reports {
        html.enabled true
        html.destination file("$buildDir/reports/tests/jacoco")
    }
    afterEvaluate {
        classDirectories = files(classDirectories.files.collect {
            //排除覆盖率扫描
            fileTree(dir: it, exclude: ['com/wtyt/**/commons/toolkit/**','com/wtyt/commons/**'])
        })
    }
}


task(buildResult, dependsOn: 'classes', type: JavaExec){
    main = 'com.wtyt.commons.base.test.BuildResult'
    classpath = sourceSets.test.runtimeClasspath
    args "LFE"
}


sourceSets {
    main {
        java {
            srcDirs = ['src/main/java']
        }
        resources {
            srcDirs = ['src/main/resources','src/main/java']
        }
    }
    test {
        java {
            srcDirs = ['src/test/java']
        }
        resources {
            srcDirs = ['src/test/resources']
        }
    }
}


sonarqube {
    properties {
        property "sonar.projectVersion", System.getenv('CI_COMMIT_REF_NAME') + "_" + new Date().format('yyyyMMddHHmmss')
    }
}