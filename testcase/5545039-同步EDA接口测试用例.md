# 5545039-雄鹰新增/修改操作链路同步EDA接口测试用例

## 接口功能描述

**接口功能描述**：5545039-雄鹰新增/修改操作链路同步EDA

- 校验入参optLinkId、optLinkName、maintenanceTeam、edaStatus不能为空
- 根据操作链路ID查询EDA主表数据，判断是新增还是修改操作
- 新增操作：创建EDA主表记录、创建或查找文件夹、创建EDA文件记录
- 修改操作：更新EDA主表记录，如果操作链路名称变更则更新文件名称
- 返回EDA操作链路文件地址URL

**涉及表结构**：
- lgfem.T_BURYPOINTS_EDA_MASTER：EDA实体主表
- lgfem.t_drawio_file：draio文件列表
- lgfem.t_drawio_folder：drawio文件夹管理列表

## 测试用例脑图

```mermaid
graph LR
A["5545039-雄鹰新增/修改操作链路同步EDA接口"]
A --> B["正常情况"]
B --> C["TC01-所有必填字段都传且非必填字段都不传"]
B --> D["TC02-所有必填和非必填字段都传"]
B --> E["TC03-新增操作链路场景"]
B --> F["TC04-修改操作链路场景"]
B --> G["TC05-edaStatus枚举值全覆盖"]
A --> H["边界条件"]
H --> I["TC06-操作链路名称包含特殊字符"]
H --> J["TC07-字段长度边界测试"]
A --> K["异常情况"]
K --> L["TC08-必填字段为空校验"]
K --> M["TC09-edaStatus不在枚举范围内"]
K --> N["TC10-数据库操作异常"]
```

## 详细测试用例

### 正常情况

#### TC01-所有必填字段都传且非必填字段都不传
- **用例描述**：传入所有必填字段（optLinkId、optLinkName、maintenanceTeam、edaStatus），非必填字段不传
- **测试步骤**：
    1. 准备T_BURYPOINTS_EDA_MASTER、t_drawio_file和t_drawio_folder表数据
    2. 调用5545039接口，传入必填参数：optLinkId="940792374873627242"，optLinkName="司机申请支付预付款"，maintenanceTeam="DO"，edaStatus="1"
- **预期结果**：操作成功，返回edaUrl链接地址

#### TC02-所有必填和非必填字段都传
- **用例描述**：传入所有必填字段和非必填字段maintenanceUi
- **测试步骤**：
    1. 准备T_BURYPOINTS_EDA_MASTER、t_drawio_file和t_drawio_folder表数据
    2. 调用5545039接口，传入所有参数：optLinkId="940792374873627242"，optLinkName="司机申请支付预付款"，maintenanceTeam="DO"，maintenanceUi="张三"，edaStatus="1"
- **预期结果**：操作成功，返回edaUrl链接地址

#### TC03-新增操作链路场景
- **用例描述**：传入数据库中不存在的操作链路ID，验证新增逻辑
- **测试步骤**：
    1. 准备T_BURYPOINTS_EDA_MASTER、t_drawio_file和t_drawio_folder表数据，确保optLinkId不存在
    2. 调用5545039接口，传入新的optLinkId="NEW123456789"，optLinkName="新操作链路"，maintenanceTeam="DO"，edaStatus="1"
- **预期结果**：新增成功，创建EDA主表记录和文件记录，返回edaUrl链接地址

#### TC04-修改操作链路场景
- **用例描述**：传入数据库中已存在的操作链路ID，验证修改逻辑
- **测试步骤**：
    1. 准备T_BURYPOINTS_EDA_MASTER、t_drawio_file和t_drawio_folder表数据，包含已存在的optLinkId
    2. 调用5545039接口，传入已存在的optLinkId="940792374873627242"，修改optLinkName="修改后的操作链路名称"，maintenanceTeam="DO"，edaStatus="2"
- **预期结果**：修改成功，更新EDA主表记录，如果名称变更则更新文件名称，返回edaUrl链接地址

#### TC05-edaStatus枚举值全覆盖
- **用例描述**：验证edaStatus字段的所有有效枚举值
- **测试步骤**：
    1. 准备T_BURYPOINTS_EDA_MASTER、t_drawio_file和t_drawio_folder表数据
    2. 分别调用5545039接口，edaStatus传入"0"、"1"、"2"、"3"
- **预期结果**：所有枚举值都能正常处理，操作成功

### 边界条件

#### TC06-操作链路名称包含特殊字符
- **用例描述**：验证操作链路名称包含特殊字符的处理
- **测试步骤**：
    1. 准备T_BURYPOINTS_EDA_MASTER、t_drawio_file和t_drawio_folder表数据
    2. 调用5545039接口，optLinkName传入包含特殊字符的名称"测试&操作<链路>名称"
- **预期结果**：能正常处理特殊字符，操作成功

#### TC07-字段长度边界测试
- **用例描述**：验证字段长度边界值处理
- **测试步骤**：
    1. 准备T_BURYPOINTS_EDA_MASTER、t_drawio_file和t_drawio_folder表数据
    2. 调用5545039接口，传入最大长度的字段值（optLinkName=64字符，maintenanceTeam=14字符等）
- **预期结果**：在字段长度限制内能正常处理，操作成功

### 异常情况

#### TC08-必填字段为空校验
- **用例描述**：验证所有必填字段的空值校验
- **测试步骤**：
    1. 分别调用5545039接口，每次将一个必填字段设为空或null
    2. optLinkId为空、optLinkName为空、maintenanceTeam为空、edaStatus为空
- **预期结果**：提示对应字段不能为空的异常信息

#### TC09-edaStatus不在枚举范围内
- **用例描述**：验证edaStatus字段值不在有效范围内的处理
- **测试步骤**：
    1. 准备T_BURYPOINTS_EDA_MASTER、t_drawio_file和t_drawio_folder表数据
    2. 调用5545039接口，edaStatus传入无效值"5"或"abc"
- **预期结果**：提示edaStatus字段值无效的异常信息

#### TC10-数据库操作异常
- **用例描述**：模拟数据库操作异常情况
- **测试步骤**：
    1. 准备T_BURYPOINTS_EDA_MASTER、t_drawio_file和t_drawio_folder表数据
    2. 模拟数据库连接异常或表不存在等情况
    3. 调用5545039接口
- **预期结果**：返回相应的数据库操作异常信息

## 测试数据准备

### T_BURYPOINTS_EDA_MASTER表测试数据

```sql
-- 已存在的操作链路数据
INSERT INTO T_BURYPOINTS_EDA_MASTER (
    BURYPOINTS_EDA_MASTER_ID, EDA_NO, EDA_NAME, MAINTENANCE_TEAM, 
    EDA_STATUS, OPT_LINK_ID, IS_DEL, EDA_TYPE
) VALUES (
    1, '940792374873627242', '司机申请支付预付款', 'DO', 
    1, '940792374873627242', 0, 0
);
```

### t_drawio_folder表测试数据

```sql
-- 插入drawio文件夹测试数据
INSERT INTO t_drawio_folder (
    DRAWIO_FOLDER_ID, FOLDER_NAME, IS_DEL, 
    CREATED_TIME, LAST_MODIFIED_TIME, NOTE
) VALUES (
    1, 'TEST_FOLDER', 0, 
    NOW(), NOW(), '测试文件夹'
);
```

### t_drawio_file表测试数据

```sql
-- 插入drawio文件测试数据
INSERT INTO t_drawio_file (
    DRAWIO_FILE_ID, FILE_NAME, DRAWIO_FOLDER_ID, 
    OPT_LINK_ID, IS_DEL, CREATED_TIME, LAST_MODIFIED_TIME, 
    STATUS, STATUS_MODIFY_TIME, NOTE
) VALUES (
    1, 'TEST_FILE.drawio', 1, 
    'TEST_OPT_LINK_001', 0, NOW(), NOW(), 
    0, NOW(), '测试文件'
);
```

## 注意事项

1. 测试前需要确保数据库表结构正确
2. 需要mock相关的文件操作和URL生成逻辑
3. 注意验证返回的edaUrl格式是否正确
4. 测试过程中需要验证数据库记录的正确性
5. 异常场景需要验证异常信息的准确性