## 固定信息

## 项目应用ID

- `b-org-ai-app`: 165f58dedc747b4390a6c7fcf79790e2bdbcb737
- `b-org-ai-mq-job`: 5c2efc254d8bf99285c2f81dc4c3b66bb32ebe3c

## 项目数据库配置

* dbType: obmysql
* schema：lgfem

## 项目规范：

    - 使用JDK1.8版本的API
	- 添加完整注释，如属性字段注释，方法注释
	- service层输出关键日志
	- 入参判断空时，空字符串也按空处理
	- 业务抛出异常，请使用UnifiedBusinessException类
	- 可以使用lombok，使用@Setter、@Getter，不要使用@Data
    - 禁止使用swagger
    - 使用@Attribute注解，指定接口的sid
	- domain层不可以引用app层中的类
    - 基础bean属性全部使用String类型
    - 禁止使用Map作为数据传输
    - 优先复用已经存在的类和方法

---